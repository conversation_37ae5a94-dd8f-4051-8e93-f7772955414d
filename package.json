{"name": "Slidevs-on-Vercel", "version": "1.1.0", "description": "A system for managing multiple Slidev presentations in one repository", "type": "module", "scripts": {"build": "npm run build:slidev-system && npm run build:slidev-system-zh && npm run build:slidev-system-ja && npm run build:index", "build:slidev-system": "cd slides/slidev-system/src && npm run build", "build:slidev-system-zh": "cd slides/slidev-system-zh/src && npm run build", "build:slidev-system-ja": "cd slides/slidev-system-ja/src && npm run build", "build:index": "node scripts/build-index.js", "dev:slidev-system": "cd slides/slidev-system/src && npm run dev", "dev:slidev-system-zh": "cd slides/slidev-system-zh/src && npm run dev", "dev:slidev-system-ja": "cd slides/slidev-system-ja/src && npm run dev", "create-slide": "node scripts/create-slide.js", "install:all": "npm install && cd slides/slidev-system/src && npm install && cd ../../slidev-system-zh/src && npm install && cd ../../slidev-system-ja/src && npm install"}, "keywords": ["slidev", "presentation", "vercel", "vue", "markdown", "slides", "pnpm-workspace", "multi-presentation", "i18n", "multilingual"], "author": "Open Source Community", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/wwlapaki310/Slidevs-on-Vercel"}}