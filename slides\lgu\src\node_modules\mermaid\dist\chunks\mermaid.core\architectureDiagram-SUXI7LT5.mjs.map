{"version": 3, "sources": ["../../../src/diagrams/architecture/architectureParser.ts", "../../../src/diagrams/architecture/architectureTypes.ts", "../../../src/diagrams/architecture/architectureDb.ts", "../../../src/diagrams/architecture/architectureStyles.ts", "../../../src/diagrams/architecture/architectureRenderer.ts", "../../../src/diagrams/architecture/architectureIcons.ts", "../../../src/diagrams/architecture/svgDraw.ts", "../../../src/diagrams/architecture/architectureDiagram.ts"], "sourcesContent": ["import type { Architecture } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport { log } from '../../logger.js';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport type { ArchitectureDB } from './architectureTypes.js';\nimport { db } from './architectureDb.js';\n\nconst populateDb = (ast: Architecture, db: ArchitectureDB) => {\n  populateCommonDb(ast, db);\n  ast.groups.map(db.addGroup);\n  ast.services.map((service) => db.addService({ ...service, type: 'service' }));\n  ast.junctions.map((service) => db.addJunction({ ...service, type: 'junction' }));\n  // @ts-ignore TODO our parser guarantees the type is L/R/T/B and not string. How to change to union type?\n  ast.edges.map(db.addEdge);\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: Architecture = await parse('architecture', input);\n    log.debug(ast);\n    populateDb(ast, db);\n  },\n};\n", "import type { DiagramDBBase } from '../../diagram-api/types.js';\nimport type { ArchitectureDiagramConfig } from '../../config.type.js';\nimport type { D3Element } from '../../types.js';\nimport type cytoscape from 'cytoscape';\n\n/*=======================================*\\\n|       Architecture Diagram Types        |\n\\*=======================================*/\n\nexport type ArchitectureAlignment = 'vertical' | 'horizontal' | 'bend';\n\nexport type ArchitectureDirection = 'L' | 'R' | 'T' | 'B';\nexport type ArchitectureDirectionX = Extract<ArchitectureDirection, 'L' | 'R'>;\nexport type ArchitectureDirectionY = Extract<ArchitectureDirection, 'T' | 'B'>;\n\n/**\n * Contains LL, RR, TT, BB which are impossible connections\n */\nexport type InvalidArchitectureDirectionPair = `${ArchitectureDirection}${ArchitectureDirection}`;\nexport type ArchitectureDirectionPair = Exclude<\n  InvalidArchitectureDirectionPair,\n  'LL' | 'RR' | 'TT' | 'BB'\n>;\nexport type ArchitectureDirectionPairXY = Exclude<\n  InvalidArchitectureDirectionPair,\n  'LL' | 'RR' | 'TT' | 'BB' | 'LR' | 'RL' | 'TB' | 'BT'\n>;\n\nexport const ArchitectureDirectionName = {\n  L: 'left',\n  R: 'right',\n  T: 'top',\n  B: 'bottom',\n} as const;\n\nexport const ArchitectureDirectionArrow = {\n  L: (scale: number) => `${scale},${scale / 2} 0,${scale} 0,0`,\n  R: (scale: number) => `0,${scale / 2} ${scale},0 ${scale},${scale}`,\n  T: (scale: number) => `0,0 ${scale},0 ${scale / 2},${scale}`,\n  B: (scale: number) => `${scale / 2},0 ${scale},${scale} 0,${scale}`,\n} as const;\n\nexport const ArchitectureDirectionArrowShift = {\n  L: (orig: number, arrowSize: number) => orig - arrowSize + 2,\n  R: (orig: number, _arrowSize: number) => orig - 2,\n  T: (orig: number, arrowSize: number) => orig - arrowSize + 2,\n  B: (orig: number, _arrowSize: number) => orig - 2,\n} as const;\n\nexport const getOppositeArchitectureDirection = function (\n  x: ArchitectureDirection\n): ArchitectureDirection {\n  if (isArchitectureDirectionX(x)) {\n    return x === 'L' ? 'R' : 'L';\n  } else {\n    return x === 'T' ? 'B' : 'T';\n  }\n};\n\nexport const isArchitectureDirection = function (x: unknown): x is ArchitectureDirection {\n  const temp = x as ArchitectureDirection;\n  return temp === 'L' || temp === 'R' || temp === 'T' || temp === 'B';\n};\n\nexport const isArchitectureDirectionX = function (\n  x: ArchitectureDirection\n): x is ArchitectureDirectionX {\n  const temp = x as ArchitectureDirectionX;\n  return temp === 'L' || temp === 'R';\n};\n\nexport const isArchitectureDirectionY = function (\n  x: ArchitectureDirection\n): x is ArchitectureDirectionY {\n  const temp = x as ArchitectureDirectionY;\n  return temp === 'T' || temp === 'B';\n};\n\nexport const isArchitectureDirectionXY = function (\n  a: ArchitectureDirection,\n  b: ArchitectureDirection\n) {\n  const aX_bY = isArchitectureDirectionX(a) && isArchitectureDirectionY(b);\n  const aY_bX = isArchitectureDirectionY(a) && isArchitectureDirectionX(b);\n  return aX_bY || aY_bX;\n};\n\nexport const isArchitecturePairXY = function (\n  pair: ArchitectureDirectionPair\n): pair is ArchitectureDirectionPairXY {\n  const lhs = pair[0] as ArchitectureDirection;\n  const rhs = pair[1] as ArchitectureDirection;\n  const aX_bY = isArchitectureDirectionX(lhs) && isArchitectureDirectionY(rhs);\n  const aY_bX = isArchitectureDirectionY(lhs) && isArchitectureDirectionX(rhs);\n  return aX_bY || aY_bX;\n};\n\n/**\n * Verifies that the architecture direction pair does not contain an invalid match (LL, RR, TT, BB)\n * @param x - architecture direction pair which could potentially be invalid\n * @returns true if the pair is not LL, RR, TT, or BB\n */\nexport const isValidArchitectureDirectionPair = function (\n  x: InvalidArchitectureDirectionPair\n): x is ArchitectureDirectionPair {\n  return x !== 'LL' && x !== 'RR' && x !== 'TT' && x !== 'BB';\n};\n\nexport type ArchitectureDirectionPairMap = Partial<Record<ArchitectureDirectionPair, string>>;\n\n/**\n * Creates a pair of the directions of each side of an edge. This function should be used instead of manually creating it to ensure that the source is always the first character.\n *\n * Note: Undefined is returned when sourceDir and targetDir are the same. In theory this should never happen since the diagram parser throws an error if a user defines it as such.\n * @param sourceDir - source direction\n * @param targetDir - target direction\n * @returns\n */\nexport const getArchitectureDirectionPair = function (\n  sourceDir: ArchitectureDirection,\n  targetDir: ArchitectureDirection\n): ArchitectureDirectionPair | undefined {\n  const pair: `${ArchitectureDirection}${ArchitectureDirection}` = `${sourceDir}${targetDir}`;\n  return isValidArchitectureDirectionPair(pair) ? pair : undefined;\n};\n\n/**\n * Given an x,y position for an arrow and the direction of the edge it belongs to, return a factor for slightly shifting the edge\n * @param param0 - [x, y] coordinate pair\n * @param pair - architecture direction pair\n * @returns a new [x, y] coordinate pair\n */\nexport const shiftPositionByArchitectureDirectionPair = function (\n  [x, y]: number[],\n  pair: ArchitectureDirectionPair\n): number[] {\n  const lhs = pair[0] as ArchitectureDirection;\n  const rhs = pair[1] as ArchitectureDirection;\n  if (isArchitectureDirectionX(lhs)) {\n    if (isArchitectureDirectionY(rhs)) {\n      return [x + (lhs === 'L' ? -1 : 1), y + (rhs === 'T' ? 1 : -1)];\n    } else {\n      return [x + (lhs === 'L' ? -1 : 1), y];\n    }\n  } else {\n    if (isArchitectureDirectionX(rhs)) {\n      return [x + (rhs === 'L' ? 1 : -1), y + (lhs === 'T' ? 1 : -1)];\n    } else {\n      return [x, y + (lhs === 'T' ? 1 : -1)];\n    }\n  }\n};\n\n/**\n * Given the directional pair of an XY edge, get the scale factors necessary to shift the coordinates inwards towards the edge\n * @param pair - XY pair of an edge\n * @returns - number[] containing [+/- 1, +/- 1]\n */\nexport const getArchitectureDirectionXYFactors = function (\n  pair: ArchitectureDirectionPairXY\n): number[] {\n  if (pair === 'LT' || pair === 'TL') {\n    return [1, 1];\n  } else if (pair === 'BL' || pair === 'LB') {\n    return [1, -1];\n  } else if (pair === 'BR' || pair === 'RB') {\n    return [-1, -1];\n  } else {\n    return [-1, 1];\n  }\n};\n\nexport const getArchitectureDirectionAlignment = function (\n  a: ArchitectureDirection,\n  b: ArchitectureDirection\n): ArchitectureAlignment {\n  if (isArchitectureDirectionXY(a, b)) {\n    return 'bend';\n  } else if (isArchitectureDirectionX(a)) {\n    return 'horizontal';\n  }\n  return 'vertical';\n};\n\nexport interface ArchitectureStyleOptions {\n  archEdgeColor: string;\n  archEdgeArrowColor: string;\n  archEdgeWidth: string;\n  archGroupBorderColor: string;\n  archGroupBorderWidth: string;\n}\n\nexport interface ArchitectureService {\n  id: string;\n  type: 'service';\n  edges: ArchitectureEdge[];\n  icon?: string;\n  iconText?: string;\n  title?: string;\n  in?: string;\n  width?: number;\n  height?: number;\n}\n\nexport interface ArchitectureJunction {\n  id: string;\n  type: 'junction';\n  edges: ArchitectureEdge[];\n  in?: string;\n  width?: number;\n  height?: number;\n}\n\nexport type ArchitectureNode = ArchitectureService | ArchitectureJunction;\n\nexport const isArchitectureService = function (x: ArchitectureNode): x is ArchitectureService {\n  const temp = x as ArchitectureService;\n  return temp.type === 'service';\n};\n\nexport const isArchitectureJunction = function (x: ArchitectureNode): x is ArchitectureJunction {\n  const temp = x as ArchitectureJunction;\n  return temp.type === 'junction';\n};\n\nexport interface ArchitectureGroup {\n  id: string;\n  icon?: string;\n  title?: string;\n  in?: string;\n}\n\nexport interface ArchitectureEdge<DT = ArchitectureDirection> {\n  lhsId: string;\n  lhsDir: DT;\n  lhsInto?: boolean;\n  lhsGroup?: boolean;\n  rhsId: string;\n  rhsDir: DT;\n  rhsInto?: boolean;\n  rhsGroup?: boolean;\n  title?: string;\n}\n\nexport interface ArchitectureDB extends DiagramDBBase<ArchitectureDiagramConfig> {\n  clear: () => void;\n  addService: (service: Omit<ArchitectureService, 'edges'>) => void;\n  getServices: () => ArchitectureService[];\n  addJunction: (service: Omit<ArchitectureJunction, 'edges'>) => void;\n  getJunctions: () => ArchitectureJunction[];\n  getNodes: () => ArchitectureNode[];\n  getNode: (id: string) => ArchitectureNode | null;\n  addGroup: (group: ArchitectureGroup) => void;\n  getGroups: () => ArchitectureGroup[];\n  addEdge: (edge: ArchitectureEdge) => void;\n  getEdges: () => ArchitectureEdge[];\n  setElementForId: (id: string, element: D3Element) => void;\n  getElementById: (id: string) => D3Element;\n  getDataStructures: () => ArchitectureDataStructures;\n}\n\nexport type ArchitectureAdjacencyList = Record<string, ArchitectureDirectionPairMap>;\nexport type ArchitectureSpatialMap = Record<string, number[]>;\n\n/**\n * Maps the direction that groups connect from.\n *\n * **Outer key**: ID of group A\n *\n * **Inner key**: ID of group B\n *\n * **Value**: 'vertical' or 'horizontal'\n *\n * Note: tmp[groupA][groupB] == tmp[groupB][groupA]\n */\nexport type ArchitectureGroupAlignments = Record<\n  string,\n  Record<string, Exclude<ArchitectureAlignment, 'bend'>>\n>;\n\nexport interface ArchitectureDataStructures {\n  adjList: ArchitectureAdjacencyList;\n  spatialMaps: ArchitectureSpatialMap[];\n  groupAlignments: ArchitectureGroupAlignments;\n}\n\nexport interface ArchitectureState extends Record<string, unknown> {\n  nodes: Record<string, ArchitectureNode>;\n  groups: Record<string, ArchitectureGroup>;\n  edges: ArchitectureEdge[];\n  registeredIds: Record<string, 'node' | 'group'>;\n  dataStructures?: ArchitectureDataStructures;\n  elements: Record<string, D3Element>;\n  config: ArchitectureDiagramConfig;\n}\n\n/*=======================================*\\\n|        Cytoscape Override Types         |\n\\*=======================================*/\n\nexport interface EdgeSingularData {\n  id: string;\n  label?: string;\n  source: string;\n  sourceDir: ArchitectureDirection;\n  sourceArrow?: boolean;\n  sourceGroup?: boolean;\n  target: string;\n  targetDir: ArchitectureDirection;\n  targetArrow?: boolean;\n  targetGroup?: boolean;\n  [key: string]: any;\n}\n\nexport const edgeData = (edge: cytoscape.EdgeSingular) => {\n  return edge.data() as EdgeSingularData;\n};\n\nexport interface EdgeSingular extends cytoscape.EdgeSingular {\n  _private: {\n    bodyBounds: unknown;\n    rscratch: {\n      startX: number;\n      startY: number;\n      midX: number;\n      midY: number;\n      endX: number;\n      endY: number;\n    };\n  };\n  data(): EdgeSingularData;\n  data<T extends keyof EdgeSingularData>(key: T): EdgeSingularData[T];\n}\n\nexport type NodeSingularData =\n  | {\n      type: 'service';\n      id: string;\n      icon?: string;\n      label?: string;\n      parent?: string;\n      width: number;\n      height: number;\n      [key: string]: any;\n    }\n  | {\n      type: 'junction';\n      id: string;\n      parent?: string;\n      width: number;\n      height: number;\n      [key: string]: any;\n    }\n  | {\n      type: 'group';\n      id: string;\n      icon?: string;\n      label?: string;\n      parent?: string;\n      [key: string]: any;\n    };\n\nexport const nodeData = (node: cytoscape.NodeSingular) => {\n  return node.data() as NodeSingularData;\n};\n\nexport interface NodeSingular extends cytoscape.NodeSingular {\n  _private: {\n    bodyBounds: {\n      h: number;\n      w: number;\n      x1: number;\n      x2: number;\n      y1: number;\n      y2: number;\n    };\n    children: cytoscape.NodeSingular[];\n  };\n  data(): NodeSingularData;\n  data<T extends keyof NodeSingularData>(key: T): NodeSingularData[T];\n}\n", "import type { ArchitectureDiagramConfig } from '../../config.type.js';\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\nimport { getConfig as commonGetConfig } from '../../config.js';\nimport type { D3Element } from '../../types.js';\nimport { ImperativeState } from '../../utils/imperativeState.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport type {\n  ArchitectureAlignment,\n  ArchitectureDB,\n  ArchitectureDirectionPair,\n  ArchitectureDirectionPairMap,\n  ArchitectureEdge,\n  ArchitectureGroup,\n  ArchitectureJunction,\n  ArchitectureNode,\n  ArchitectureService,\n  ArchitectureSpatialMap,\n  ArchitectureState,\n} from './architectureTypes.js';\nimport {\n  getArchitectureDirectionAlignment,\n  getArchitectureDirectionPair,\n  isArchitectureDirection,\n  isArchitectureJunction,\n  isArchitectureService,\n  shiftPositionByArchitectureDirectionPair,\n} from './architectureTypes.js';\nimport { cleanAndMerge } from '../../utils.js';\n\nconst DEFAULT_ARCHITECTURE_CONFIG: Required<ArchitectureDiagramConfig> =\n  DEFAULT_CONFIG.architecture;\n\nconst state = new ImperativeState<ArchitectureState>(() => ({\n  nodes: {},\n  groups: {},\n  edges: [],\n  registeredIds: {},\n  config: DEFAULT_ARCHITECTURE_CONFIG,\n  dataStructures: undefined,\n  elements: {},\n}));\n\nconst clear = (): void => {\n  state.reset();\n  commonClear();\n};\n\nconst addService = function ({\n  id,\n  icon,\n  in: parent,\n  title,\n  iconText,\n}: Omit<ArchitectureService, 'edges'>) {\n  if (state.records.registeredIds[id] !== undefined) {\n    throw new Error(\n      `The service id [${id}] is already in use by another ${state.records.registeredIds[id]}`\n    );\n  }\n  if (parent !== undefined) {\n    if (id === parent) {\n      throw new Error(`The service [${id}] cannot be placed within itself`);\n    }\n    if (state.records.registeredIds[parent] === undefined) {\n      throw new Error(\n        `The service [${id}]'s parent does not exist. Please make sure the parent is created before this service`\n      );\n    }\n    if (state.records.registeredIds[parent] === 'node') {\n      throw new Error(`The service [${id}]'s parent is not a group`);\n    }\n  }\n\n  state.records.registeredIds[id] = 'node';\n\n  state.records.nodes[id] = {\n    id,\n    type: 'service',\n    icon,\n    iconText,\n    title,\n    edges: [],\n    in: parent,\n  };\n};\n\nconst getServices = (): ArchitectureService[] =>\n  Object.values(state.records.nodes).filter<ArchitectureService>(isArchitectureService);\n\nconst addJunction = function ({ id, in: parent }: Omit<ArchitectureJunction, 'edges'>) {\n  state.records.registeredIds[id] = 'node';\n\n  state.records.nodes[id] = {\n    id,\n    type: 'junction',\n    edges: [],\n    in: parent,\n  };\n};\n\nconst getJunctions = (): ArchitectureJunction[] =>\n  Object.values(state.records.nodes).filter<ArchitectureJunction>(isArchitectureJunction);\n\nconst getNodes = (): ArchitectureNode[] => Object.values(state.records.nodes);\n\nconst getNode = (id: string): ArchitectureNode | null => state.records.nodes[id];\n\nconst addGroup = function ({ id, icon, in: parent, title }: ArchitectureGroup) {\n  if (state.records.registeredIds[id] !== undefined) {\n    throw new Error(\n      `The group id [${id}] is already in use by another ${state.records.registeredIds[id]}`\n    );\n  }\n  if (parent !== undefined) {\n    if (id === parent) {\n      throw new Error(`The group [${id}] cannot be placed within itself`);\n    }\n    if (state.records.registeredIds[parent] === undefined) {\n      throw new Error(\n        `The group [${id}]'s parent does not exist. Please make sure the parent is created before this group`\n      );\n    }\n    if (state.records.registeredIds[parent] === 'node') {\n      throw new Error(`The group [${id}]'s parent is not a group`);\n    }\n  }\n\n  state.records.registeredIds[id] = 'group';\n\n  state.records.groups[id] = {\n    id,\n    icon,\n    title,\n    in: parent,\n  };\n};\nconst getGroups = (): ArchitectureGroup[] => {\n  return Object.values(state.records.groups);\n};\n\nconst addEdge = function ({\n  lhsId,\n  rhsId,\n  lhsDir,\n  rhsDir,\n  lhsInto,\n  rhsInto,\n  lhsGroup,\n  rhsGroup,\n  title,\n}: ArchitectureEdge<string>) {\n  if (!isArchitectureDirection(lhsDir)) {\n    throw new Error(\n      `Invalid direction given for left hand side of edge ${lhsId}--${rhsId}. Expected (L,R,T,B) got ${lhsDir}`\n    );\n  }\n  if (!isArchitectureDirection(rhsDir)) {\n    throw new Error(\n      `Invalid direction given for right hand side of edge ${lhsId}--${rhsId}. Expected (L,R,T,B) got ${rhsDir}`\n    );\n  }\n\n  if (state.records.nodes[lhsId] === undefined && state.records.groups[lhsId] === undefined) {\n    throw new Error(\n      `The left-hand id [${lhsId}] does not yet exist. Please create the service/group before declaring an edge to it.`\n    );\n  }\n  if (state.records.nodes[rhsId] === undefined && state.records.groups[lhsId] === undefined) {\n    throw new Error(\n      `The right-hand id [${rhsId}] does not yet exist. Please create the service/group before declaring an edge to it.`\n    );\n  }\n\n  const lhsGroupId = state.records.nodes[lhsId].in;\n  const rhsGroupId = state.records.nodes[rhsId].in;\n  if (lhsGroup && lhsGroupId && rhsGroupId && lhsGroupId == rhsGroupId) {\n    throw new Error(\n      `The left-hand id [${lhsId}] is modified to traverse the group boundary, but the edge does not pass through two groups.`\n    );\n  }\n  if (rhsGroup && lhsGroupId && rhsGroupId && lhsGroupId == rhsGroupId) {\n    throw new Error(\n      `The right-hand id [${rhsId}] is modified to traverse the group boundary, but the edge does not pass through two groups.`\n    );\n  }\n\n  const edge = {\n    lhsId,\n    lhsDir,\n    lhsInto,\n    lhsGroup,\n    rhsId,\n    rhsDir,\n    rhsInto,\n    rhsGroup,\n    title,\n  };\n\n  state.records.edges.push(edge);\n  if (state.records.nodes[lhsId] && state.records.nodes[rhsId]) {\n    state.records.nodes[lhsId].edges.push(state.records.edges[state.records.edges.length - 1]);\n    state.records.nodes[rhsId].edges.push(state.records.edges[state.records.edges.length - 1]);\n  }\n};\n\nconst getEdges = (): ArchitectureEdge[] => state.records.edges;\n\n/**\n * Returns the current diagram's adjacency list, spatial map, & group alignments.\n * If they have not been created, run the algorithms to generate them.\n * @returns\n */\nconst getDataStructures = () => {\n  if (state.records.dataStructures === undefined) {\n    // Tracks how groups are aligned with one another. Generated while creating the adj list\n    const groupAlignments: Record<\n      string,\n      Record<string, Exclude<ArchitectureAlignment, 'bend'>>\n    > = {};\n\n    // Create an adjacency list of the diagram to perform BFS on\n    // Outer reduce applied on all services\n    // Inner reduce applied on the edges for a service\n    const adjList = Object.entries(state.records.nodes).reduce<\n      Record<string, ArchitectureDirectionPairMap>\n    >((prevOuter, [id, service]) => {\n      prevOuter[id] = service.edges.reduce<ArchitectureDirectionPairMap>((prevInner, edge) => {\n        // track the direction groups connect to one another\n        const lhsGroupId = getNode(edge.lhsId)?.in;\n        const rhsGroupId = getNode(edge.rhsId)?.in;\n        if (lhsGroupId && rhsGroupId && lhsGroupId !== rhsGroupId) {\n          const alignment = getArchitectureDirectionAlignment(edge.lhsDir, edge.rhsDir);\n          if (alignment !== 'bend') {\n            groupAlignments[lhsGroupId] ??= {};\n            groupAlignments[lhsGroupId][rhsGroupId] = alignment;\n            groupAlignments[rhsGroupId] ??= {};\n            groupAlignments[rhsGroupId][lhsGroupId] = alignment;\n          }\n        }\n\n        if (edge.lhsId === id) {\n          // source is LHS\n          const pair = getArchitectureDirectionPair(edge.lhsDir, edge.rhsDir);\n          if (pair) {\n            prevInner[pair] = edge.rhsId;\n          }\n        } else {\n          // source is RHS\n          const pair = getArchitectureDirectionPair(edge.rhsDir, edge.lhsDir);\n          if (pair) {\n            prevInner[pair] = edge.lhsId;\n          }\n        }\n        return prevInner;\n      }, {});\n      return prevOuter;\n    }, {});\n\n    // Configuration for the initial pass of BFS\n    const firstId = Object.keys(adjList)[0];\n    const visited = { [firstId]: 1 };\n    // If a key is present in this object, it has not been visited\n    const notVisited = Object.keys(adjList).reduce(\n      (prev, id) => (id === firstId ? prev : { ...prev, [id]: 1 }),\n      {} as Record<string, number>\n    );\n\n    // Perform BFS on the adjacency list\n    const BFS = (startingId: string): ArchitectureSpatialMap => {\n      const spatialMap = { [startingId]: [0, 0] };\n      const queue = [startingId];\n      while (queue.length > 0) {\n        const id = queue.shift();\n        if (id) {\n          visited[id] = 1;\n          delete notVisited[id];\n          const adj = adjList[id];\n          const [posX, posY] = spatialMap[id];\n          Object.entries(adj).forEach(([dir, rhsId]) => {\n            if (!visited[rhsId]) {\n              spatialMap[rhsId] = shiftPositionByArchitectureDirectionPair(\n                [posX, posY],\n                dir as ArchitectureDirectionPair\n              );\n              queue.push(rhsId);\n            }\n          });\n        }\n      }\n      return spatialMap;\n    };\n    const spatialMaps = [BFS(firstId)];\n\n    // If our diagram is disconnected, keep adding additional spatial maps until all disconnected graphs have been found\n    while (Object.keys(notVisited).length > 0) {\n      spatialMaps.push(BFS(Object.keys(notVisited)[0]));\n    }\n    state.records.dataStructures = {\n      adjList,\n      spatialMaps,\n      groupAlignments,\n    };\n  }\n  return state.records.dataStructures;\n};\n\nconst setElementForId = (id: string, element: D3Element) => {\n  state.records.elements[id] = element;\n};\nconst getElementById = (id: string) => state.records.elements[id];\n\nconst getConfig = (): Required<ArchitectureDiagramConfig> => {\n  const config = cleanAndMerge({\n    ...DEFAULT_ARCHITECTURE_CONFIG,\n    ...commonGetConfig().architecture,\n  });\n  return config;\n};\n\nexport const db: ArchitectureDB = {\n  clear,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  getConfig,\n\n  addService,\n  getServices,\n  addJunction,\n  getJunctions,\n  getNodes,\n  getNode,\n  addGroup,\n  getGroups,\n  addEdge,\n  getEdges,\n  setElementForId,\n  getElementById,\n  getDataStructures,\n};\n\n/**\n * Typed wrapper for resolving an architecture diagram's config fields. Returns the default value if undefined\n * @param field - the config field to access\n * @returns\n */\nexport function getConfigField<T extends keyof ArchitectureDiagramConfig>(\n  field: T\n): Required<ArchitectureDiagramConfig>[T] {\n  return getConfig()[field];\n}\n", "import type { DiagramStylesProvider } from '../../diagram-api/types.js';\nimport type { ArchitectureStyleOptions } from './architectureTypes.js';\n\nconst getStyles: DiagramStylesProvider = (options: ArchitectureStyleOptions) =>\n  `\n  .edge {\n    stroke-width: ${options.archEdgeWidth};\n    stroke: ${options.archEdgeColor};\n    fill: none;\n  }\n\n  .arrow {\n    fill: ${options.archEdgeArrowColor};\n  }\n\n  .node-bkg {\n    fill: none;\n    stroke: ${options.archGroupBorderColor};\n    stroke-width: ${options.archGroupBorderWidth};\n    stroke-dasharray: 8;\n  }\n  .node-icon-text {\n    display: flex; \n    align-items: center;\n  }\n  \n  .node-icon-text > div {\n    color: #fff;\n    margin: 1px;\n    height: fit-content;\n    text-align: center;\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n  }\n`;\n\nexport default getStyles;\n", "import { registerIconPacks } from '../../rendering-util/icons.js';\nimport type { Position } from 'cytoscape';\nimport cytoscape from 'cytoscape';\nimport type { FcoseLayoutOptions } from 'cytoscape-fcose';\nimport fcose from 'cytoscape-fcose';\nimport { select } from 'd3';\nimport type { DrawDefinition, SVG } from '../../diagram-api/types.js';\nimport type { Diagram } from '../../Diagram.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport { getConfigField } from './architectureDb.js';\nimport { architectureIcons } from './architectureIcons.js';\nimport type {\n  ArchitectureAlignment,\n  ArchitectureDataStructures,\n  ArchitectureGroupAlignments,\n  ArchitectureJunction,\n  ArchitectureSpatialMap,\n  EdgeSingular,\n  EdgeSingularData,\n  NodeSingularData,\n} from './architectureTypes.js';\nimport {\n  type ArchitectureDB,\n  type ArchitectureDirection,\n  type ArchitectureEdge,\n  type ArchitectureGroup,\n  type ArchitectureService,\n  ArchitectureDirectionName,\n  edgeData,\n  getOppositeArchitectureDirection,\n  isArchitectureDirectionXY,\n  isArchitectureDirectionY,\n  nodeData,\n} from './architectureTypes.js';\nimport { drawEdges, drawGroups, drawJunctions, drawServices } from './svgDraw.js';\n\nregisterIconPacks([\n  {\n    name: architectureIcons.prefix,\n    icons: architectureIcons,\n  },\n]);\ncytoscape.use(fcose);\n\nfunction addServices(services: ArchitectureService[], cy: cytoscape.Core) {\n  services.forEach((service) => {\n    cy.add({\n      group: 'nodes',\n      data: {\n        type: 'service',\n        id: service.id,\n        icon: service.icon,\n        label: service.title,\n        parent: service.in,\n        width: getConfigField('iconSize'),\n        height: getConfigField('iconSize'),\n      } as NodeSingularData,\n      classes: 'node-service',\n    });\n  });\n}\n\nfunction addJunctions(junctions: ArchitectureJunction[], cy: cytoscape.Core) {\n  junctions.forEach((junction) => {\n    cy.add({\n      group: 'nodes',\n      data: {\n        type: 'junction',\n        id: junction.id,\n        parent: junction.in,\n        width: getConfigField('iconSize'),\n        height: getConfigField('iconSize'),\n      } as NodeSingularData,\n      classes: 'node-junction',\n    });\n  });\n}\n\nfunction positionNodes(db: ArchitectureDB, cy: cytoscape.Core) {\n  cy.nodes().map((node) => {\n    const data = nodeData(node);\n    if (data.type === 'group') {\n      return;\n    }\n    data.x = node.position().x;\n    data.y = node.position().y;\n\n    const nodeElem = db.getElementById(data.id);\n    nodeElem.attr('transform', 'translate(' + (data.x || 0) + ',' + (data.y || 0) + ')');\n  });\n}\n\nfunction addGroups(groups: ArchitectureGroup[], cy: cytoscape.Core) {\n  groups.forEach((group) => {\n    cy.add({\n      group: 'nodes',\n      data: {\n        type: 'group',\n        id: group.id,\n        icon: group.icon,\n        label: group.title,\n        parent: group.in,\n      } as NodeSingularData,\n      classes: 'node-group',\n    });\n  });\n}\n\nfunction addEdges(edges: ArchitectureEdge[], cy: cytoscape.Core) {\n  edges.forEach((parsedEdge) => {\n    const { lhsId, rhsId, lhsInto, lhsGroup, rhsInto, lhsDir, rhsDir, rhsGroup, title } =\n      parsedEdge;\n    const edgeType = isArchitectureDirectionXY(parsedEdge.lhsDir, parsedEdge.rhsDir)\n      ? 'segments'\n      : 'straight';\n    const edge: EdgeSingularData = {\n      id: `${lhsId}-${rhsId}`,\n      label: title,\n      source: lhsId,\n      sourceDir: lhsDir,\n      sourceArrow: lhsInto,\n      sourceGroup: lhsGroup,\n      sourceEndpoint:\n        lhsDir === 'L'\n          ? '0 50%'\n          : lhsDir === 'R'\n            ? '100% 50%'\n            : lhsDir === 'T'\n              ? '50% 0'\n              : '50% 100%',\n      target: rhsId,\n      targetDir: rhsDir,\n      targetArrow: rhsInto,\n      targetGroup: rhsGroup,\n      targetEndpoint:\n        rhsDir === 'L'\n          ? '0 50%'\n          : rhsDir === 'R'\n            ? '100% 50%'\n            : rhsDir === 'T'\n              ? '50% 0'\n              : '50% 100%',\n    };\n    cy.add({\n      group: 'edges',\n      data: edge,\n      classes: edgeType,\n    });\n  });\n}\n\nfunction getAlignments(\n  db: ArchitectureDB,\n  spatialMaps: ArchitectureSpatialMap[],\n  groupAlignments: ArchitectureGroupAlignments\n): fcose.FcoseAlignmentConstraint {\n  /**\n   * Flattens the alignment object so nodes in different groups will be in the same alignment array IFF their groups don't connect in a conflicting alignment\n   *\n   * i.e., two groups which connect horizontally should not have nodes with vertical alignments to one another\n   *\n   * See: #5952\n   *\n   * @param alignmentObj - alignment object with the outer key being the row/col # and the inner key being the group name mapped to the nodes on that axis in the group\n   * @param alignmentDir - alignment direction\n   * @returns flattened alignment object with an arbitrary key mapping to nodes in the same row/col\n   */\n  const flattenAlignments = (\n    alignmentObj: Record<number, Record<string, string[]>>,\n    alignmentDir: ArchitectureAlignment\n  ): Record<string, string[]> => {\n    return Object.entries(alignmentObj).reduce(\n      (prev, [dir, alignments]) => {\n        // prev is the mapping of x/y coordinate to an array of the nodes in that row/column\n        let cnt = 0;\n        const arr = Object.entries(alignments); // [group name, array of nodes within the group on axis dir]\n        if (arr.length === 1) {\n          // If only one group exists in the row/column, we don't need to do anything else\n          prev[dir] = arr[0][1];\n          return prev;\n        }\n        for (let i = 0; i < arr.length - 1; i++) {\n          for (let j = i + 1; j < arr.length; j++) {\n            const [aGroupId, aNodeIds] = arr[i];\n            const [bGroupId, bNodeIds] = arr[j];\n            const alignment = groupAlignments[aGroupId]?.[bGroupId]; // Get how the two groups are intended to align (undefined if they aren't)\n\n            if (alignment === alignmentDir) {\n              // If the intended alignment between the two groups is the same as the alignment we are parsing\n              prev[dir] ??= [];\n              prev[dir] = [...prev[dir], ...aNodeIds, ...bNodeIds]; // add the node ids of both groups to the axis array in prev\n            } else if (aGroupId === 'default' || bGroupId === 'default') {\n              // If either of the groups are in the default space (not in a group), use the same behavior as above\n              prev[dir] ??= [];\n              prev[dir] = [...prev[dir], ...aNodeIds, ...bNodeIds];\n            } else {\n              // Otherwise, the nodes in the two groups are not intended to align\n              const keyA = `${dir}-${cnt++}`;\n              prev[keyA] = aNodeIds;\n              const keyB = `${dir}-${cnt++}`;\n              prev[keyB] = bNodeIds;\n            }\n          }\n        }\n\n        return prev;\n      },\n      {} as Record<string, string[]>\n    );\n  };\n\n  const alignments = spatialMaps.map((spatialMap) => {\n    const horizontalAlignments: Record<number, Record<string, string[]>> = {};\n    const verticalAlignments: Record<number, Record<string, string[]>> = {};\n\n    // Group service ids in an object with their x and y coordinate as the key\n    Object.entries(spatialMap).forEach(([id, [x, y]]) => {\n      const nodeGroup = db.getNode(id)?.in ?? 'default';\n\n      horizontalAlignments[y] ??= {};\n      horizontalAlignments[y][nodeGroup] ??= [];\n      horizontalAlignments[y][nodeGroup].push(id);\n\n      verticalAlignments[x] ??= {};\n      verticalAlignments[x][nodeGroup] ??= [];\n      verticalAlignments[x][nodeGroup].push(id);\n    });\n\n    // Merge the values of each object into a list if the inner list has at least 2 elements\n    return {\n      horiz: Object.values(flattenAlignments(horizontalAlignments, 'horizontal')).filter(\n        (arr) => arr.length > 1\n      ),\n      vert: Object.values(flattenAlignments(verticalAlignments, 'vertical')).filter(\n        (arr) => arr.length > 1\n      ),\n    };\n  });\n\n  // Merge the alignment lists for each spatial map into one 2d array per axis\n  const [horizontal, vertical] = alignments.reduce(\n    ([prevHoriz, prevVert], { horiz, vert }) => {\n      return [\n        [...prevHoriz, ...horiz],\n        [...prevVert, ...vert],\n      ];\n    },\n    [[] as string[][], [] as string[][]]\n  );\n\n  return {\n    horizontal,\n    vertical,\n  };\n}\n\nfunction getRelativeConstraints(\n  spatialMaps: ArchitectureSpatialMap[]\n): fcose.FcoseRelativePlacementConstraint[] {\n  const relativeConstraints: fcose.FcoseRelativePlacementConstraint[] = [];\n  const posToStr = (pos: number[]) => `${pos[0]},${pos[1]}`;\n  const strToPos = (pos: string) => pos.split(',').map((p) => parseInt(p));\n\n  spatialMaps.forEach((spatialMap) => {\n    const invSpatialMap = Object.fromEntries(\n      Object.entries(spatialMap).map(([id, pos]) => [posToStr(pos), id])\n    );\n\n    // perform BFS\n    const queue = [posToStr([0, 0])];\n    const visited: Record<string, number> = {};\n    const directions: Record<ArchitectureDirection, number[]> = {\n      L: [-1, 0],\n      R: [1, 0],\n      T: [0, 1],\n      B: [0, -1],\n    };\n    while (queue.length > 0) {\n      const curr = queue.shift();\n      if (curr) {\n        visited[curr] = 1;\n        const currId = invSpatialMap[curr];\n        if (currId) {\n          const currPos = strToPos(curr);\n          Object.entries(directions).forEach(([dir, shift]) => {\n            const newPos = posToStr([currPos[0] + shift[0], currPos[1] + shift[1]]);\n            const newId = invSpatialMap[newPos];\n            // If there is an adjacent service to the current one and it has not yet been visited\n            if (newId && !visited[newPos]) {\n              queue.push(newPos);\n              // @ts-ignore cannot determine if left/right or top/bottom are paired together\n              relativeConstraints.push({\n                [ArchitectureDirectionName[dir as ArchitectureDirection]]: newId,\n                [ArchitectureDirectionName[\n                  getOppositeArchitectureDirection(dir as ArchitectureDirection)\n                ]]: currId,\n                gap: 1.5 * getConfigField('iconSize'),\n              });\n            }\n          });\n        }\n      }\n    }\n  });\n  return relativeConstraints;\n}\n\nfunction layoutArchitecture(\n  services: ArchitectureService[],\n  junctions: ArchitectureJunction[],\n  groups: ArchitectureGroup[],\n  edges: ArchitectureEdge[],\n  db: ArchitectureDB,\n  { spatialMaps, groupAlignments }: ArchitectureDataStructures\n): Promise<cytoscape.Core> {\n  return new Promise((resolve) => {\n    const renderEl = select('body').append('div').attr('id', 'cy').attr('style', 'display:none');\n    const cy = cytoscape({\n      container: document.getElementById('cy'),\n      style: [\n        {\n          selector: 'edge',\n          style: {\n            'curve-style': 'straight',\n            label: 'data(label)',\n            'source-endpoint': 'data(sourceEndpoint)',\n            'target-endpoint': 'data(targetEndpoint)',\n          },\n        },\n        {\n          selector: 'edge.segments',\n          style: {\n            'curve-style': 'segments',\n            'segment-weights': '0',\n            'segment-distances': [0.5],\n            // @ts-ignore Incorrect library types\n            'edge-distances': 'endpoints',\n            'source-endpoint': 'data(sourceEndpoint)',\n            'target-endpoint': 'data(targetEndpoint)',\n          },\n        },\n        {\n          selector: 'node',\n          style: {\n            // @ts-ignore Incorrect library types\n            'compound-sizing-wrt-labels': 'include',\n          },\n        },\n        {\n          selector: 'node[label]',\n          style: {\n            'text-valign': 'bottom',\n            'text-halign': 'center',\n            'font-size': `${getConfigField('fontSize')}px`,\n          },\n        },\n        {\n          selector: '.node-service',\n          style: {\n            label: 'data(label)',\n            width: 'data(width)',\n            height: 'data(height)',\n          },\n        },\n        {\n          selector: '.node-junction',\n          style: {\n            width: 'data(width)',\n            height: 'data(height)',\n          },\n        },\n        {\n          selector: '.node-group',\n          style: {\n            // @ts-ignore Incorrect library types\n            padding: `${getConfigField('padding')}px`,\n          },\n        },\n      ],\n      layout: {\n        name: 'grid',\n        boundingBox: {\n          x1: 0,\n          x2: 100,\n          y1: 0,\n          y2: 100,\n        },\n      },\n    });\n    // Remove element after layout\n    renderEl.remove();\n\n    addGroups(groups, cy);\n    addServices(services, cy);\n    addJunctions(junctions, cy);\n    addEdges(edges, cy);\n    // Use the spatial map to create alignment arrays for fcose\n    const alignmentConstraint = getAlignments(db, spatialMaps, groupAlignments);\n\n    // Create the relative constraints for fcose by using an inverse of the spatial map and performing BFS on it\n    const relativePlacementConstraint = getRelativeConstraints(spatialMaps);\n\n    const layout = cy.layout({\n      name: 'fcose',\n      quality: 'proof',\n      styleEnabled: false,\n      animate: false,\n      nodeDimensionsIncludeLabels: false,\n      // Adjust the edge parameters if it passes through the border of a group\n      // Hacky fix for: https://github.com/iVis-at-Bilkent/cytoscape.js-fcose/issues/67\n      idealEdgeLength(edge: EdgeSingular) {\n        const [nodeA, nodeB] = edge.connectedNodes();\n        const { parent: parentA } = nodeData(nodeA);\n        const { parent: parentB } = nodeData(nodeB);\n        const elasticity =\n          parentA === parentB ? 1.5 * getConfigField('iconSize') : 0.5 * getConfigField('iconSize');\n        return elasticity;\n      },\n      edgeElasticity(edge: EdgeSingular) {\n        const [nodeA, nodeB] = edge.connectedNodes();\n        const { parent: parentA } = nodeData(nodeA);\n        const { parent: parentB } = nodeData(nodeB);\n        const elasticity = parentA === parentB ? 0.45 : 0.001;\n        return elasticity;\n      },\n      alignmentConstraint,\n      relativePlacementConstraint,\n    } as FcoseLayoutOptions);\n\n    // Once the diagram has been generated and the service's position cords are set, adjust the XY edges to have a 90deg bend\n    layout.one('layoutstop', () => {\n      function getSegmentWeights(\n        source: Position,\n        target: Position,\n        pointX: number,\n        pointY: number\n      ) {\n        let W, D;\n        const { x: sX, y: sY } = source;\n        const { x: tX, y: tY } = target;\n\n        D =\n          (pointY - sY + ((sX - pointX) * (sY - tY)) / (sX - tX)) /\n          Math.sqrt(1 + Math.pow((sY - tY) / (sX - tX), 2));\n        W = Math.sqrt(Math.pow(pointY - sY, 2) + Math.pow(pointX - sX, 2) - Math.pow(D, 2));\n\n        const distAB = Math.sqrt(Math.pow(tX - sX, 2) + Math.pow(tY - sY, 2));\n        W = W / distAB;\n\n        //check whether the point (pointX, pointY) is on right or left of the line src to tgt. for instance : a point C(X, Y) and line (AB).  d=(xB-xA)(yC-yA)-(yB-yA)(xC-xA). if d>0, then C is on left of the line. if d<0, it is on right. if d=0, it is on the line.\n        let delta1 = (tX - sX) * (pointY - sY) - (tY - sY) * (pointX - sX);\n        switch (true) {\n          case delta1 >= 0:\n            delta1 = 1;\n            break;\n          case delta1 < 0:\n            delta1 = -1;\n            break;\n        }\n        //check whether the point (pointX, pointY) is \"behind\" the line src to tgt\n        let delta2 = (tX - sX) * (pointX - sX) + (tY - sY) * (pointY - sY);\n        switch (true) {\n          case delta2 >= 0:\n            delta2 = 1;\n            break;\n          case delta2 < 0:\n            delta2 = -1;\n            break;\n        }\n\n        D = Math.abs(D) * delta1; //ensure that sign of D is same as sign of delta1. Hence we need to take absolute value of D and multiply by delta1\n        W = W * delta2;\n\n        return {\n          distances: D,\n          weights: W,\n        };\n      }\n      cy.startBatch();\n      for (const edge of Object.values(cy.edges())) {\n        if (edge.data?.()) {\n          const { x: sX, y: sY } = edge.source().position();\n          const { x: tX, y: tY } = edge.target().position();\n          if (sX !== tX && sY !== tY) {\n            const sEP = edge.sourceEndpoint();\n            const tEP = edge.targetEndpoint();\n            const { sourceDir } = edgeData(edge);\n            const [pointX, pointY] = isArchitectureDirectionY(sourceDir)\n              ? [sEP.x, tEP.y]\n              : [tEP.x, sEP.y];\n            const { weights, distances } = getSegmentWeights(sEP, tEP, pointX, pointY);\n            edge.style('segment-distances', distances);\n            edge.style('segment-weights', weights);\n          }\n        }\n      }\n      cy.endBatch();\n      layout.run();\n    });\n    layout.run();\n\n    cy.ready((e) => {\n      log.info('Ready', e);\n      resolve(cy);\n    });\n  });\n}\n\nexport const draw: DrawDefinition = async (text, id, _version, diagObj: Diagram) => {\n  // TODO: Add title support for architecture diagrams\n\n  const db = diagObj.db as ArchitectureDB;\n\n  const services = db.getServices();\n  const junctions = db.getJunctions();\n  const groups = db.getGroups();\n  const edges = db.getEdges();\n  const ds = db.getDataStructures();\n\n  const svg: SVG = selectSvgElement(id);\n\n  const edgesElem = svg.append('g');\n  edgesElem.attr('class', 'architecture-edges');\n\n  const servicesElem = svg.append('g');\n  servicesElem.attr('class', 'architecture-services');\n\n  const groupElem = svg.append('g');\n  groupElem.attr('class', 'architecture-groups');\n\n  await drawServices(db, servicesElem, services);\n  drawJunctions(db, servicesElem, junctions);\n\n  const cy = await layoutArchitecture(services, junctions, groups, edges, db, ds);\n\n  await drawEdges(edgesElem, cy);\n  await drawGroups(groupElem, cy);\n  positionNodes(db, cy);\n\n  setupGraphViewbox(undefined, svg, getConfigField('padding'), getConfigField('useMaxWidth'));\n};\n\nexport const renderer = { draw };\n", "import { unknownIcon } from '../../rendering-util/icons.js';\nimport type { IconifyJSON } from '@iconify/types';\n\nconst wrapIcon = (icon: string) => {\n  return `<g><rect width=\"80\" height=\"80\" style=\"fill: #087ebf; stroke-width: 0px;\"/>${icon}</g>`;\n};\n\nexport const architectureIcons: IconifyJSON = {\n  prefix: 'mermaid-architecture',\n  height: 80,\n  width: 80,\n  icons: {\n    database: {\n      body: wrapIcon(\n        '<path id=\"b\" data-name=\"4\" d=\"m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path id=\"c\" data-name=\"3\" d=\"m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path id=\"d\" data-name=\"2\" d=\"m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse id=\"e\" data-name=\"1\" cx=\"40\" cy=\"22.14\" rx=\"20\" ry=\"7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"20\" y1=\"57.86\" x2=\"20\" y2=\"22.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"60\" y1=\"57.86\" x2=\"60\" y2=\"22.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/>'\n      ),\n    },\n    server: {\n      body: wrapIcon(\n        '<rect x=\"17.5\" y=\"17.5\" width=\"45\" height=\"45\" rx=\"2\" ry=\"2\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"17.5\" y1=\"32.5\" x2=\"62.5\" y2=\"32.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"17.5\" y1=\"47.5\" x2=\"62.5\" y2=\"47.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><g><path d=\"m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: #fff; stroke-width: 0px;\"/><path d=\"m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><path d=\"m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: #fff; stroke-width: 0px;\"/><path d=\"m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><path d=\"m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: #fff; stroke-width: 0px;\"/><path d=\"m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><circle cx=\"32.5\" cy=\"25\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"27.5\" cy=\"25\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"22.5\" cy=\"25\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><circle cx=\"32.5\" cy=\"40\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"27.5\" cy=\"40\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"22.5\" cy=\"40\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><circle cx=\"32.5\" cy=\"55\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"27.5\" cy=\"55\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"22.5\" cy=\"55\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/></g>'\n      ),\n    },\n    disk: {\n      body: wrapIcon(\n        '<rect x=\"20\" y=\"15\" width=\"40\" height=\"50\" rx=\"1\" ry=\"1\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"24\" cy=\"19.17\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"56\" cy=\"19.17\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"24\" cy=\"60.83\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"56\" cy=\"60.83\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"40\" cy=\"33.75\" rx=\"14\" ry=\"14.58\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"40\" cy=\"33.75\" rx=\"4\" ry=\"4.17\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path d=\"m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z\" style=\"fill: #fff; stroke-width: 0px;\"/>'\n      ),\n    },\n    internet: {\n      body: wrapIcon(\n        '<circle cx=\"40\" cy=\"40\" r=\"22.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"40\" y1=\"17.5\" x2=\"40\" y2=\"62.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"17.5\" y1=\"40\" x2=\"62.5\" y2=\"40\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path d=\"m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path d=\"m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"19.75\" y1=\"30.1\" x2=\"60.25\" y2=\"30.1\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"19.75\" y1=\"49.9\" x2=\"60.25\" y2=\"49.9\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/>'\n      ),\n    },\n    cloud: {\n      body: wrapIcon(\n        '<path d=\"m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/>'\n      ),\n    },\n    unknown: unknownIcon,\n    blank: {\n      body: wrapIcon(''),\n    },\n  },\n};\n", "import { getIconSVG } from '../../rendering-util/icons.js';\nimport type cytoscape from 'cytoscape';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { createText } from '../../rendering-util/createText.js';\nimport type { D3Element } from '../../types.js';\nimport { db, getConfigField } from './architectureDb.js';\nimport { architectureIcons } from './architectureIcons.js';\nimport {\n  ArchitectureDirectionArrow,\n  ArchitectureDirectionArrowShift,\n  edgeData,\n  getArchitectureDirectionPair,\n  getArchitectureDirectionXYFactors,\n  isArchitectureDirectionX,\n  isArchitectureDirectionXY,\n  isArchitectureDirectionY,\n  isArchitecturePairXY,\n  nodeData,\n  type ArchitectureDB,\n  type ArchitectureJunction,\n  type ArchitectureService,\n} from './architectureTypes.js';\n\nexport const drawEdges = async function (edgesEl: D3Element, cy: cytoscape.Core) {\n  const padding = getConfigField('padding');\n  const iconSize = getConfigField('iconSize');\n  const halfIconSize = iconSize / 2;\n  const arrowSize = iconSize / 6;\n  const halfArrowSize = arrowSize / 2;\n\n  await Promise.all(\n    cy.edges().map(async (edge) => {\n      const {\n        source,\n        sourceDir,\n        sourceArrow,\n        sourceGroup,\n        target,\n        targetDir,\n        targetArrow,\n        targetGroup,\n        label,\n      } = edgeData(edge);\n      let { x: startX, y: startY } = edge[0].sourceEndpoint();\n      const { x: midX, y: midY } = edge[0].midpoint();\n      let { x: endX, y: endY } = edge[0].targetEndpoint();\n\n      // Adjust the edge distance if it has the {group} modifier\n      const groupEdgeShift = padding + 4;\n      // +18 comes from the service label height that extends the padding on the bottom side of each group\n      if (sourceGroup) {\n        if (isArchitectureDirectionX(sourceDir)) {\n          startX += sourceDir === 'L' ? -groupEdgeShift : groupEdgeShift;\n        } else {\n          startY += sourceDir === 'T' ? -groupEdgeShift : groupEdgeShift + 18;\n        }\n      }\n\n      if (targetGroup) {\n        if (isArchitectureDirectionX(targetDir)) {\n          endX += targetDir === 'L' ? -groupEdgeShift : groupEdgeShift;\n        } else {\n          endY += targetDir === 'T' ? -groupEdgeShift : groupEdgeShift + 18;\n        }\n      }\n\n      // Adjust the edge distance if it doesn't have the {group} modifier and the endpoint is a junction node\n      if (!sourceGroup && db.getNode(source)?.type === 'junction') {\n        if (isArchitectureDirectionX(sourceDir)) {\n          startX += sourceDir === 'L' ? halfIconSize : -halfIconSize;\n        } else {\n          startY += sourceDir === 'T' ? halfIconSize : -halfIconSize;\n        }\n      }\n      if (!targetGroup && db.getNode(target)?.type === 'junction') {\n        if (isArchitectureDirectionX(targetDir)) {\n          endX += targetDir === 'L' ? halfIconSize : -halfIconSize;\n        } else {\n          endY += targetDir === 'T' ? halfIconSize : -halfIconSize;\n        }\n      }\n\n      if (edge[0]._private.rscratch) {\n        // const bounds = edge[0]._private.rscratch;\n\n        const g = edgesEl.insert('g');\n\n        g.insert('path')\n          .attr('d', `M ${startX},${startY} L ${midX},${midY} L${endX},${endY} `)\n          .attr('class', 'edge');\n\n        if (sourceArrow) {\n          const xShift = isArchitectureDirectionX(sourceDir)\n            ? ArchitectureDirectionArrowShift[sourceDir](startX, arrowSize)\n            : startX - halfArrowSize;\n          const yShift = isArchitectureDirectionY(sourceDir)\n            ? ArchitectureDirectionArrowShift[sourceDir](startY, arrowSize)\n            : startY - halfArrowSize;\n\n          g.insert('polygon')\n            .attr('points', ArchitectureDirectionArrow[sourceDir](arrowSize))\n            .attr('transform', `translate(${xShift},${yShift})`)\n            .attr('class', 'arrow');\n        }\n        if (targetArrow) {\n          const xShift = isArchitectureDirectionX(targetDir)\n            ? ArchitectureDirectionArrowShift[targetDir](endX, arrowSize)\n            : endX - halfArrowSize;\n          const yShift = isArchitectureDirectionY(targetDir)\n            ? ArchitectureDirectionArrowShift[targetDir](endY, arrowSize)\n            : endY - halfArrowSize;\n\n          g.insert('polygon')\n            .attr('points', ArchitectureDirectionArrow[targetDir](arrowSize))\n            .attr('transform', `translate(${xShift},${yShift})`)\n            .attr('class', 'arrow');\n        }\n\n        if (label) {\n          const axis = !isArchitectureDirectionXY(sourceDir, targetDir)\n            ? isArchitectureDirectionX(sourceDir)\n              ? 'X'\n              : 'Y'\n            : 'XY';\n\n          let width = 0;\n          if (axis === 'X') {\n            width = Math.abs(startX - endX);\n          } else if (axis === 'Y') {\n            // Reduce width by a factor of 1.5 to avoid overlapping service labels\n            width = Math.abs(startY - endY) / 1.5;\n          } else {\n            width = Math.abs(startX - endX) / 2;\n          }\n\n          const textElem = g.append('g');\n          await createText(\n            textElem,\n            label,\n            {\n              useHtmlLabels: false,\n              width,\n              classes: 'architecture-service-label',\n            },\n            getConfig()\n          );\n\n          textElem\n            .attr('dy', '1em')\n            .attr('alignment-baseline', 'middle')\n            .attr('dominant-baseline', 'middle')\n            .attr('text-anchor', 'middle');\n\n          if (axis === 'X') {\n            textElem.attr('transform', 'translate(' + midX + ', ' + midY + ')');\n          } else if (axis === 'Y') {\n            textElem.attr('transform', 'translate(' + midX + ', ' + midY + ') rotate(-90)');\n          } else if (axis === 'XY') {\n            const pair = getArchitectureDirectionPair(sourceDir, targetDir);\n            if (pair && isArchitecturePairXY(pair)) {\n              const bboxOrig = textElem.node().getBoundingClientRect();\n              const [x, y] = getArchitectureDirectionXYFactors(pair);\n\n              textElem\n                .attr('dominant-baseline', 'auto')\n                .attr('transform', `rotate(${-1 * x * y * 45})`);\n\n              // Calculate the new width/height with the rotation applied, and transform to the proper position\n              const bboxNew = textElem.node().getBoundingClientRect();\n              textElem.attr(\n                'transform',\n                `\n                translate(${midX}, ${midY - bboxOrig.height / 2})\n                translate(${(x * bboxNew.width) / 2}, ${(y * bboxNew.height) / 2})\n                rotate(${-1 * x * y * 45}, 0, ${bboxOrig.height / 2})\n              `\n              );\n            }\n          }\n        }\n      }\n    })\n  );\n};\n\nexport const drawGroups = async function (groupsEl: D3Element, cy: cytoscape.Core) {\n  const padding = getConfigField('padding');\n  const groupIconSize = padding * 0.75;\n\n  const fontSize = getConfigField('fontSize');\n\n  const iconSize = getConfigField('iconSize');\n  const halfIconSize = iconSize / 2;\n\n  await Promise.all(\n    cy.nodes().map(async (node) => {\n      const data = nodeData(node);\n      if (data.type === 'group') {\n        const { h, w, x1, y1 } = node.boundingBox();\n\n        groupsEl\n          .append('rect')\n          .attr('x', x1 + halfIconSize)\n          .attr('y', y1 + halfIconSize)\n          .attr('width', w)\n          .attr('height', h)\n          .attr('class', 'node-bkg');\n\n        const groupLabelContainer = groupsEl.append('g');\n        let shiftedX1 = x1;\n        let shiftedY1 = y1;\n        if (data.icon) {\n          const bkgElem = groupLabelContainer.append('g');\n          bkgElem.html(\n            `<g>${await getIconSVG(data.icon, { height: groupIconSize, width: groupIconSize, fallbackPrefix: architectureIcons.prefix })}</g>`\n          );\n          bkgElem.attr(\n            'transform',\n            'translate(' +\n              (shiftedX1 + halfIconSize + 1) +\n              ', ' +\n              (shiftedY1 + halfIconSize + 1) +\n              ')'\n          );\n          shiftedX1 += groupIconSize;\n          // TODO: test with more values\n          // - 1 - 2 comes from the Y axis transform of the icon and label\n          shiftedY1 += fontSize / 2 - 1 - 2;\n        }\n        if (data.label) {\n          const textElem = groupLabelContainer.append('g');\n          await createText(\n            textElem,\n            data.label,\n            {\n              useHtmlLabels: false,\n              width: w,\n              classes: 'architecture-service-label',\n            },\n            getConfig()\n          );\n          textElem\n            .attr('dy', '1em')\n            .attr('alignment-baseline', 'middle')\n            .attr('dominant-baseline', 'start')\n            .attr('text-anchor', 'start');\n\n          textElem.attr(\n            'transform',\n            'translate(' +\n              (shiftedX1 + halfIconSize + 4) +\n              ', ' +\n              (shiftedY1 + halfIconSize + 2) +\n              ')'\n          );\n        }\n      }\n    })\n  );\n};\n\nexport const drawServices = async function (\n  db: ArchitectureDB,\n  elem: D3Element,\n  services: ArchitectureService[]\n): Promise<number> {\n  for (const service of services) {\n    const serviceElem = elem.append('g');\n    const iconSize = getConfigField('iconSize');\n\n    if (service.title) {\n      const textElem = serviceElem.append('g');\n      await createText(\n        textElem,\n        service.title,\n        {\n          useHtmlLabels: false,\n          width: iconSize * 1.5,\n          classes: 'architecture-service-label',\n        },\n        getConfig()\n      );\n\n      textElem\n        .attr('dy', '1em')\n        .attr('alignment-baseline', 'middle')\n        .attr('dominant-baseline', 'middle')\n        .attr('text-anchor', 'middle');\n\n      textElem.attr('transform', 'translate(' + iconSize / 2 + ', ' + iconSize + ')');\n    }\n\n    const bkgElem = serviceElem.append('g');\n    if (service.icon) {\n      // TODO: should a warning be given to end-users saying which icon names are available?\n      // if (!isIconNameInUse(service.icon)) {\n      //   throw new Error(`Invalid SVG Icon name: \"${service.icon}\"`);\n      // }\n      bkgElem.html(\n        `<g>${await getIconSVG(service.icon, { height: iconSize, width: iconSize, fallbackPrefix: architectureIcons.prefix })}</g>`\n      );\n    } else if (service.iconText) {\n      bkgElem.html(\n        `<g>${await getIconSVG('blank', { height: iconSize, width: iconSize, fallbackPrefix: architectureIcons.prefix })}</g>`\n      );\n      const textElemContainer = bkgElem.append('g');\n      const fo = textElemContainer\n        .append('foreignObject')\n        .attr('width', iconSize)\n        .attr('height', iconSize);\n      const divElem = fo\n        .append('div')\n        .attr('class', 'node-icon-text')\n        .attr('style', `height: ${iconSize}px;`)\n        .append('div')\n        .html(service.iconText);\n      const fontSize =\n        parseInt(\n          window\n            .getComputedStyle(divElem.node(), null)\n            .getPropertyValue('font-size')\n            .replace(/\\D/g, '')\n        ) ?? 16;\n      divElem.attr('style', `-webkit-line-clamp: ${Math.floor((iconSize - 2) / fontSize)};`);\n    } else {\n      bkgElem\n        .append('path')\n        .attr('class', 'node-bkg')\n        .attr('id', 'node-' + service.id)\n        .attr(\n          'd',\n          `M0 ${iconSize} v${-iconSize} q0,-5 5,-5 h${iconSize} q5,0 5,5 v${iconSize} H0 Z`\n        );\n    }\n\n    serviceElem.attr('class', 'architecture-service');\n\n    const { width, height } = serviceElem._groups[0][0].getBBox();\n    service.width = width;\n    service.height = height;\n    db.setElementForId(service.id, serviceElem);\n  }\n  return 0;\n};\n\nexport const drawJunctions = function (\n  db: ArchitectureDB,\n  elem: D3Element,\n  junctions: ArchitectureJunction[]\n) {\n  junctions.forEach((junction) => {\n    const junctionElem = elem.append('g');\n    const iconSize = getConfigField('iconSize');\n\n    const bkgElem = junctionElem.append('g');\n    bkgElem\n      .append('rect')\n      .attr('id', 'node-' + junction.id)\n      .attr('fill-opacity', '0')\n      .attr('width', iconSize)\n      .attr('height', iconSize);\n\n    junctionElem.attr('class', 'architecture-junction');\n\n    const { width, height } = junctionElem._groups[0][0].getBBox();\n    junctionElem.width = width;\n    junctionElem.height = height;\n    db.setElementForId(junction.id, junctionElem);\n  });\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { parser } from './architectureParser.js';\nimport { db } from './architectureDb.js';\nimport styles from './architectureStyles.js';\nimport { renderer } from './architectureRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,aAAa;;;AC2Bf,IAAM,4BAA4B;AAAA,EACvC,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AAEO,IAAM,6BAA6B;AAAA,EACxC,GAAG,wBAAC,UAAkB,GAAG,KAAK,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAnD;AAAA,EACH,GAAG,wBAAC,UAAkB,KAAK,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAA9D;AAAA,EACH,GAAG,wBAAC,UAAkB,OAAO,KAAK,MAAM,QAAQ,CAAC,IAAI,KAAK,IAAvD;AAAA,EACH,GAAG,wBAAC,UAAkB,GAAG,QAAQ,CAAC,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK,IAA9D;AACL;AAEO,IAAM,kCAAkC;AAAA,EAC7C,GAAG,wBAAC,MAAc,cAAsB,OAAO,YAAY,GAAxD;AAAA,EACH,GAAG,wBAAC,MAAc,eAAuB,OAAO,GAA7C;AAAA,EACH,GAAG,wBAAC,MAAc,cAAsB,OAAO,YAAY,GAAxD;AAAA,EACH,GAAG,wBAAC,MAAc,eAAuB,OAAO,GAA7C;AACL;AAEO,IAAM,mCAAmC,gCAC9C,GACuB;AACvB,MAAI,yBAAyB,CAAC,GAAG;AAC/B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC3B,OAAO;AACL,WAAO,MAAM,MAAM,MAAM;AAAA,EAC3B;AACF,GARgD;AAUzC,IAAM,0BAA0B,gCAAU,GAAwC;AACvF,QAAM,OAAO;AACb,SAAO,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS;AAClE,GAHuC;AAKhC,IAAM,2BAA2B,gCACtC,GAC6B;AAC7B,QAAM,OAAO;AACb,SAAO,SAAS,OAAO,SAAS;AAClC,GALwC;AAOjC,IAAM,2BAA2B,gCACtC,GAC6B;AAC7B,QAAM,OAAO;AACb,SAAO,SAAS,OAAO,SAAS;AAClC,GALwC;AAOjC,IAAM,4BAA4B,gCACvC,GACA,GACA;AACA,QAAM,QAAQ,yBAAyB,CAAC,KAAK,yBAAyB,CAAC;AACvE,QAAM,QAAQ,yBAAyB,CAAC,KAAK,yBAAyB,CAAC;AACvE,SAAO,SAAS;AAClB,GAPyC;AASlC,IAAM,uBAAuB,gCAClC,MACqC;AACrC,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,QAAQ,yBAAyB,GAAG,KAAK,yBAAyB,GAAG;AAC3E,QAAM,QAAQ,yBAAyB,GAAG,KAAK,yBAAyB,GAAG;AAC3E,SAAO,SAAS;AAClB,GARoC;AAe7B,IAAM,mCAAmC,gCAC9C,GACgC;AAChC,SAAO,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM;AACzD,GAJgD;AAgBzC,IAAM,+BAA+B,gCAC1C,WACA,WACuC;AACvC,QAAM,OAA2D,GAAG,SAAS,GAAG,SAAS;AACzF,SAAO,iCAAiC,IAAI,IAAI,OAAO;AACzD,GAN4C;AAcrC,IAAM,2CAA2C,gCACtD,CAAC,GAAG,CAAC,GACL,MACU;AACV,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,MAAM,KAAK,CAAC;AAClB,MAAI,yBAAyB,GAAG,GAAG;AACjC,QAAI,yBAAyB,GAAG,GAAG;AACjC,aAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,IAAI,KAAK,QAAQ,MAAM,IAAI,GAAG;AAAA,IAChE,OAAO;AACL,aAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,IAAI,CAAC;AAAA,IACvC;AAAA,EACF,OAAO;AACL,QAAI,yBAAyB,GAAG,GAAG;AACjC,aAAO,CAAC,KAAK,QAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,MAAM,IAAI,GAAG;AAAA,IAChE,OAAO;AACL,aAAO,CAAC,GAAG,KAAK,QAAQ,MAAM,IAAI,GAAG;AAAA,IACvC;AAAA,EACF;AACF,GAnBwD;AA0BjD,IAAM,oCAAoC,gCAC/C,MACU;AACV,MAAI,SAAS,QAAQ,SAAS,MAAM;AAClC,WAAO,CAAC,GAAG,CAAC;AAAA,EACd,WAAW,SAAS,QAAQ,SAAS,MAAM;AACzC,WAAO,CAAC,GAAG,EAAE;AAAA,EACf,WAAW,SAAS,QAAQ,SAAS,MAAM;AACzC,WAAO,CAAC,IAAI,EAAE;AAAA,EAChB,OAAO;AACL,WAAO,CAAC,IAAI,CAAC;AAAA,EACf;AACF,GAZiD;AAc1C,IAAM,oCAAoC,gCAC/C,GACA,GACuB;AACvB,MAAI,0BAA0B,GAAG,CAAC,GAAG;AACnC,WAAO;AAAA,EACT,WAAW,yBAAyB,CAAC,GAAG;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AACT,GAViD;AA2C1C,IAAM,wBAAwB,gCAAU,GAA+C;AAC5F,QAAM,OAAO;AACb,SAAO,KAAK,SAAS;AACvB,GAHqC;AAK9B,IAAM,yBAAyB,gCAAU,GAAgD;AAC9F,QAAM,OAAO;AACb,SAAO,KAAK,SAAS;AACvB,GAHsC;AA8F/B,IAAM,WAAW,wBAAC,SAAiC;AACxD,SAAO,KAAK,KAAK;AACnB,GAFwB;AAgDjB,IAAM,WAAW,wBAAC,SAAiC;AACxD,SAAO,KAAK,KAAK;AACnB,GAFwB;;;ACrUxB,IAAM,8BACJ,sBAAe;AAEjB,IAAM,QAAQ,IAAI,gBAAmC,OAAO;AAAA,EAC1D,OAAO,CAAC;AAAA,EACR,QAAQ,CAAC;AAAA,EACT,OAAO,CAAC;AAAA,EACR,eAAe,CAAC;AAAA,EAChB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,UAAU,CAAC;AACb,EAAE;AAEF,IAAMA,SAAQ,6BAAY;AACxB,QAAM,MAAM;AACZ,QAAY;AACd,GAHc;AAKd,IAAM,aAAa,gCAAU;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA;AACF,GAAuC;AACrC,MAAI,MAAM,QAAQ,cAAc,EAAE,MAAM,QAAW;AACjD,UAAM,IAAI;AAAA,MACR,mBAAmB,EAAE,kCAAkC,MAAM,QAAQ,cAAc,EAAE,CAAC;AAAA,IACxF;AAAA,EACF;AACA,MAAI,WAAW,QAAW;AACxB,QAAI,OAAO,QAAQ;AACjB,YAAM,IAAI,MAAM,gBAAgB,EAAE,kCAAkC;AAAA,IACtE;AACA,QAAI,MAAM,QAAQ,cAAc,MAAM,MAAM,QAAW;AACrD,YAAM,IAAI;AAAA,QACR,gBAAgB,EAAE;AAAA,MACpB;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,cAAc,MAAM,MAAM,QAAQ;AAClD,YAAM,IAAI,MAAM,gBAAgB,EAAE,2BAA2B;AAAA,IAC/D;AAAA,EACF;AAEA,QAAM,QAAQ,cAAc,EAAE,IAAI;AAElC,QAAM,QAAQ,MAAM,EAAE,IAAI;AAAA,IACxB;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,IAAI;AAAA,EACN;AACF,GArCmB;AAuCnB,IAAM,cAAc,6BAClB,OAAO,OAAO,MAAM,QAAQ,KAAK,EAAE,OAA4B,qBAAqB,GADlE;AAGpB,IAAM,cAAc,gCAAU,EAAE,IAAI,IAAI,OAAO,GAAwC;AACrF,QAAM,QAAQ,cAAc,EAAE,IAAI;AAElC,QAAM,QAAQ,MAAM,EAAE,IAAI;AAAA,IACxB;AAAA,IACA,MAAM;AAAA,IACN,OAAO,CAAC;AAAA,IACR,IAAI;AAAA,EACN;AACF,GAToB;AAWpB,IAAM,eAAe,6BACnB,OAAO,OAAO,MAAM,QAAQ,KAAK,EAAE,OAA6B,sBAAsB,GADnE;AAGrB,IAAM,WAAW,6BAA0B,OAAO,OAAO,MAAM,QAAQ,KAAK,GAA3D;AAEjB,IAAM,UAAU,wBAAC,OAAwC,MAAM,QAAQ,MAAM,EAAE,GAA/D;AAEhB,IAAM,WAAW,gCAAU,EAAE,IAAI,MAAM,IAAI,QAAQ,MAAM,GAAsB;AAC7E,MAAI,MAAM,QAAQ,cAAc,EAAE,MAAM,QAAW;AACjD,UAAM,IAAI;AAAA,MACR,iBAAiB,EAAE,kCAAkC,MAAM,QAAQ,cAAc,EAAE,CAAC;AAAA,IACtF;AAAA,EACF;AACA,MAAI,WAAW,QAAW;AACxB,QAAI,OAAO,QAAQ;AACjB,YAAM,IAAI,MAAM,cAAc,EAAE,kCAAkC;AAAA,IACpE;AACA,QAAI,MAAM,QAAQ,cAAc,MAAM,MAAM,QAAW;AACrD,YAAM,IAAI;AAAA,QACR,cAAc,EAAE;AAAA,MAClB;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,cAAc,MAAM,MAAM,QAAQ;AAClD,YAAM,IAAI,MAAM,cAAc,EAAE,2BAA2B;AAAA,IAC7D;AAAA,EACF;AAEA,QAAM,QAAQ,cAAc,EAAE,IAAI;AAElC,QAAM,QAAQ,OAAO,EAAE,IAAI;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI;AAAA,EACN;AACF,GA5BiB;AA6BjB,IAAM,YAAY,6BAA2B;AAC3C,SAAO,OAAO,OAAO,MAAM,QAAQ,MAAM;AAC3C,GAFkB;AAIlB,IAAM,UAAU,gCAAU;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAA6B;AAC3B,MAAI,CAAC,wBAAwB,MAAM,GAAG;AACpC,UAAM,IAAI;AAAA,MACR,sDAAsD,KAAK,KAAK,KAAK,4BAA4B,MAAM;AAAA,IACzG;AAAA,EACF;AACA,MAAI,CAAC,wBAAwB,MAAM,GAAG;AACpC,UAAM,IAAI;AAAA,MACR,uDAAuD,KAAK,KAAK,KAAK,4BAA4B,MAAM;AAAA,IAC1G;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,MAAM,KAAK,MAAM,UAAa,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAW;AACzF,UAAM,IAAI;AAAA,MACR,qBAAqB,KAAK;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,MAAM,KAAK,MAAM,UAAa,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAW;AACzF,UAAM,IAAI;AAAA,MACR,sBAAsB,KAAK;AAAA,IAC7B;AAAA,EACF;AAEA,QAAM,aAAa,MAAM,QAAQ,MAAM,KAAK,EAAE;AAC9C,QAAM,aAAa,MAAM,QAAQ,MAAM,KAAK,EAAE;AAC9C,MAAI,YAAY,cAAc,cAAc,cAAc,YAAY;AACpE,UAAM,IAAI;AAAA,MACR,qBAAqB,KAAK;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,YAAY,cAAc,cAAc,cAAc,YAAY;AACpE,UAAM,IAAI;AAAA,MACR,sBAAsB,KAAK;AAAA,IAC7B;AAAA,EACF;AAEA,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,QAAQ,MAAM,KAAK,IAAI;AAC7B,MAAI,MAAM,QAAQ,MAAM,KAAK,KAAK,MAAM,QAAQ,MAAM,KAAK,GAAG;AAC5D,UAAM,QAAQ,MAAM,KAAK,EAAE,MAAM,KAAK,MAAM,QAAQ,MAAM,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAC;AACzF,UAAM,QAAQ,MAAM,KAAK,EAAE,MAAM,KAAK,MAAM,QAAQ,MAAM,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAC;AAAA,EAC3F;AACF,GA/DgB;AAiEhB,IAAM,WAAW,6BAA0B,MAAM,QAAQ,OAAxC;AAOjB,IAAM,oBAAoB,6BAAM;AAC9B,MAAI,MAAM,QAAQ,mBAAmB,QAAW;AAE9C,UAAM,kBAGF,CAAC;AAKL,UAAM,UAAU,OAAO,QAAQ,MAAM,QAAQ,KAAK,EAAE,OAElD,CAAC,WAAW,CAAC,IAAI,OAAO,MAAM;AAC9B,gBAAU,EAAE,IAAI,QAAQ,MAAM,OAAqC,CAAC,WAAW,SAAS;AAEtF,cAAM,aAAa,QAAQ,KAAK,KAAK,GAAG;AACxC,cAAM,aAAa,QAAQ,KAAK,KAAK,GAAG;AACxC,YAAI,cAAc,cAAc,eAAe,YAAY;AACzD,gBAAM,YAAY,kCAAkC,KAAK,QAAQ,KAAK,MAAM;AAC5E,cAAI,cAAc,QAAQ;AACxB,4BAAgB,UAAU,MAAM,CAAC;AACjC,4BAAgB,UAAU,EAAE,UAAU,IAAI;AAC1C,4BAAgB,UAAU,MAAM,CAAC;AACjC,4BAAgB,UAAU,EAAE,UAAU,IAAI;AAAA,UAC5C;AAAA,QACF;AAEA,YAAI,KAAK,UAAU,IAAI;AAErB,gBAAM,OAAO,6BAA6B,KAAK,QAAQ,KAAK,MAAM;AAClE,cAAI,MAAM;AACR,sBAAU,IAAI,IAAI,KAAK;AAAA,UACzB;AAAA,QACF,OAAO;AAEL,gBAAM,OAAO,6BAA6B,KAAK,QAAQ,KAAK,MAAM;AAClE,cAAI,MAAM;AACR,sBAAU,IAAI,IAAI,KAAK;AAAA,UACzB;AAAA,QACF;AACA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAGL,UAAM,UAAU,OAAO,KAAK,OAAO,EAAE,CAAC;AACtC,UAAM,UAAU,EAAE,CAAC,OAAO,GAAG,EAAE;AAE/B,UAAM,aAAa,OAAO,KAAK,OAAO,EAAE;AAAA,MACtC,CAAC,MAAM,OAAQ,OAAO,UAAU,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE;AAAA,MAC1D,CAAC;AAAA,IACH;AAGA,UAAM,MAAM,wBAAC,eAA+C;AAC1D,YAAM,aAAa,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAM,QAAQ,CAAC,UAAU;AACzB,aAAO,MAAM,SAAS,GAAG;AACvB,cAAM,KAAK,MAAM,MAAM;AACvB,YAAI,IAAI;AACN,kBAAQ,EAAE,IAAI;AACd,iBAAO,WAAW,EAAE;AACpB,gBAAM,MAAM,QAAQ,EAAE;AACtB,gBAAM,CAAC,MAAM,IAAI,IAAI,WAAW,EAAE;AAClC,iBAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC5C,gBAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,yBAAW,KAAK,IAAI;AAAA,gBAClB,CAAC,MAAM,IAAI;AAAA,gBACX;AAAA,cACF;AACA,oBAAM,KAAK,KAAK;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAtBY;AAuBZ,UAAM,cAAc,CAAC,IAAI,OAAO,CAAC;AAGjC,WAAO,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AACzC,kBAAY,KAAK,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC,CAAC,CAAC;AAAA,IAClD;AACA,UAAM,QAAQ,iBAAiB;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO,MAAM,QAAQ;AACvB,GA5F0B;AA8F1B,IAAM,kBAAkB,wBAAC,IAAY,YAAuB;AAC1D,QAAM,QAAQ,SAAS,EAAE,IAAI;AAC/B,GAFwB;AAGxB,IAAM,iBAAiB,wBAAC,OAAe,MAAM,QAAQ,SAAS,EAAE,GAAzC;AAEvB,IAAMC,aAAY,6BAA2C;AAC3D,QAAM,SAAS,cAAc;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,UAAgB,EAAE;AAAA,EACvB,CAAC;AACD,SAAO;AACT,GANkB;AAQX,IAAM,KAAqB;AAAA,EAChC,OAAAD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAOO,SAAS,eACd,OACwC;AACxC,SAAOA,WAAU,EAAE,KAAK;AAC1B;AAJgB;;;AF7VhB,IAAM,aAAa,wBAAC,KAAmBC,QAAuB;AAC5D,mBAAiB,KAAKA,GAAE;AACxB,MAAI,OAAO,IAAIA,IAAG,QAAQ;AAC1B,MAAI,SAAS,IAAI,CAAC,YAAYA,IAAG,WAAW,EAAE,GAAG,SAAS,MAAM,UAAU,CAAC,CAAC;AAC5E,MAAI,UAAU,IAAI,CAAC,YAAYA,IAAG,YAAY,EAAE,GAAG,SAAS,MAAM,WAAW,CAAC,CAAC;AAE/E,MAAI,MAAM,IAAIA,IAAG,OAAO;AAC1B,GAPmB;AASZ,IAAM,SAA2B;AAAA,EACtC,OAAO,8BAAO,UAAiC;AAC7C,UAAM,MAAoB,MAAM,MAAM,gBAAgB,KAAK;AAC3D,QAAI,MAAM,GAAG;AACb,eAAW,KAAK,EAAE;AAAA,EACpB,GAJO;AAKT;;;AGpBA,IAAM,YAAmC,wBAAC,YACxC;AAAA;AAAA,oBAEkB,QAAQ,aAAa;AAAA,cAC3B,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,YAKvB,QAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,cAKxB,QAAQ,oBAAoB;AAAA,oBACtB,QAAQ,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAfP;AAkCzC,IAAO,6BAAQ;;;ACnCf,OAAO,eAAe;AAEtB,OAAO,WAAW;AAClB,SAAS,cAAc;;;ACFvB,IAAM,WAAW,wBAAC,SAAiB;AACjC,SAAO,8EAA8E,IAAI;AAC3F,GAFiB;AAIV,IAAM,oBAAiC;AAAA,EAC5C,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,IACL,UAAU;AAAA,MACR,MAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,MACL,MAAM,SAAS,EAAE;AAAA,IACnB;AAAA,EACF;AACF;;;ACnBO,IAAM,YAAY,sCAAgB,SAAoB,IAAoB;AAC/E,QAAM,UAAU,eAAe,SAAS;AACxC,QAAM,WAAW,eAAe,UAAU;AAC1C,QAAM,eAAe,WAAW;AAChC,QAAM,YAAY,WAAW;AAC7B,QAAM,gBAAgB,YAAY;AAElC,QAAM,QAAQ;AAAA,IACZ,GAAG,MAAM,EAAE,IAAI,OAAO,SAAS;AAC7B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,SAAS,IAAI;AACjB,UAAI,EAAE,GAAG,QAAQ,GAAG,OAAO,IAAI,KAAK,CAAC,EAAE,eAAe;AACtD,YAAM,EAAE,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,CAAC,EAAE,SAAS;AAC9C,UAAI,EAAE,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,CAAC,EAAE,eAAe;AAGlD,YAAM,iBAAiB,UAAU;AAEjC,UAAI,aAAa;AACf,YAAI,yBAAyB,SAAS,GAAG;AACvC,oBAAU,cAAc,MAAM,CAAC,iBAAiB;AAAA,QAClD,OAAO;AACL,oBAAU,cAAc,MAAM,CAAC,iBAAiB,iBAAiB;AAAA,QACnE;AAAA,MACF;AAEA,UAAI,aAAa;AACf,YAAI,yBAAyB,SAAS,GAAG;AACvC,kBAAQ,cAAc,MAAM,CAAC,iBAAiB;AAAA,QAChD,OAAO;AACL,kBAAQ,cAAc,MAAM,CAAC,iBAAiB,iBAAiB;AAAA,QACjE;AAAA,MACF;AAGA,UAAI,CAAC,eAAe,GAAG,QAAQ,MAAM,GAAG,SAAS,YAAY;AAC3D,YAAI,yBAAyB,SAAS,GAAG;AACvC,oBAAU,cAAc,MAAM,eAAe,CAAC;AAAA,QAChD,OAAO;AACL,oBAAU,cAAc,MAAM,eAAe,CAAC;AAAA,QAChD;AAAA,MACF;AACA,UAAI,CAAC,eAAe,GAAG,QAAQ,MAAM,GAAG,SAAS,YAAY;AAC3D,YAAI,yBAAyB,SAAS,GAAG;AACvC,kBAAQ,cAAc,MAAM,eAAe,CAAC;AAAA,QAC9C,OAAO;AACL,kBAAQ,cAAc,MAAM,eAAe,CAAC;AAAA,QAC9C;AAAA,MACF;AAEA,UAAI,KAAK,CAAC,EAAE,SAAS,UAAU;AAG7B,cAAM,IAAI,QAAQ,OAAO,GAAG;AAE5B,UAAE,OAAO,MAAM,EACZ,KAAK,KAAK,KAAK,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,EACrE,KAAK,SAAS,MAAM;AAEvB,YAAI,aAAa;AACf,gBAAM,SAAS,yBAAyB,SAAS,IAC7C,gCAAgC,SAAS,EAAE,QAAQ,SAAS,IAC5D,SAAS;AACb,gBAAM,SAAS,yBAAyB,SAAS,IAC7C,gCAAgC,SAAS,EAAE,QAAQ,SAAS,IAC5D,SAAS;AAEb,YAAE,OAAO,SAAS,EACf,KAAK,UAAU,2BAA2B,SAAS,EAAE,SAAS,CAAC,EAC/D,KAAK,aAAa,aAAa,MAAM,IAAI,MAAM,GAAG,EAClD,KAAK,SAAS,OAAO;AAAA,QAC1B;AACA,YAAI,aAAa;AACf,gBAAM,SAAS,yBAAyB,SAAS,IAC7C,gCAAgC,SAAS,EAAE,MAAM,SAAS,IAC1D,OAAO;AACX,gBAAM,SAAS,yBAAyB,SAAS,IAC7C,gCAAgC,SAAS,EAAE,MAAM,SAAS,IAC1D,OAAO;AAEX,YAAE,OAAO,SAAS,EACf,KAAK,UAAU,2BAA2B,SAAS,EAAE,SAAS,CAAC,EAC/D,KAAK,aAAa,aAAa,MAAM,IAAI,MAAM,GAAG,EAClD,KAAK,SAAS,OAAO;AAAA,QAC1B;AAEA,YAAI,OAAO;AACT,gBAAM,OAAO,CAAC,0BAA0B,WAAW,SAAS,IACxD,yBAAyB,SAAS,IAChC,MACA,MACF;AAEJ,cAAI,QAAQ;AACZ,cAAI,SAAS,KAAK;AAChB,oBAAQ,KAAK,IAAI,SAAS,IAAI;AAAA,UAChC,WAAW,SAAS,KAAK;AAEvB,oBAAQ,KAAK,IAAI,SAAS,IAAI,IAAI;AAAA,UACpC,OAAO;AACL,oBAAQ,KAAK,IAAI,SAAS,IAAI,IAAI;AAAA,UACpC;AAEA,gBAAM,WAAW,EAAE,OAAO,GAAG;AAC7B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,cACE,eAAe;AAAA,cACf;AAAA,cACA,SAAS;AAAA,YACX;AAAA,YACAC,WAAU;AAAA,UACZ;AAEA,mBACG,KAAK,MAAM,KAAK,EAChB,KAAK,sBAAsB,QAAQ,EACnC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ;AAE/B,cAAI,SAAS,KAAK;AAChB,qBAAS,KAAK,aAAa,eAAe,OAAO,OAAO,OAAO,GAAG;AAAA,UACpE,WAAW,SAAS,KAAK;AACvB,qBAAS,KAAK,aAAa,eAAe,OAAO,OAAO,OAAO,eAAe;AAAA,UAChF,WAAW,SAAS,MAAM;AACxB,kBAAM,OAAO,6BAA6B,WAAW,SAAS;AAC9D,gBAAI,QAAQ,qBAAqB,IAAI,GAAG;AACtC,oBAAM,WAAW,SAAS,KAAK,EAAE,sBAAsB;AACvD,oBAAM,CAAC,GAAG,CAAC,IAAI,kCAAkC,IAAI;AAErD,uBACG,KAAK,qBAAqB,MAAM,EAChC,KAAK,aAAa,UAAU,KAAK,IAAI,IAAI,EAAE,GAAG;AAGjD,oBAAM,UAAU,SAAS,KAAK,EAAE,sBAAsB;AACtD,uBAAS;AAAA,gBACP;AAAA,gBACA;AAAA,4BACY,IAAI,KAAK,OAAO,SAAS,SAAS,CAAC;AAAA,4BAClC,IAAI,QAAQ,QAAS,CAAC,KAAM,IAAI,QAAQ,SAAU,CAAC;AAAA,yBACvD,KAAK,IAAI,IAAI,EAAE,QAAQ,SAAS,SAAS,CAAC;AAAA;AAAA,cAErD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF,GAhKyB;AAkKlB,IAAM,aAAa,sCAAgB,UAAqB,IAAoB;AACjF,QAAM,UAAU,eAAe,SAAS;AACxC,QAAM,gBAAgB,UAAU;AAEhC,QAAM,WAAW,eAAe,UAAU;AAE1C,QAAM,WAAW,eAAe,UAAU;AAC1C,QAAM,eAAe,WAAW;AAEhC,QAAM,QAAQ;AAAA,IACZ,GAAG,MAAM,EAAE,IAAI,OAAO,SAAS;AAC7B,YAAM,OAAO,SAAS,IAAI;AAC1B,UAAI,KAAK,SAAS,SAAS;AACzB,cAAM,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,KAAK,YAAY;AAE1C,iBACG,OAAO,MAAM,EACb,KAAK,KAAK,KAAK,YAAY,EAC3B,KAAK,KAAK,KAAK,YAAY,EAC3B,KAAK,SAAS,CAAC,EACf,KAAK,UAAU,CAAC,EAChB,KAAK,SAAS,UAAU;AAE3B,cAAM,sBAAsB,SAAS,OAAO,GAAG;AAC/C,YAAI,YAAY;AAChB,YAAI,YAAY;AAChB,YAAI,KAAK,MAAM;AACb,gBAAM,UAAU,oBAAoB,OAAO,GAAG;AAC9C,kBAAQ;AAAA,YACN,MAAM,MAAM,WAAW,KAAK,MAAM,EAAE,QAAQ,eAAe,OAAO,eAAe,gBAAgB,kBAAkB,OAAO,CAAC,CAAC;AAAA,UAC9H;AACA,kBAAQ;AAAA,YACN;AAAA,YACA,gBACG,YAAY,eAAe,KAC5B,QACC,YAAY,eAAe,KAC5B;AAAA,UACJ;AACA,uBAAa;AAGb,uBAAa,WAAW,IAAI,IAAI;AAAA,QAClC;AACA,YAAI,KAAK,OAAO;AACd,gBAAM,WAAW,oBAAoB,OAAO,GAAG;AAC/C,gBAAM;AAAA,YACJ;AAAA,YACA,KAAK;AAAA,YACL;AAAA,cACE,eAAe;AAAA,cACf,OAAO;AAAA,cACP,SAAS;AAAA,YACX;AAAA,YACAA,WAAU;AAAA,UACZ;AACA,mBACG,KAAK,MAAM,KAAK,EAChB,KAAK,sBAAsB,QAAQ,EACnC,KAAK,qBAAqB,OAAO,EACjC,KAAK,eAAe,OAAO;AAE9B,mBAAS;AAAA,YACP;AAAA,YACA,gBACG,YAAY,eAAe,KAC5B,QACC,YAAY,eAAe,KAC5B;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF,GA1E0B;AA4EnB,IAAM,eAAe,sCAC1BC,KACA,MACA,UACiB;AACjB,aAAW,WAAW,UAAU;AAC9B,UAAM,cAAc,KAAK,OAAO,GAAG;AACnC,UAAM,WAAW,eAAe,UAAU;AAE1C,QAAI,QAAQ,OAAO;AACjB,YAAM,WAAW,YAAY,OAAO,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,UACE,eAAe;AAAA,UACf,OAAO,WAAW;AAAA,UAClB,SAAS;AAAA,QACX;AAAA,QACAD,WAAU;AAAA,MACZ;AAEA,eACG,KAAK,MAAM,KAAK,EAChB,KAAK,sBAAsB,QAAQ,EACnC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ;AAE/B,eAAS,KAAK,aAAa,eAAe,WAAW,IAAI,OAAO,WAAW,GAAG;AAAA,IAChF;AAEA,UAAM,UAAU,YAAY,OAAO,GAAG;AACtC,QAAI,QAAQ,MAAM;AAKhB,cAAQ;AAAA,QACN,MAAM,MAAM,WAAW,QAAQ,MAAM,EAAE,QAAQ,UAAU,OAAO,UAAU,gBAAgB,kBAAkB,OAAO,CAAC,CAAC;AAAA,MACvH;AAAA,IACF,WAAW,QAAQ,UAAU;AAC3B,cAAQ;AAAA,QACN,MAAM,MAAM,WAAW,SAAS,EAAE,QAAQ,UAAU,OAAO,UAAU,gBAAgB,kBAAkB,OAAO,CAAC,CAAC;AAAA,MAClH;AACA,YAAM,oBAAoB,QAAQ,OAAO,GAAG;AAC5C,YAAM,KAAK,kBACR,OAAO,eAAe,EACtB,KAAK,SAAS,QAAQ,EACtB,KAAK,UAAU,QAAQ;AAC1B,YAAM,UAAU,GACb,OAAO,KAAK,EACZ,KAAK,SAAS,gBAAgB,EAC9B,KAAK,SAAS,WAAW,QAAQ,KAAK,EACtC,OAAO,KAAK,EACZ,KAAK,QAAQ,QAAQ;AACxB,YAAM,WACJ;AAAA,QACE,OACG,iBAAiB,QAAQ,KAAK,GAAG,IAAI,EACrC,iBAAiB,WAAW,EAC5B,QAAQ,OAAO,EAAE;AAAA,MACtB,KAAK;AACP,cAAQ,KAAK,SAAS,uBAAuB,KAAK,OAAO,WAAW,KAAK,QAAQ,CAAC,GAAG;AAAA,IACvF,OAAO;AACL,cACG,OAAO,MAAM,EACb,KAAK,SAAS,UAAU,EACxB,KAAK,MAAM,UAAU,QAAQ,EAAE,EAC/B;AAAA,QACC;AAAA,QACA,MAAM,QAAQ,KAAK,CAAC,QAAQ,gBAAgB,QAAQ,cAAc,QAAQ;AAAA,MAC5E;AAAA,IACJ;AAEA,gBAAY,KAAK,SAAS,sBAAsB;AAEhD,UAAM,EAAE,OAAO,OAAO,IAAI,YAAY,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ;AAC5D,YAAQ,QAAQ;AAChB,YAAQ,SAAS;AACjB,IAAAC,IAAG,gBAAgB,QAAQ,IAAI,WAAW;AAAA,EAC5C;AACA,SAAO;AACT,GAlF4B;AAoFrB,IAAM,gBAAgB,gCAC3BA,KACA,MACA,WACA;AACA,YAAU,QAAQ,CAAC,aAAa;AAC9B,UAAM,eAAe,KAAK,OAAO,GAAG;AACpC,UAAM,WAAW,eAAe,UAAU;AAE1C,UAAM,UAAU,aAAa,OAAO,GAAG;AACvC,YACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,SAAS,EAAE,EAChC,KAAK,gBAAgB,GAAG,EACxB,KAAK,SAAS,QAAQ,EACtB,KAAK,UAAU,QAAQ;AAE1B,iBAAa,KAAK,SAAS,uBAAuB;AAElD,UAAM,EAAE,OAAO,OAAO,IAAI,aAAa,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ;AAC7D,iBAAa,QAAQ;AACrB,iBAAa,SAAS;AACtB,IAAAA,IAAG,gBAAgB,SAAS,IAAI,YAAY;AAAA,EAC9C,CAAC;AACH,GAxB6B;;;AFnT7B,kBAAkB;AAAA,EAChB;AAAA,IACE,MAAM,kBAAkB;AAAA,IACxB,OAAO;AAAA,EACT;AACF,CAAC;AACD,UAAU,IAAI,KAAK;AAEnB,SAAS,YAAY,UAAiC,IAAoB;AACxE,WAAS,QAAQ,CAAC,YAAY;AAC5B,OAAG,IAAI;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,IAAI,QAAQ;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,QAAQ,QAAQ;AAAA,QAChB,OAAO,eAAe,UAAU;AAAA,QAChC,QAAQ,eAAe,UAAU;AAAA,MACnC;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC;AACH;AAhBS;AAkBT,SAAS,aAAa,WAAmC,IAAoB;AAC3E,YAAU,QAAQ,CAAC,aAAa;AAC9B,OAAG,IAAI;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,IAAI,SAAS;AAAA,QACb,QAAQ,SAAS;AAAA,QACjB,OAAO,eAAe,UAAU;AAAA,QAChC,QAAQ,eAAe,UAAU;AAAA,MACnC;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC;AACH;AAdS;AAgBT,SAAS,cAAcC,KAAoB,IAAoB;AAC7D,KAAG,MAAM,EAAE,IAAI,CAAC,SAAS;AACvB,UAAM,OAAO,SAAS,IAAI;AAC1B,QAAI,KAAK,SAAS,SAAS;AACzB;AAAA,IACF;AACA,SAAK,IAAI,KAAK,SAAS,EAAE;AACzB,SAAK,IAAI,KAAK,SAAS,EAAE;AAEzB,UAAM,WAAWA,IAAG,eAAe,KAAK,EAAE;AAC1C,aAAS,KAAK,aAAa,gBAAgB,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG;AAAA,EACrF,CAAC;AACH;AAZS;AAcT,SAAS,UAAU,QAA6B,IAAoB;AAClE,SAAO,QAAQ,CAAC,UAAU;AACxB,OAAG,IAAI;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,IAAI,MAAM;AAAA,QACV,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,QACb,QAAQ,MAAM;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC;AACH;AAdS;AAgBT,SAAS,SAAS,OAA2B,IAAoB;AAC/D,QAAM,QAAQ,CAAC,eAAe;AAC5B,UAAM,EAAE,OAAO,OAAO,SAAS,UAAU,SAAS,QAAQ,QAAQ,UAAU,MAAM,IAChF;AACF,UAAM,WAAW,0BAA0B,WAAW,QAAQ,WAAW,MAAM,IAC3E,aACA;AACJ,UAAM,OAAyB;AAAA,MAC7B,IAAI,GAAG,KAAK,IAAI,KAAK;AAAA,MACrB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,aAAa;AAAA,MACb,gBACE,WAAW,MACP,UACA,WAAW,MACT,aACA,WAAW,MACT,UACA;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,aAAa;AAAA,MACb,gBACE,WAAW,MACP,UACA,WAAW,MACT,aACA,WAAW,MACT,UACA;AAAA,IACZ;AACA,OAAG,IAAI;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC;AACH;AAzCS;AA2CT,SAAS,cACPA,KACA,aACA,iBACgC;AAYhC,QAAM,oBAAoB,wBACxB,cACA,iBAC6B;AAC7B,WAAO,OAAO,QAAQ,YAAY,EAAE;AAAA,MAClC,CAAC,MAAM,CAAC,KAAKC,WAAU,MAAM;AAE3B,YAAI,MAAM;AACV,cAAM,MAAM,OAAO,QAAQA,WAAU;AACrC,YAAI,IAAI,WAAW,GAAG;AAEpB,eAAK,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;AACpB,iBAAO;AAAA,QACT;AACA,iBAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,KAAK;AACvC,mBAAS,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACvC,kBAAM,CAAC,UAAU,QAAQ,IAAI,IAAI,CAAC;AAClC,kBAAM,CAAC,UAAU,QAAQ,IAAI,IAAI,CAAC;AAClC,kBAAM,YAAY,gBAAgB,QAAQ,IAAI,QAAQ;AAEtD,gBAAI,cAAc,cAAc;AAE9B,mBAAK,GAAG,MAAM,CAAC;AACf,mBAAK,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,UAAU,GAAG,QAAQ;AAAA,YACrD,WAAW,aAAa,aAAa,aAAa,WAAW;AAE3D,mBAAK,GAAG,MAAM,CAAC;AACf,mBAAK,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,UAAU,GAAG,QAAQ;AAAA,YACrD,OAAO;AAEL,oBAAM,OAAO,GAAG,GAAG,IAAI,KAAK;AAC5B,mBAAK,IAAI,IAAI;AACb,oBAAM,OAAO,GAAG,GAAG,IAAI,KAAK;AAC5B,mBAAK,IAAI,IAAI;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,MACA,CAAC;AAAA,IACH;AAAA,EACF,GA1C0B;AA4C1B,QAAM,aAAa,YAAY,IAAI,CAAC,eAAe;AACjD,UAAM,uBAAiE,CAAC;AACxE,UAAM,qBAA+D,CAAC;AAGtE,WAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM;AACnD,YAAM,YAAYD,IAAG,QAAQ,EAAE,GAAG,MAAM;AAExC,2BAAqB,CAAC,MAAM,CAAC;AAC7B,2BAAqB,CAAC,EAAE,SAAS,MAAM,CAAC;AACxC,2BAAqB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE;AAE1C,yBAAmB,CAAC,MAAM,CAAC;AAC3B,yBAAmB,CAAC,EAAE,SAAS,MAAM,CAAC;AACtC,yBAAmB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE;AAAA,IAC1C,CAAC;AAGD,WAAO;AAAA,MACL,OAAO,OAAO,OAAO,kBAAkB,sBAAsB,YAAY,CAAC,EAAE;AAAA,QAC1E,CAAC,QAAQ,IAAI,SAAS;AAAA,MACxB;AAAA,MACA,MAAM,OAAO,OAAO,kBAAkB,oBAAoB,UAAU,CAAC,EAAE;AAAA,QACrE,CAAC,QAAQ,IAAI,SAAS;AAAA,MACxB;AAAA,IACF;AAAA,EACF,CAAC;AAGD,QAAM,CAAC,YAAY,QAAQ,IAAI,WAAW;AAAA,IACxC,CAAC,CAAC,WAAW,QAAQ,GAAG,EAAE,OAAO,KAAK,MAAM;AAC1C,aAAO;AAAA,QACL,CAAC,GAAG,WAAW,GAAG,KAAK;AAAA,QACvB,CAAC,GAAG,UAAU,GAAG,IAAI;AAAA,MACvB;AAAA,IACF;AAAA,IACA,CAAC,CAAC,GAAiB,CAAC,CAAe;AAAA,EACrC;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAvGS;AAyGT,SAAS,uBACP,aAC0C;AAC1C,QAAM,sBAAgE,CAAC;AACvE,QAAM,WAAW,wBAAC,QAAkB,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAtC;AACjB,QAAM,WAAW,wBAAC,QAAgB,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC,GAAtD;AAEjB,cAAY,QAAQ,CAAC,eAAe;AAClC,UAAM,gBAAgB,OAAO;AAAA,MAC3B,OAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;AAAA,IACnE;AAGA,UAAM,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,UAAM,UAAkC,CAAC;AACzC,UAAM,aAAsD;AAAA,MAC1D,GAAG,CAAC,IAAI,CAAC;AAAA,MACT,GAAG,CAAC,GAAG,CAAC;AAAA,MACR,GAAG,CAAC,GAAG,CAAC;AAAA,MACR,GAAG,CAAC,GAAG,EAAE;AAAA,IACX;AACA,WAAO,MAAM,SAAS,GAAG;AACvB,YAAM,OAAO,MAAM,MAAM;AACzB,UAAI,MAAM;AACR,gBAAQ,IAAI,IAAI;AAChB,cAAM,SAAS,cAAc,IAAI;AACjC,YAAI,QAAQ;AACV,gBAAM,UAAU,SAAS,IAAI;AAC7B,iBAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACnD,kBAAM,SAAS,SAAS,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC;AACtE,kBAAM,QAAQ,cAAc,MAAM;AAElC,gBAAI,SAAS,CAAC,QAAQ,MAAM,GAAG;AAC7B,oBAAM,KAAK,MAAM;AAEjB,kCAAoB,KAAK;AAAA,gBACvB,CAAC,0BAA0B,GAA4B,CAAC,GAAG;AAAA,gBAC3D,CAAC,0BACC,iCAAiC,GAA4B,CAC/D,CAAC,GAAG;AAAA,gBACJ,KAAK,MAAM,eAAe,UAAU;AAAA,cACtC,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAjDS;AAmDT,SAAS,mBACP,UACA,WACA,QACA,OACAA,KACA,EAAE,aAAa,gBAAgB,GACN;AACzB,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,UAAM,WAAW,OAAO,MAAM,EAAE,OAAO,KAAK,EAAE,KAAK,MAAM,IAAI,EAAE,KAAK,SAAS,cAAc;AAC3F,UAAM,KAAK,UAAU;AAAA,MACnB,WAAW,SAAS,eAAe,IAAI;AAAA,MACvC,OAAO;AAAA,QACL;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA,YACL,eAAe;AAAA,YACf,OAAO;AAAA,YACP,mBAAmB;AAAA,YACnB,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA,YACL,eAAe;AAAA,YACf,mBAAmB;AAAA,YACnB,qBAAqB,CAAC,GAAG;AAAA;AAAA,YAEzB,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA;AAAA,YAEL,8BAA8B;AAAA,UAChC;AAAA,QACF;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA,YACL,eAAe;AAAA,YACf,eAAe;AAAA,YACf,aAAa,GAAG,eAAe,UAAU,CAAC;AAAA,UAC5C;AAAA,QACF;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA,YACL,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA;AAAA,YAEL,SAAS,GAAG,eAAe,SAAS,CAAC;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,UACX,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,QACN;AAAA,MACF;AAAA,IACF,CAAC;AAED,aAAS,OAAO;AAEhB,cAAU,QAAQ,EAAE;AACpB,gBAAY,UAAU,EAAE;AACxB,iBAAa,WAAW,EAAE;AAC1B,aAAS,OAAO,EAAE;AAElB,UAAM,sBAAsB,cAAcA,KAAI,aAAa,eAAe;AAG1E,UAAM,8BAA8B,uBAAuB,WAAW;AAEtE,UAAM,SAAS,GAAG,OAAO;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,MACT,6BAA6B;AAAA;AAAA;AAAA,MAG7B,gBAAgB,MAAoB;AAClC,cAAM,CAAC,OAAO,KAAK,IAAI,KAAK,eAAe;AAC3C,cAAM,EAAE,QAAQ,QAAQ,IAAI,SAAS,KAAK;AAC1C,cAAM,EAAE,QAAQ,QAAQ,IAAI,SAAS,KAAK;AAC1C,cAAM,aACJ,YAAY,UAAU,MAAM,eAAe,UAAU,IAAI,MAAM,eAAe,UAAU;AAC1F,eAAO;AAAA,MACT;AAAA,MACA,eAAe,MAAoB;AACjC,cAAM,CAAC,OAAO,KAAK,IAAI,KAAK,eAAe;AAC3C,cAAM,EAAE,QAAQ,QAAQ,IAAI,SAAS,KAAK;AAC1C,cAAM,EAAE,QAAQ,QAAQ,IAAI,SAAS,KAAK;AAC1C,cAAM,aAAa,YAAY,UAAU,OAAO;AAChD,eAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAuB;AAGvB,WAAO,IAAI,cAAc,MAAM;AAC7B,eAAS,kBACP,QACA,QACA,QACA,QACA;AACA,YAAI,GAAG;AACP,cAAM,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI;AACzB,cAAM,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI;AAEzB,aACG,SAAS,MAAO,KAAK,WAAW,KAAK,OAAQ,KAAK,OACnD,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC;AAClD,YAAI,KAAK,KAAK,KAAK,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAElF,cAAM,SAAS,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AACpE,YAAI,IAAI;AAGR,YAAI,UAAU,KAAK,OAAO,SAAS,OAAO,KAAK,OAAO,SAAS;AAC/D,gBAAQ,MAAM;AAAA,UACZ,KAAK,UAAU;AACb,qBAAS;AACT;AAAA,UACF,KAAK,SAAS;AACZ,qBAAS;AACT;AAAA,QACJ;AAEA,YAAI,UAAU,KAAK,OAAO,SAAS,OAAO,KAAK,OAAO,SAAS;AAC/D,gBAAQ,MAAM;AAAA,UACZ,KAAK,UAAU;AACb,qBAAS;AACT;AAAA,UACF,KAAK,SAAS;AACZ,qBAAS;AACT;AAAA,QACJ;AAEA,YAAI,KAAK,IAAI,CAAC,IAAI;AAClB,YAAI,IAAI;AAER,eAAO;AAAA,UACL,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,MACF;AA9CS;AA+CT,SAAG,WAAW;AACd,iBAAW,QAAQ,OAAO,OAAO,GAAG,MAAM,CAAC,GAAG;AAC5C,YAAI,KAAK,OAAO,GAAG;AACjB,gBAAM,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,KAAK,OAAO,EAAE,SAAS;AAChD,gBAAM,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,KAAK,OAAO,EAAE,SAAS;AAChD,cAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,kBAAM,MAAM,KAAK,eAAe;AAChC,kBAAM,MAAM,KAAK,eAAe;AAChC,kBAAM,EAAE,UAAU,IAAI,SAAS,IAAI;AACnC,kBAAM,CAAC,QAAQ,MAAM,IAAI,yBAAyB,SAAS,IACvD,CAAC,IAAI,GAAG,IAAI,CAAC,IACb,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,kBAAM,EAAE,SAAS,UAAU,IAAI,kBAAkB,KAAK,KAAK,QAAQ,MAAM;AACzE,iBAAK,MAAM,qBAAqB,SAAS;AACzC,iBAAK,MAAM,mBAAmB,OAAO;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AACA,SAAG,SAAS;AACZ,aAAO,IAAI;AAAA,IACb,CAAC;AACD,WAAO,IAAI;AAEX,OAAG,MAAM,CAAC,MAAM;AACd,UAAI,KAAK,SAAS,CAAC;AACnB,cAAQ,EAAE;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAvMS;AAyMF,IAAM,OAAuB,8BAAO,MAAM,IAAI,UAAU,YAAqB;AAGlF,QAAMA,MAAK,QAAQ;AAEnB,QAAM,WAAWA,IAAG,YAAY;AAChC,QAAM,YAAYA,IAAG,aAAa;AAClC,QAAM,SAASA,IAAG,UAAU;AAC5B,QAAM,QAAQA,IAAG,SAAS;AAC1B,QAAM,KAAKA,IAAG,kBAAkB;AAEhC,QAAM,MAAW,iBAAiB,EAAE;AAEpC,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,YAAU,KAAK,SAAS,oBAAoB;AAE5C,QAAM,eAAe,IAAI,OAAO,GAAG;AACnC,eAAa,KAAK,SAAS,uBAAuB;AAElD,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,YAAU,KAAK,SAAS,qBAAqB;AAE7C,QAAM,aAAaA,KAAI,cAAc,QAAQ;AAC7C,gBAAcA,KAAI,cAAc,SAAS;AAEzC,QAAM,KAAK,MAAM,mBAAmB,UAAU,WAAW,QAAQ,OAAOA,KAAI,EAAE;AAE9E,QAAM,UAAU,WAAW,EAAE;AAC7B,QAAM,WAAW,WAAW,EAAE;AAC9B,gBAAcA,KAAI,EAAE;AAEpB,oBAAkB,QAAW,KAAK,eAAe,SAAS,GAAG,eAAe,aAAa,CAAC;AAC5F,GAhCoC;AAkC7B,IAAM,WAAW,EAAE,KAAK;;;AG1hBxB,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["clear", "getConfig", "db", "getConfig", "db", "db", "alignments"]}