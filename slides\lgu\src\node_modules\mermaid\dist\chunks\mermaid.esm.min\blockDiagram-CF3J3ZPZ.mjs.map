{"version": 3, "sources": ["../../../src/diagrams/block/parser/block.jison", "../../../src/diagrams/block/blockDB.ts", "../../../src/diagrams/block/styles.ts", "../../../src/dagre-wrapper/markers.js", "../../../src/diagrams/block/layout.ts", "../../../src/dagre-wrapper/createLabel.js", "../../../src/dagre-wrapper/edgeMarker.ts", "../../../src/dagre-wrapper/edges.js", "../../../src/dagre-wrapper/blockArrowHelper.ts", "../../../src/dagre-wrapper/intersect/intersect-node.js", "../../../src/dagre-wrapper/intersect/intersect-ellipse.js", "../../../src/dagre-wrapper/intersect/intersect-circle.js", "../../../src/dagre-wrapper/intersect/intersect-line.js", "../../../src/dagre-wrapper/intersect/intersect-polygon.js", "../../../src/dagre-wrapper/intersect/intersect-rect.js", "../../../src/dagre-wrapper/intersect/index.js", "../../../src/dagre-wrapper/shapes/util.js", "../../../src/dagre-wrapper/shapes/note.js", "../../../src/dagre-wrapper/nodes.js", "../../../src/diagrams/block/renderHelpers.ts", "../../../src/diagrams/block/blockRenderer.ts", "../../../src/diagrams/block/blockDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,7],$V1=[1,13],$V2=[1,14],$V3=[1,15],$V4=[1,19],$V5=[1,16],$V6=[1,17],$V7=[1,18],$V8=[8,30],$V9=[8,21,28,29,30,31,32,40,44,47],$Va=[1,23],$Vb=[1,24],$Vc=[8,15,16,21,28,29,30,31,32,40,44,47],$Vd=[8,15,16,21,27,28,29,30,31,32,40,44,47],$Ve=[1,49];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"spaceLines\":3,\"SPACELINE\":4,\"NL\":5,\"separator\":6,\"SPACE\":7,\"EOF\":8,\"start\":9,\"BLOCK_DIAGRAM_KEY\":10,\"document\":11,\"stop\":12,\"statement\":13,\"link\":14,\"LINK\":15,\"START_LINK\":16,\"LINK_LABEL\":17,\"STR\":18,\"nodeStatement\":19,\"columnsStatement\":20,\"SPACE_BLOCK\":21,\"blockStatement\":22,\"classDefStatement\":23,\"cssClassStatement\":24,\"styleStatement\":25,\"node\":26,\"SIZE\":27,\"COLUMNS\":28,\"id-block\":29,\"end\":30,\"block\":31,\"NODE_ID\":32,\"nodeShapeNLabel\":33,\"dirList\":34,\"DIR\":35,\"NODE_DSTART\":36,\"NODE_DEND\":37,\"BLOCK_ARROW_START\":38,\"BLOCK_ARROW_END\":39,\"classDef\":40,\"CLASSDEF_ID\":41,\"CLASSDEF_STYLEOPTS\":42,\"DEFAULT\":43,\"class\":44,\"CLASSENTITY_IDS\":45,\"STYLECLASS\":46,\"style\":47,\"STYLE_ENTITY_IDS\":48,\"STYLE_DEFINITION_DATA\":49,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SPACELINE\",5:\"NL\",7:\"SPACE\",8:\"EOF\",10:\"BLOCK_DIAGRAM_KEY\",15:\"LINK\",16:\"START_LINK\",17:\"LINK_LABEL\",18:\"STR\",21:\"SPACE_BLOCK\",27:\"SIZE\",28:\"COLUMNS\",29:\"id-block\",30:\"end\",31:\"block\",32:\"NODE_ID\",35:\"DIR\",36:\"NODE_DSTART\",37:\"NODE_DEND\",38:\"BLOCK_ARROW_START\",39:\"BLOCK_ARROW_END\",40:\"classDef\",41:\"CLASSDEF_ID\",42:\"CLASSDEF_STYLEOPTS\",43:\"DEFAULT\",44:\"class\",45:\"CLASSENTITY_IDS\",46:\"STYLECLASS\",47:\"style\",48:\"STYLE_ENTITY_IDS\",49:\"STYLE_DEFINITION_DATA\"},\nproductions_: [0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 4:\nyy.getLogger().debug('Rule: separator (NL) ');\nbreak;\ncase 5:\nyy.getLogger().debug('Rule: separator (Space) ');\nbreak;\ncase 6:\nyy.getLogger().debug('Rule: separator (EOF) ');\nbreak;\ncase 7:\n yy.getLogger().debug(\"Rule: hierarchy: \", $$[$0-1]); yy.setHierarchy($$[$0-1]); \nbreak;\ncase 8:\nyy.getLogger().debug('Stop NL ');\nbreak;\ncase 9:\nyy.getLogger().debug('Stop EOF ');\nbreak;\ncase 10:\nyy.getLogger().debug('Stop NL2 ');\nbreak;\ncase 11:\nyy.getLogger().debug('Stop EOF2 ');\nbreak;\ncase 12:\n yy.getLogger().debug(\"Rule: statement: \", $$[$0]); typeof $$[$0].length === 'number'?this.$ = $$[$0]:this.$ = [$$[$0]]; \nbreak;\ncase 13:\n yy.getLogger().debug(\"Rule: statement #2: \", $$[$0-1]); this.$ = [$$[$0-1]].concat($$[$0]); \nbreak;\ncase 14:\n yy.getLogger().debug(\"Rule: link: \", $$[$0], yytext); this.$={edgeTypeStr: $$[$0], label:''}; \nbreak;\ncase 15:\n yy.getLogger().debug(\"Rule: LABEL link: \", $$[$0-3], $$[$0-1], $$[$0]); this.$={edgeTypeStr: $$[$0], label:$$[$0-1]}; \nbreak;\ncase 18:\n const num=parseInt($$[$0]); const spaceId = yy.generateId(); this.$ = { id: spaceId, type:'space', label:'', width: num, children: [] }\nbreak;\ncase 23:\n\n    yy.getLogger().debug('Rule: (nodeStatement link node) ', $$[$0-2], $$[$0-1], $$[$0], ' typestr: ',$$[$0-1].edgeTypeStr);\n    const edgeData = yy.edgeStrToEdgeData($$[$0-1].edgeTypeStr)\n    this.$ = [\n      {id: $$[$0-2].id, label: $$[$0-2].label, type:$$[$0-2].type, directions: $$[$0-2].directions},\n      {id: $$[$0-2].id + '-' + $$[$0].id, start: $$[$0-2].id, end: $$[$0].id, label: $$[$0-1].label, type: 'edge', directions: $$[$0].directions, arrowTypeEnd: edgeData, arrowTypeStart: 'arrow_open' },\n      {id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions}\n      ];\n    \nbreak;\ncase 24:\n yy.getLogger().debug('Rule: nodeStatement (abc88 node size) ', $$[$0-1], $$[$0]); this.$ = {id: $$[$0-1].id, label: $$[$0-1].label, type: yy.typeStr2Type($$[$0-1].typeStr), directions: $$[$0-1].directions, widthInColumns: parseInt($$[$0],10)}; \nbreak;\ncase 25:\n yy.getLogger().debug('Rule: nodeStatement (node) ', $$[$0]); this.$ = {id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions, widthInColumns:1}; \nbreak;\ncase 26:\n yy.getLogger().debug('APA123', this? this:'na'); yy.getLogger().debug(\"COLUMNS: \", $$[$0]); this.$ = {type: 'column-setting', columns: $$[$0] === 'auto'?-1:parseInt($$[$0]) } \nbreak;\ncase 27:\n yy.getLogger().debug('Rule: id-block statement : ', $$[$0-2], $$[$0-1]); const id2 = yy.generateId(); this.$ = { ...$$[$0-2], type:'composite', children: $$[$0-1] }; \nbreak;\ncase 28:\n yy.getLogger().debug('Rule: blockStatement : ', $$[$0-2], $$[$0-1], $$[$0]); const id = yy.generateId(); this.$ = { id, type:'composite', label:'', children: $$[$0-1] }; \nbreak;\ncase 29:\n yy.getLogger().debug(\"Rule: node (NODE_ID separator): \", $$[$0]); this.$ = { id: $$[$0] }; \nbreak;\ncase 30:\n\n    yy.getLogger().debug(\"Rule: node (NODE_ID nodeShapeNLabel separator): \", $$[$0-1], $$[$0]);\n    this.$ = { id: $$[$0-1], label: $$[$0].label, typeStr: $$[$0].typeStr, directions: $$[$0].directions };\n  \nbreak;\ncase 31:\n yy.getLogger().debug(\"Rule: dirList: \", $$[$0]); this.$ = [$$[$0]]; \nbreak;\ncase 32:\n yy.getLogger().debug(\"Rule: dirList: \", $$[$0-1], $$[$0]); this.$ = [$$[$0-1]].concat($$[$0]); \nbreak;\ncase 33:\n yy.getLogger().debug(\"Rule: nodeShapeNLabel: \", $$[$0-2], $$[$0-1], $$[$0]); this.$ = { typeStr: $$[$0-2] + $$[$0], label: $$[$0-1] }; \nbreak;\ncase 34:\n yy.getLogger().debug(\"Rule: BLOCK_ARROW nodeShapeNLabel: \", $$[$0-3], $$[$0-2], \" #3:\",$$[$0-1], $$[$0]); this.$ = { typeStr: $$[$0-3] + $$[$0], label: $$[$0-2], directions: $$[$0-1]}; \nbreak;\ncase 35: case 36:\n\n      this.$ = { type: 'classDef', id: $$[$0-1].trim(), css: $$[$0].trim() };\n      \nbreak;\ncase 37:\n\n        //log.debug('apply class: id(s): ',$$[$0-1], '  style class: ', $$[$0]);\n        this.$={ type: 'applyClass', id: $$[$0-1].trim(), styleClass: $$[$0].trim() };\n        \nbreak;\ncase 38:\n\n        this.$={ type: 'applyStyles', id: $$[$0-1].trim(), stylesStr: $$[$0].trim() };\n        \nbreak;\n}\n},\ntable: [{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:$V0,22:8,23:9,24:10,25:11,26:12,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7},{8:[1,20]},o($V8,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:$V0,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7}),o($V9,[2,16],{14:22,15:$Va,16:$Vb}),o($V9,[2,17]),o($V9,[2,18]),o($V9,[2,19]),o($V9,[2,20]),o($V9,[2,21]),o($V9,[2,22]),o($Vc,[2,25],{27:[1,25]}),o($V9,[2,26]),{19:26,26:12,32:$V4},{11:27,13:4,19:5,20:6,21:$V0,22:8,23:9,24:10,25:11,26:12,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},o($Vd,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},o($V8,[2,13]),{26:35,32:$V4},{32:[2,14]},{17:[1,36]},o($Vc,[2,24]),{11:37,13:4,14:22,15:$Va,16:$Vb,19:5,20:6,21:$V0,22:8,23:9,24:10,25:11,26:12,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},o($Vd,[2,30]),{18:[1,43]},{18:[1,44]},o($Vc,[2,23]),{18:[1,45]},{30:[1,46]},o($V9,[2,28]),o($V9,[2,35]),o($V9,[2,36]),o($V9,[2,37]),o($V9,[2,38]),{37:[1,47]},{34:48,35:$Ve},{15:[1,50]},o($V9,[2,27]),o($Vd,[2,33]),{39:[1,51]},{34:52,35:$Ve,39:[2,31]},{32:[2,15]},o($Vd,[2,34]),{39:[2,32]}],\ndefaultActions: {20:[2,7],23:[2,14],50:[2,15],52:[2,32]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0: return 10; \nbreak;\ncase 1: yy.getLogger().debug('Found space-block'); return 31;\nbreak;\ncase 2: yy.getLogger().debug('Found nl-block'); return 31;\nbreak;\ncase 3: yy.getLogger().debug('Found space-block'); return 29;\nbreak;\ncase 4: yy.getLogger().debug('.', yy_.yytext); /* skip all whitespace */  \nbreak;\ncase 5:yy.getLogger().debug('_', yy_.yytext);                 /* skip all whitespace */   \nbreak;\ncase 6: return 5 \nbreak;\ncase 7: yy_.yytext=-1; return 28; \nbreak;\ncase 8: yy_.yytext = yy_.yytext.replace(/columns\\s+/,''); yy.getLogger().debug('COLUMNS (LEX)', yy_.yytext); return 28; \nbreak;\ncase 9: this.pushState(\"md_string\");\nbreak;\ncase 10: return \"MD_STR\";\nbreak;\ncase 11: this.popState();\nbreak;\ncase 12:this.pushState(\"string\");\nbreak;\ncase 13: yy.getLogger().debug('LEX: POPPING STR:', yy_.yytext);this.popState();\nbreak;\ncase 14: yy.getLogger().debug('LEX: STR end:', yy_.yytext); return \"STR\";\nbreak;\ncase 15:  yy_.yytext = yy_.yytext.replace(/space\\:/,'');yy.getLogger().debug('SPACE NUM (LEX)', yy_.yytext); return 21; \nbreak;\ncase 16: yy_.yytext = '1'; yy.getLogger().debug('COLUMNS (LEX)', yy_.yytext); return 21; \nbreak;\ncase 17:return 43;\nbreak;\ncase 18:return 'LINKSTYLE';\nbreak;\ncase 19:return 'INTERPOLATE';\nbreak;\ncase 20: this.pushState('CLASSDEF'); return 40; \nbreak;\ncase 21: this.popState(); this.pushState('CLASSDEFID'); return 'DEFAULT_CLASSDEF_ID' \nbreak;\ncase 22: this.popState(); this.pushState('CLASSDEFID'); return 41 \nbreak;\ncase 23: this.popState(); return 42 \nbreak;\ncase 24: this.pushState('CLASS'); return 44; \nbreak;\ncase 25: this.popState(); this.pushState('CLASS_STYLE'); return 45 \nbreak;\ncase 26: this.popState(); return 46 \nbreak;\ncase 27: this.pushState('STYLE_STMNT'); return 47; \nbreak;\ncase 28: this.popState(); this.pushState('STYLE_DEFINITION'); return 48 \nbreak;\ncase 29: this.popState(); return 49 \nbreak;\ncase 30: this.pushState(\"acc_title\");return 'acc_title'; \nbreak;\ncase 31: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 32: this.pushState(\"acc_descr\");return 'acc_descr'; \nbreak;\ncase 33: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 34: this.pushState(\"acc_descr_multiline\");\nbreak;\ncase 35: this.popState(); \nbreak;\ncase 36:return \"acc_descr_multiline_value\";\nbreak;\ncase 37:return 30;\nbreak;\ncase 38: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 39: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 40: this.popState();yy.getLogger().debug('Lex: ))'); return \"NODE_DEND\"; \nbreak;\ncase 41: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 42: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 43: this.popState();yy.getLogger().debug('Lex: (-'); return \"NODE_DEND\"; \nbreak;\ncase 44: this.popState();yy.getLogger().debug('Lex: -)'); return \"NODE_DEND\"; \nbreak;\ncase 45: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 46: this.popState();yy.getLogger().debug('Lex: ]]'); return \"NODE_DEND\"; \nbreak;\ncase 47: this.popState();yy.getLogger().debug('Lex: (');  return \"NODE_DEND\";  \nbreak;\ncase 48: this.popState();yy.getLogger().debug('Lex: ])'); return \"NODE_DEND\"; \nbreak;\ncase 49: this.popState();yy.getLogger().debug('Lex: /]'); return \"NODE_DEND\"; \nbreak;\ncase 50: this.popState();yy.getLogger().debug('Lex: /]'); return \"NODE_DEND\"; \nbreak;\ncase 51: this.popState();yy.getLogger().debug('Lex: )]'); return \"NODE_DEND\"; \nbreak;\ncase 52: this.popState();yy.getLogger().debug('Lex: )');  return \"NODE_DEND\"; \nbreak;\ncase 53: this.popState();yy.getLogger().debug('Lex: ]>'); return \"NODE_DEND\"; \nbreak;\ncase 54: this.popState();yy.getLogger().debug('Lex: ]'); return \"NODE_DEND\"; \nbreak;\ncase 55: yy.getLogger().debug('Lexa: -)'); this.pushState('NODE');return 36; \nbreak;\ncase 56: yy.getLogger().debug('Lexa: (-'); this.pushState('NODE');return 36; \nbreak;\ncase 57: yy.getLogger().debug('Lexa: ))'); this.pushState('NODE');return 36;  \nbreak;\ncase 58: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36;      \nbreak;\ncase 59: yy.getLogger().debug('Lex: (((');  this.pushState('NODE');return 36; \nbreak;\ncase 60: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 61: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 62: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 63: yy.getLogger().debug('Lexc: >'); this.pushState('NODE');return 36; \nbreak;\ncase 64: yy.getLogger().debug('Lexa: (['); this.pushState('NODE');return 36; \nbreak;\ncase 65: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 66: this.pushState('NODE');return 36; \nbreak;\ncase 67: this.pushState('NODE');return 36; \nbreak;\ncase 68: this.pushState('NODE');return 36; \nbreak;\ncase 69: this.pushState('NODE');return 36; \nbreak;\ncase 70: this.pushState('NODE');return 36; \nbreak;\ncase 71: this.pushState('NODE');return 36; \nbreak;\ncase 72: this.pushState('NODE');return 36; \nbreak;\ncase 73: yy.getLogger().debug('Lexa: ['); this.pushState('NODE');return 36; \nbreak;\ncase 74: this.pushState('BLOCK_ARROW');yy.getLogger().debug('LEX ARR START');return 38; \nbreak;\ncase 75: yy.getLogger().debug('Lex: NODE_ID', yy_.yytext);return 32; \nbreak;\ncase 76: yy.getLogger().debug('Lex: EOF', yy_.yytext);return 8; \nbreak;\ncase 77: this.pushState(\"md_string\");\nbreak;\ncase 78: this.pushState(\"md_string\");\nbreak;\ncase 79: return \"NODE_DESCR\";\nbreak;\ncase 80: this.popState();\nbreak;\ncase 81: yy.getLogger().debug('Lex: Starting string');this.pushState(\"string\");\nbreak;\ncase 82: yy.getLogger().debug('LEX ARR: Starting string');this.pushState(\"string\");\nbreak;\ncase 83: yy.getLogger().debug('LEX: NODE_DESCR:', yy_.yytext); return \"NODE_DESCR\";\nbreak;\ncase 84:yy.getLogger().debug('LEX POPPING');this.popState();\nbreak;\ncase 85: yy.getLogger().debug('Lex: =>BAE');  this.pushState('ARROW_DIR');  \nbreak;\ncase 86: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (right): dir:',yy_.yytext);return \"DIR\"; \nbreak;\ncase 87: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (left):',yy_.yytext);return \"DIR\"; \nbreak;\ncase 88: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (x):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 89: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (y):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 90: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (up):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 91: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (down):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 92: yy_.yytext=']>';yy.getLogger().debug('Lex (ARROW_DIR end):',yy_.yytext);this.popState();this.popState();return \"BLOCK_ARROW_END\"; \nbreak;\ncase 93: yy.getLogger().debug('Lex: LINK', '#'+yy_.yytext+'#'); return 15; \nbreak;\ncase 94: yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 95: yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 96: yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 97: yy.getLogger().debug('Lex: START_LINK', yy_.yytext);this.pushState(\"LLABEL\");return 16; \nbreak;\ncase 98: yy.getLogger().debug('Lex: START_LINK', yy_.yytext);this.pushState(\"LLABEL\");return 16; \nbreak;\ncase 99: yy.getLogger().debug('Lex: START_LINK', yy_.yytext);this.pushState(\"LLABEL\");return 16; \nbreak;\ncase 100: this.pushState(\"md_string\");\nbreak;\ncase 101: yy.getLogger().debug('Lex: Starting string');this.pushState(\"string\"); return \"LINK_LABEL\";\nbreak;\ncase 102: this.popState(); yy.getLogger().debug('Lex: LINK', '#'+yy_.yytext+'#'); return 15; \nbreak;\ncase 103: this.popState(); yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 104: this.popState(); yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 105: yy.getLogger().debug('Lex: COLON', yy_.yytext); yy_.yytext=yy_.yytext.slice(1);return 27; \nbreak;\n}\n},\nrules: [/^(?:block-beta\\b)/,/^(?:block\\s+)/,/^(?:block\\n+)/,/^(?:block:)/,/^(?:[\\s]+)/,/^(?:[\\n]+)/,/^(?:((\\u000D\\u000A)|(\\u000A)))/,/^(?:columns\\s+auto\\b)/,/^(?:columns\\s+[\\d]+)/,/^(?:[\"][`])/,/^(?:[^`\"]+)/,/^(?:[`][\"])/,/^(?:[\"])/,/^(?:[\"])/,/^(?:[^\"]*)/,/^(?:space[:]\\d+)/,/^(?:space\\b)/,/^(?:default\\b)/,/^(?:linkStyle\\b)/,/^(?:interpolate\\b)/,/^(?:classDef\\s+)/,/^(?:DEFAULT\\s+)/,/^(?:\\w+\\s+)/,/^(?:[^\\n]*)/,/^(?:class\\s+)/,/^(?:(\\w+)+((,\\s*\\w+)*))/,/^(?:[^\\n]*)/,/^(?:style\\s+)/,/^(?:(\\w+)+((,\\s*\\w+)*))/,/^(?:[^\\n]*)/,/^(?:accTitle\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*\\{\\s*)/,/^(?:[\\}])/,/^(?:[^\\}]*)/,/^(?:end\\b\\s*)/,/^(?:\\(\\(\\()/,/^(?:\\)\\)\\))/,/^(?:[\\)]\\))/,/^(?:\\}\\})/,/^(?:\\})/,/^(?:\\(-)/,/^(?:-\\))/,/^(?:\\(\\()/,/^(?:\\]\\])/,/^(?:\\()/,/^(?:\\]\\))/,/^(?:\\\\\\])/,/^(?:\\/\\])/,/^(?:\\)\\])/,/^(?:[\\)])/,/^(?:\\]>)/,/^(?:[\\]])/,/^(?:-\\))/,/^(?:\\(-)/,/^(?:\\)\\))/,/^(?:\\))/,/^(?:\\(\\(\\()/,/^(?:\\(\\()/,/^(?:\\{\\{)/,/^(?:\\{)/,/^(?:>)/,/^(?:\\(\\[)/,/^(?:\\()/,/^(?:\\[\\[)/,/^(?:\\[\\|)/,/^(?:\\[\\()/,/^(?:\\)\\)\\))/,/^(?:\\[\\\\)/,/^(?:\\[\\/)/,/^(?:\\[\\\\)/,/^(?:\\[)/,/^(?:<\\[)/,/^(?:[^\\(\\[\\n\\-\\)\\{\\}\\s\\<\\>:]+)/,/^(?:$)/,/^(?:[\"][`])/,/^(?:[\"][`])/,/^(?:[^`\"]+)/,/^(?:[`][\"])/,/^(?:[\"])/,/^(?:[\"])/,/^(?:[^\"]+)/,/^(?:[\"])/,/^(?:\\]>\\s*\\()/,/^(?:,?\\s*right\\s*)/,/^(?:,?\\s*left\\s*)/,/^(?:,?\\s*x\\s*)/,/^(?:,?\\s*y\\s*)/,/^(?:,?\\s*up\\s*)/,/^(?:,?\\s*down\\s*)/,/^(?:\\)\\s*)/,/^(?:\\s*[xo<]?--+[-xo>]\\s*)/,/^(?:\\s*[xo<]?==+[=xo>]\\s*)/,/^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/,/^(?:\\s*~~[\\~]+\\s*)/,/^(?:\\s*[xo<]?--\\s*)/,/^(?:\\s*[xo<]?==\\s*)/,/^(?:\\s*[xo<]?-\\.\\s*)/,/^(?:[\"][`])/,/^(?:[\"])/,/^(?:\\s*[xo<]?--+[-xo>]\\s*)/,/^(?:\\s*[xo<]?==+[=xo>]\\s*)/,/^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/,/^(?::\\d+)/],\nconditions: {\"STYLE_DEFINITION\":{\"rules\":[29],\"inclusive\":false},\"STYLE_STMNT\":{\"rules\":[28],\"inclusive\":false},\"CLASSDEFID\":{\"rules\":[23],\"inclusive\":false},\"CLASSDEF\":{\"rules\":[21,22],\"inclusive\":false},\"CLASS_STYLE\":{\"rules\":[26],\"inclusive\":false},\"CLASS\":{\"rules\":[25],\"inclusive\":false},\"LLABEL\":{\"rules\":[100,101,102,103,104],\"inclusive\":false},\"ARROW_DIR\":{\"rules\":[86,87,88,89,90,91,92],\"inclusive\":false},\"BLOCK_ARROW\":{\"rules\":[77,82,85],\"inclusive\":false},\"NODE\":{\"rules\":[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],\"inclusive\":false},\"md_string\":{\"rules\":[10,11,79,80],\"inclusive\":false},\"space\":{\"rules\":[],\"inclusive\":false},\"string\":{\"rules\":[13,14,83,84],\"inclusive\":false},\"acc_descr_multiline\":{\"rules\":[35,36],\"inclusive\":false},\"acc_descr\":{\"rules\":[33],\"inclusive\":false},\"acc_title\":{\"rules\":[31],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import clone from 'lodash-es/clone.js';\nimport * as configApi from '../../config.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DiagramDB } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport common from '../common/common.js';\nimport { clear as commonClear } from '../common/commonDb.js';\nimport type { Block, ClassDef } from './blockTypes.js';\n\n// Initialize the node database for simple lookups\nlet blockDatabase = new Map<string, Block>();\nlet edgeList: Block[] = [];\nlet edgeCount = new Map<string, number>();\n\nconst COLOR_KEYWORD = 'color';\nconst FILL_KEYWORD = 'fill';\nconst BG_FILL = 'bgFill';\nconst STYLECLASS_SEP = ',';\nconst config = getConfig();\n\nlet classes = new Map<string, ClassDef>();\n\nconst sanitizeText = (txt: string) => common.sanitizeText(txt, config);\n\n/**\n * Called when the parser comes across a (style) class definition\n * @example classDef my-style fill:#f96;\n *\n * @param id - the id of this (style) class\n * @param styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n */\nexport const addStyleClass = function (id: string, styleAttributes = '') {\n  // create a new style class object with this id\n  let foundClass = classes.get(id);\n  if (!foundClass) {\n    foundClass = { id: id, styles: [], textStyles: [] };\n    classes.set(id, foundClass); // This is a classDef\n  }\n  if (styleAttributes !== undefined && styleAttributes !== null) {\n    styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n      // remove any trailing ;\n      const fixedAttrib = attrib.replace(/([^;]*);/, '$1').trim();\n\n      // replace some style keywords\n      if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n        const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n        const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n        foundClass.textStyles.push(newStyle2);\n      }\n      foundClass.styles.push(fixedAttrib);\n    });\n  }\n};\n\n/**\n * Called when the parser comes across a style definition\n * @example style my-block-id fill:#f96;\n *\n * @param id - the id of the block to style\n * @param styles - the string with 1 or more style attributes (each separated by a comma)\n */\nexport const addStyle2Node = function (id: string, styles = '') {\n  const foundBlock = blockDatabase.get(id)!;\n  if (styles !== undefined && styles !== null) {\n    foundBlock.styles = styles.split(STYLECLASS_SEP);\n  }\n};\n\n/**\n * Add a CSS/style class to the block with the given id.\n * If the block isn't already in the list of known blocks, add it.\n * Might be called by parser when a CSS/style class should be applied to a block\n *\n * @param itemIds - The id or a list of ids of the item(s) to apply the css class to\n * @param cssClassName - CSS class name\n */\nexport const setCssClass = function (itemIds: string, cssClassName: string) {\n  itemIds.split(',').forEach(function (id: string) {\n    let foundBlock = blockDatabase.get(id);\n    if (foundBlock === undefined) {\n      const trimmedId = id.trim();\n      foundBlock = { id: trimmedId, type: 'na', children: [] } as Block;\n      blockDatabase.set(trimmedId, foundBlock);\n    }\n    if (!foundBlock.classes) {\n      foundBlock.classes = [];\n    }\n    foundBlock.classes.push(cssClassName);\n  });\n};\n\nconst populateBlockDatabase = (_blockList: Block[], parent: Block): void => {\n  const blockList = _blockList.flat();\n  const children = [];\n  for (const block of blockList) {\n    if (block.label) {\n      block.label = sanitizeText(block.label);\n    }\n    if (block.type === 'classDef') {\n      addStyleClass(block.id, block.css);\n      continue;\n    }\n    if (block.type === 'applyClass') {\n      setCssClass(block.id, block?.styleClass ?? '');\n      continue;\n    }\n    if (block.type === 'applyStyles') {\n      if (block?.stylesStr) {\n        addStyle2Node(block.id, block?.stylesStr);\n      }\n      continue;\n    }\n    if (block.type === 'column-setting') {\n      parent.columns = block.columns ?? -1;\n    } else if (block.type === 'edge') {\n      const count = (edgeCount.get(block.id) ?? 0) + 1;\n      edgeCount.set(block.id, count);\n      block.id = count + '-' + block.id;\n      edgeList.push(block);\n    } else {\n      if (!block.label) {\n        if (block.type === 'composite') {\n          block.label = '';\n          // log.debug('abc89 composite', block);\n        } else {\n          block.label = block.id;\n        }\n      }\n      const existingBlock = blockDatabase.get(block.id);\n\n      if (existingBlock === undefined) {\n        blockDatabase.set(block.id, block);\n      } else {\n        // Add newer relevant data to aggregated node\n        if (block.type !== 'na') {\n          existingBlock.type = block.type;\n        }\n        if (block.label !== block.id) {\n          existingBlock.label = block.label;\n        }\n      }\n\n      if (block.children) {\n        populateBlockDatabase(block.children, block);\n      }\n      if (block.type === 'space') {\n        // log.debug('abc95 space', block);\n        const w = block.width ?? 1;\n        for (let j = 0; j < w; j++) {\n          const newBlock = clone(block);\n          newBlock.id = newBlock.id + '-' + j;\n          blockDatabase.set(newBlock.id, newBlock);\n          children.push(newBlock);\n        }\n      } else if (existingBlock === undefined) {\n        children.push(block);\n      }\n    }\n  }\n  parent.children = children;\n};\n\nlet blocks: Block[] = [];\nlet rootBlock = { id: 'root', type: 'composite', children: [], columns: -1 } as Block;\n\nconst clear = (): void => {\n  log.debug('Clear called');\n  commonClear();\n  rootBlock = { id: 'root', type: 'composite', children: [], columns: -1 } as Block;\n  blockDatabase = new Map([['root', rootBlock]]);\n  blocks = [];\n  classes = new Map();\n\n  edgeList = [];\n  edgeCount = new Map();\n};\n\nexport function typeStr2Type(typeStr: string) {\n  log.debug('typeStr2Type', typeStr);\n  switch (typeStr) {\n    case '[]':\n      return 'square';\n    case '()':\n      log.debug('we have a round');\n      return 'round';\n    case '(())':\n      return 'circle';\n    case '>]':\n      return 'rect_left_inv_arrow';\n    case '{}':\n      return 'diamond';\n    case '{{}}':\n      return 'hexagon';\n    case '([])':\n      return 'stadium';\n    case '[[]]':\n      return 'subroutine';\n    case '[()]':\n      return 'cylinder';\n    case '((()))':\n      return 'doublecircle';\n    case '[//]':\n      return 'lean_right';\n    case '[\\\\\\\\]':\n      return 'lean_left';\n    case '[/\\\\]':\n      return 'trapezoid';\n    case '[\\\\/]':\n      return 'inv_trapezoid';\n    case '<[]>':\n      return 'block_arrow';\n    default:\n      return 'na';\n  }\n}\n\nexport function edgeTypeStr2Type(typeStr: string): string {\n  log.debug('typeStr2Type', typeStr);\n  switch (typeStr) {\n    case '==':\n      return 'thick';\n    default:\n      return 'normal';\n  }\n}\n\nexport function edgeStrToEdgeData(typeStr: string): string {\n  switch (typeStr.trim()) {\n    case '--x':\n      return 'arrow_cross';\n    case '--o':\n      return 'arrow_circle';\n    default:\n      return 'arrow_point';\n  }\n}\n\nlet cnt = 0;\nexport const generateId = () => {\n  cnt++;\n  return 'id-' + Math.random().toString(36).substr(2, 12) + '-' + cnt;\n};\n\nconst setHierarchy = (block: Block[]): void => {\n  rootBlock.children = block;\n  populateBlockDatabase(block, rootBlock);\n  blocks = rootBlock.children;\n};\n\nconst getColumns = (blockId: string): number => {\n  const block = blockDatabase.get(blockId);\n  if (!block) {\n    return -1;\n  }\n  if (block.columns) {\n    return block.columns;\n  }\n  if (!block.children) {\n    return -1;\n  }\n  return block.children.length;\n};\n\n/**\n * Returns all the blocks as a flat array\n * @returns\n */\nconst getBlocksFlat = () => {\n  return [...blockDatabase.values()];\n};\n/**\n * Returns the hierarchy of blocks\n * @returns\n */\nconst getBlocks = () => {\n  return blocks || [];\n};\n\nconst getEdges = () => {\n  return edgeList;\n};\nconst getBlock = (id: string) => {\n  return blockDatabase.get(id);\n};\n\nconst setBlock = (block: Block) => {\n  blockDatabase.set(block.id, block);\n};\n\nconst getLogger = () => log;\n\n/**\n * Return all of the style classes\n */\nexport const getClasses = function () {\n  return classes;\n};\n\nconst db = {\n  getConfig: () => configApi.getConfig().block,\n  typeStr2Type: typeStr2Type,\n  edgeTypeStr2Type: edgeTypeStr2Type,\n  edgeStrToEdgeData,\n  getLogger,\n  getBlocksFlat,\n  getBlocks,\n  getEdges,\n  setHierarchy,\n  getBlock,\n  setBlock,\n  getColumns,\n  getClasses,\n  clear,\n  generateId,\n} as const;\n\nexport type BlockDB = typeof db & DiagramDB;\nexport default db;\n", "import * as khroma from 'khroma';\nimport { getIconStyles } from '../globalStyles.js';\n\n/** Returns the styles given options */\nexport interface BlockChartStyleOptions {\n  arrowheadColor: string;\n  border2: string;\n  clusterBkg: string;\n  clusterBorder: string;\n  edgeLabelBackground: string;\n  fontFamily: string;\n  lineColor: string;\n  mainBkg: string;\n  nodeBorder: string;\n  nodeTextColor: string;\n  tertiaryColor: string;\n  textColor: string;\n  titleColor: string;\n}\n\nconst fade = (color: string, opacity: number) => {\n  // @ts-ignore TODO: incorrect types from khroma\n  const channel = khroma.channel;\n\n  const r = channel(color, 'r');\n  const g = channel(color, 'g');\n  const b = channel(color, 'b');\n\n  // @ts-ignore incorrect types from khroma\n  return khroma.rgba(r, g, b, opacity);\n};\n\nconst getStyles = (options: BlockChartStyleOptions) =>\n  `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .cluster-label text {\n    fill: ${options.titleColor};\n  }\n  .cluster-label span,p {\n    color: ${options.titleColor};\n  }\n\n\n\n  .label text,span,p {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n  .flowchart-label text {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n      background-color: ${options.edgeLabelBackground};\n      fill: ${options.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${fade(options.edgeLabelBackground, 0.5)};\n    // background-color:\n  }\n\n  .node .cluster {\n    // fill: ${fade(options.mainBkg, 0.5)};\n    fill: ${fade(options.clusterBkg, 0.5)};\n    stroke: ${fade(options.clusterBorder, 0.2)};\n    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  .cluster span,p {\n    color: ${options.titleColor};\n  }\n  /* .cluster div {\n    color: ${options.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n  ${getIconStyles()}\n`;\n\nexport default getStyles;\n", "/** Setup arrow head and define the marker. The result is appended to the svg. */\n\nimport { log } from '../logger.js';\n\n// Only add the number of markers that the diagram needs\nconst insertMarkers = (elem, markerArray, type, id) => {\n  markerArray.forEach((markerName) => {\n    markers[markerName](elem, type, id);\n  });\n};\n\nconst extension = (elem, type, id) => {\n  log.trace('Making markers for ', id);\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionStart')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,7 L18,13 V 1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionEnd')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,1 V 13 L18,7 Z'); // this is actual shape for arrowhead\n};\n\nconst composition = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionStart')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionEnd')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst aggregation = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationStart')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationEnd')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst dependency = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyStart')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 6)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 5,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyEnd')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\nconst lollipop = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopStart')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopEnd')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n};\nconst point = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 6)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 0 L 10 5 L 0 10 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 4.5)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 5 L 10 10 L 10 0 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst circle = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 11)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', -1)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst cross = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossEnd')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', 12)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossStart')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', -1)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n};\nconst barb = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-barbEnd')\n    .attr('refX', 19)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 14)\n    .attr('markerUnits', 'strokeWidth')\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 19,7 L9,13 L14,7 L9,1 Z');\n};\n\n// TODO rename the class diagram markers to something shape descriptive and semantic free\nconst markers = {\n  extension,\n  composition,\n  aggregation,\n  dependency,\n  lollipop,\n  point,\n  circle,\n  cross,\n  barb,\n};\nexport default insertMarkers;\n", "import type { BlockDB } from './blockDB.js';\nimport type { Block } from './blockTypes.js';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\n// TODO: This means the number we provide in diagram's config will never be used. Should fix.\nconst padding = getConfig()?.block?.padding ?? 8;\n\ninterface BlockPosition {\n  px: number;\n  py: number;\n}\n\nexport function calculateBlockPosition(columns: number, position: number): BlockPosition {\n  // log.debug('calculateBlockPosition abc89', columns, position);\n  // Ensure that columns is a positive integer\n  if (columns === 0 || !Number.isInteger(columns)) {\n    throw new Error('Columns must be an integer !== 0.');\n  }\n\n  // Ensure that position is a non-negative integer\n  if (position < 0 || !Number.isInteger(position)) {\n    throw new Error('Position must be a non-negative integer.' + position);\n  }\n\n  if (columns < 0) {\n    // Auto columns is set\n    return { px: position, py: 0 };\n  }\n  if (columns === 1) {\n    // Auto columns is set\n    return { px: 0, py: position };\n  }\n  // Calculate posX and posY\n  const px = position % columns;\n  const py = Math.floor(position / columns);\n  // log.debug('calculateBlockPosition abc89', columns, position, '=> (', px, py, ')');\n  return { px, py };\n}\n\nconst getMaxChildSize = (block: Block) => {\n  let maxWidth = 0;\n  let maxHeight = 0;\n  // find max width of children\n  // log.debug('getMaxChildSize abc95 (start) parent:', block.id);\n  for (const child of block.children) {\n    const { width, height, x, y } = child.size ?? { width: 0, height: 0, x: 0, y: 0 };\n    log.debug(\n      'getMaxChildSize abc95 child:',\n      child.id,\n      'width:',\n      width,\n      'height:',\n      height,\n      'x:',\n      x,\n      'y:',\n      y,\n      child.type\n    );\n    if (child.type === 'space') {\n      continue;\n    }\n    if (width > maxWidth) {\n      maxWidth = width / (block.widthInColumns ?? 1);\n    }\n    if (height > maxHeight) {\n      maxHeight = height;\n    }\n  }\n  return { width: maxWidth, height: maxHeight };\n};\n\nfunction setBlockSizes(block: Block, db: BlockDB, siblingWidth = 0, siblingHeight = 0) {\n  log.debug(\n    'setBlockSizes abc95 (start)',\n    block.id,\n    block?.size?.x,\n    'block width =',\n    block?.size,\n    'siblingWidth',\n    siblingWidth\n  );\n  if (!block?.size?.width) {\n    block.size = {\n      width: siblingWidth,\n      height: siblingHeight,\n      x: 0,\n      y: 0,\n    };\n  }\n  let maxWidth = 0;\n  let maxHeight = 0;\n\n  if (block.children?.length > 0) {\n    for (const child of block.children) {\n      setBlockSizes(child, db);\n    }\n    // find max width of children\n    const childSize = getMaxChildSize(block);\n    maxWidth = childSize.width;\n    maxHeight = childSize.height;\n    log.debug('setBlockSizes abc95 maxWidth of', block.id, ':s children is ', maxWidth, maxHeight);\n\n    // set width of block to max width of children\n    for (const child of block.children) {\n      if (child.size) {\n        log.debug(\n          `abc95 Setting size of children of ${block.id} id=${child.id} ${maxWidth} ${maxHeight} ${JSON.stringify(child.size)}`\n        );\n        child.size.width =\n          maxWidth * (child.widthInColumns ?? 1) + padding * ((child.widthInColumns ?? 1) - 1);\n        child.size.height = maxHeight;\n        child.size.x = 0;\n        child.size.y = 0;\n\n        log.debug(\n          `abc95 updating size of ${block.id} children child:${child.id} maxWidth:${maxWidth} maxHeight:${maxHeight}`\n        );\n      }\n    }\n    for (const child of block.children) {\n      setBlockSizes(child, db, maxWidth, maxHeight);\n    }\n\n    const columns = block.columns ?? -1;\n    let numItems = 0;\n    for (const child of block.children) {\n      numItems += child.widthInColumns ?? 1;\n    }\n\n    // The width and height in number blocks\n    let xSize = block.children.length;\n    if (columns > 0 && columns < numItems) {\n      xSize = columns;\n    }\n\n    const ySize = Math.ceil(numItems / xSize);\n\n    let width = xSize * (maxWidth + padding) + padding;\n    let height = ySize * (maxHeight + padding) + padding;\n    // If maxWidth\n    if (width < siblingWidth) {\n      log.debug(\n        `Detected to small sibling: abc95 ${block.id} siblingWidth ${siblingWidth} siblingHeight ${siblingHeight} width ${width}`\n      );\n      width = siblingWidth;\n      height = siblingHeight;\n      const childWidth = (siblingWidth - xSize * padding - padding) / xSize;\n      const childHeight = (siblingHeight - ySize * padding - padding) / ySize;\n      // cspell:ignore indata\n      log.debug('Size indata abc88', block.id, 'childWidth', childWidth, 'maxWidth', maxWidth);\n      log.debug('Size indata abc88', block.id, 'childHeight', childHeight, 'maxHeight', maxHeight);\n      log.debug('Size indata abc88 xSize', xSize, 'padding', padding);\n\n      // set width of block to max width of children\n      for (const child of block.children) {\n        if (child.size) {\n          child.size.width = childWidth;\n          child.size.height = childHeight;\n          child.size.x = 0;\n          child.size.y = 0;\n        }\n      }\n    }\n\n    log.debug(\n      `abc95 (finale calc) ${block.id} xSize ${xSize} ySize ${ySize} columns ${columns}${\n        block.children.length\n      } width=${Math.max(width, block.size?.width || 0)}`\n    );\n    if (width < (block?.size?.width || 0)) {\n      width = block?.size?.width || 0;\n\n      // Grow children to fit\n      const num = columns > 0 ? Math.min(block.children.length, columns) : block.children.length;\n      if (num > 0) {\n        const childWidth = (width - num * padding - padding) / num;\n        log.debug('abc95 (growing to fit) width', block.id, width, block.size?.width, childWidth);\n        for (const child of block.children) {\n          if (child.size) {\n            child.size.width = childWidth;\n          }\n        }\n      }\n    }\n    block.size = {\n      width,\n      height,\n      x: 0,\n      y: 0,\n    };\n  }\n\n  log.debug(\n    'setBlockSizes abc94 (done)',\n    block.id,\n    block?.size?.x,\n    block?.size?.width,\n    block?.size?.y,\n    block?.size?.height\n  );\n}\n\nfunction layoutBlocks(block: Block, db: BlockDB) {\n  log.debug(\n    `abc85 layout blocks (=>layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n  const columns = block.columns ?? -1;\n  log.debug('layoutBlocks columns abc95', block.id, '=>', columns, block);\n  if (\n    block.children && // find max width of children\n    block.children.length > 0\n  ) {\n    const width = block?.children[0]?.size?.width ?? 0;\n    const widthOfChildren = block.children.length * width + (block.children.length - 1) * padding;\n\n    log.debug('widthOfChildren 88', widthOfChildren, 'posX');\n\n    // let first = true;\n    let columnPos = 0;\n    log.debug('abc91 block?.size?.x', block.id, block?.size?.x);\n    let startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n    let rowPos = 0;\n    for (const child of block.children) {\n      const parent = block;\n\n      if (!child.size) {\n        continue;\n      }\n      const { width, height } = child.size;\n      const { px, py } = calculateBlockPosition(columns, columnPos);\n      if (py != rowPos) {\n        rowPos = py;\n        startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n        log.debug('New row in layout for block', block.id, ' and child ', child.id, rowPos);\n      }\n      log.debug(\n        `abc89 layout blocks (child) id: ${child.id} Pos: ${columnPos} (px, py) ${px},${py} (${parent?.size?.x},${parent?.size?.y}) parent: ${parent.id} width: ${width}${padding}`\n      );\n      if (parent.size) {\n        const halfWidth = width / 2;\n        child.size.x = startingPosX + padding + halfWidth;\n\n        // cspell:ignore pyid\n        log.debug(\n          `abc91 layout blocks (calc) px, pyid:${\n            child.id\n          } startingPos=X${startingPosX} new startingPosX${\n            child.size.x\n          } ${halfWidth} padding=${padding} width=${width} halfWidth=${halfWidth} => x:${\n            child.size.x\n          } y:${child.size.y} ${child.widthInColumns} (width * (child?.w || 1)) / 2 ${\n            (width * (child?.widthInColumns ?? 1)) / 2\n          }`\n        );\n\n        startingPosX = child.size.x + halfWidth;\n\n        child.size.y =\n          parent.size.y - parent.size.height / 2 + py * (height + padding) + height / 2 + padding;\n\n        log.debug(\n          `abc88 layout blocks (calc) px, pyid:${\n            child.id\n          }startingPosX${startingPosX}${padding}${halfWidth}=>x:${child.size.x}y:${child.size.y}${\n            child.widthInColumns\n          }(width * (child?.w || 1)) / 2${(width * (child?.widthInColumns ?? 1)) / 2}`\n        );\n      }\n      if (child.children) {\n        layoutBlocks(child, db);\n      }\n      columnPos += child?.widthInColumns ?? 1;\n      log.debug('abc88 columnsPos', child, columnPos);\n    }\n  }\n  log.debug(\n    `layout blocks (<==layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n}\n\nfunction findBounds(\n  block: Block,\n  { minX, minY, maxX, maxY } = { minX: 0, minY: 0, maxX: 0, maxY: 0 }\n) {\n  if (block.size && block.id !== 'root') {\n    const { x, y, width, height } = block.size;\n    if (x - width / 2 < minX) {\n      minX = x - width / 2;\n    }\n    if (y - height / 2 < minY) {\n      minY = y - height / 2;\n    }\n    if (x + width / 2 > maxX) {\n      maxX = x + width / 2;\n    }\n    if (y + height / 2 > maxY) {\n      maxY = y + height / 2;\n    }\n  }\n  if (block.children) {\n    for (const child of block.children) {\n      ({ minX, minY, maxX, maxY } = findBounds(child, { minX, minY, maxX, maxY }));\n    }\n  }\n  return { minX, minY, maxX, maxY };\n}\n\nexport function layout(db: BlockDB) {\n  const root = db.getBlock('root');\n  if (!root) {\n    return;\n  }\n\n  setBlockSizes(root, db, 0, 0);\n  layoutBlocks(root, db);\n  // Position blocks relative to parents\n  // positionBlock(root, root, db);\n  log.debug('getBlocks', JSON.stringify(root, null, 2));\n\n  const { minX, minY, maxX, maxY } = findBounds(root);\n\n  const height = maxY - minY;\n  const width = maxX - minX;\n  return { x: minX, y: minY, width, height };\n}\n", "import { select } from 'd3';\nimport { log } from '../logger.js';\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport { evaluate } from '../diagrams/common/common.js';\nimport { decodeEntities } from '../utils.js';\nimport { replaceIconSubstring } from '../rendering-util/createText.js';\n\n/**\n * @param dom\n * @param styleFn\n */\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\n/**\n * @param {any} node\n * @returns {SVGForeignObjectElement} Node\n */\nfunction addHtmlLabel(node) {\n  const fo = select(document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject'));\n  const div = fo.append('xhtml:div');\n\n  const label = node.label;\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  const span = div.append('span');\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr('class', labelClass);\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'inline-block');\n  // Fix for firefox\n  div.style('white-space', 'nowrap');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  return fo.node();\n}\n/**\n * @param _vertexText\n * @param style\n * @param isTitle\n * @param isNode\n * @deprecated svg-util/createText instead\n */\nconst createLabel = async (_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || '';\n  if (typeof vertexText === 'object') {\n    vertexText = vertexText[0];\n  }\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n    vertexText = vertexText.replace(/\\\\n|\\n/g, '<br />');\n    log.debug('vertexText' + vertexText);\n    const label = await replaceIconSubstring(decodeEntities(vertexText));\n    const node = {\n      isNode,\n      label,\n      labelStyle: style.replace('fill:', 'color:'),\n    };\n    let vertexNode = addHtmlLabel(node);\n    // vertexNode.parentNode.removeChild(vertexNode);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n    svgLabel.setAttribute('style', style.replace('color:', 'fill:'));\n    let rows = [];\n    if (typeof vertexText === 'string') {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n\n    for (const row of rows) {\n      const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');\n      tspan.setAttributeNS('http://www.w3.org/XML/1998/namespace', 'xml:space', 'preserve');\n      tspan.setAttribute('dy', '1em');\n      tspan.setAttribute('x', '0');\n      if (isTitle) {\n        tspan.setAttribute('class', 'title-row');\n      } else {\n        tspan.setAttribute('class', 'row');\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n};\n\nexport default createLabel;\n", "import type { SVG } from '../diagram-api/types.js';\nimport { log } from '../logger.js';\nimport type { EdgeData } from '../types.js';\n/**\n * Adds SVG markers to a path element based on the arrow types specified in the edge.\n *\n * @param svgPath - The SVG path element to add markers to.\n * @param edge - The edge data object containing the arrow types.\n * @param url - The URL of the SVG marker definitions.\n * @param id - The ID prefix for the SVG marker definitions.\n * @param diagramType - The type of diagram being rendered.\n */\nexport const addEdgeMarkers = (\n  svgPath: SVG,\n  edge: Pick<EdgeData, 'arrowTypeStart' | 'arrowTypeEnd'>,\n  url: string,\n  id: string,\n  diagramType: string\n) => {\n  if (edge.arrowTypeStart) {\n    addEdgeMarker(svgPath, 'start', edge.arrowTypeStart, url, id, diagramType);\n  }\n  if (edge.arrowTypeEnd) {\n    addEdgeMarker(svgPath, 'end', edge.arrowTypeEnd, url, id, diagramType);\n  }\n};\n\nconst arrowTypesMap = {\n  arrow_cross: 'cross',\n  arrow_point: 'point',\n  arrow_barb: 'barb',\n  arrow_circle: 'circle',\n  aggregation: 'aggregation',\n  extension: 'extension',\n  composition: 'composition',\n  dependency: 'dependency',\n  lollipop: 'lollipop',\n} as const;\n\nconst addEdgeMarker = (\n  svgPath: SVG,\n  position: 'start' | 'end',\n  arrowType: string,\n  url: string,\n  id: string,\n  diagramType: string\n) => {\n  const endMarkerType = arrowTypesMap[arrowType as keyof typeof arrowTypesMap];\n\n  if (!endMarkerType) {\n    log.warn(`Unknown arrow type: ${arrowType}`);\n    return; // unknown arrow type, ignore\n  }\n\n  const suffix = position === 'start' ? 'Start' : 'End';\n  svgPath.attr(`marker-${position}`, `url(${url}#${id}_${diagramType}-${endMarkerType}${suffix})`);\n};\n", "import { log } from '../logger.js';\nimport createLabel from './createLabel.js';\nimport { createText } from '../rendering-util/createText.js';\nimport { line, curveBasis, select } from 'd3';\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport utils from '../utils.js';\nimport { evaluate, getUrl } from '../diagrams/common/common.js';\nimport { getLineFunctionsWithOffset } from '../utils/lineWithOffset.js';\nimport { getSubGraphTitleMargins } from '../utils/subGraphTitleMargins.js';\nimport { addEdgeMarkers } from './edgeMarker.js';\n\nlet edgeLabels = {};\nlet terminalLabels = {};\n\nexport const clear = () => {\n  edgeLabels = {};\n  terminalLabels = {};\n};\n\nexport const insertEdgeLabel = async (elem, edge) => {\n  const config = getConfig();\n  const useHtmlLabels = evaluate(config.flowchart.htmlLabels);\n  // Create the actual text element\n  const labelElement =\n    edge.labelType === 'markdown'\n      ? createText(\n          elem,\n          edge.label,\n          {\n            style: edge.labelStyle,\n            useHtmlLabels,\n            addSvgBackground: true,\n          },\n          config\n        )\n      : await createLabel(edge.label, edge.labelStyle);\n\n  // Create outer g, edgeLabel, this will be positioned after graph layout\n  const edgeLabel = elem.insert('g').attr('class', 'edgeLabel');\n\n  // Create inner g, label, this will be positioned now for centering the text\n  const label = edgeLabel.insert('g').attr('class', 'label');\n  label.node().appendChild(labelElement);\n\n  // Center the label\n  let bbox = labelElement.getBBox();\n  if (useHtmlLabels) {\n    const div = labelElement.children[0];\n    const dv = select(labelElement);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n\n  // Make element accessible by id for positioning\n  edgeLabels[edge.id] = edgeLabel;\n\n  // Update the abstract data of the edge with the new information about its width and height\n  edge.width = bbox.width;\n  edge.height = bbox.height;\n\n  let fo;\n  if (edge.startLabelLeft) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(edge.startLabelLeft, edge.labelStyle);\n    const startEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startLeft = startEdgeLabelLeft;\n    setTerminalWidth(fo, edge.startLabelLeft);\n  }\n  if (edge.startLabelRight) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(edge.startLabelRight, edge.labelStyle);\n    const startEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelRight.insert('g').attr('class', 'inner');\n    fo = startEdgeLabelRight.node().appendChild(startLabelElement);\n    inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startRight = startEdgeLabelRight;\n    setTerminalWidth(fo, edge.startLabelRight);\n  }\n  if (edge.endLabelLeft) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelLeft, edge.labelStyle);\n    const endEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelLeft.node().appendChild(endLabelElement);\n\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endLeft = endEdgeLabelLeft;\n    setTerminalWidth(fo, edge.endLabelLeft);\n  }\n  if (edge.endLabelRight) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelRight, edge.labelStyle);\n    const endEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelRight.insert('g').attr('class', 'inner');\n\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelRight.node().appendChild(endLabelElement);\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endRight = endEdgeLabelRight;\n    setTerminalWidth(fo, edge.endLabelRight);\n  }\n  return labelElement;\n};\n\n/**\n * @param {any} fo\n * @param {any} value\n */\nfunction setTerminalWidth(fo, value) {\n  if (getConfig().flowchart.htmlLabels && fo) {\n    fo.style.width = value.length * 9 + 'px';\n    fo.style.height = '12px';\n  }\n}\n\nexport const positionEdgeLabel = (edge, paths) => {\n  log.debug('Moving label abc88 ', edge.id, edge.label, edgeLabels[edge.id], paths);\n  let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  const siteConfig = getConfig();\n  const { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  if (edge.label) {\n    const el = edgeLabels[edge.id];\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      //   // debugger;\n      const pos = utils.calcLabelPosition(path);\n      log.debug(\n        'Moving label ' + edge.label + ' from (',\n        x,\n        ',',\n        y,\n        ') to (',\n        pos.x,\n        ',',\n        pos.y,\n        ') abc88'\n      );\n      if (paths.updatedPath) {\n        x = pos.x;\n        y = pos.y;\n      }\n    }\n    el.attr('transform', `translate(${x}, ${y + subGraphTitleTotalMargin / 2})`);\n  }\n\n  //let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  if (edge.startLabelLeft) {\n    const el = terminalLabels[edge.id].startLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, 'start_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.startLabelRight) {\n    const el = terminalLabels[edge.id].startRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(\n        edge.arrowTypeStart ? 10 : 0,\n        'start_right',\n        path\n      );\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelLeft) {\n    const el = terminalLabels[edge.id].endLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelRight) {\n    const el = terminalLabels[edge.id].endRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_right', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n};\n\nconst outsideNode = (node, point) => {\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(point.x - x);\n  const dy = Math.abs(point.y - y);\n  const w = node.width / 2;\n  const h = node.height / 2;\n  if (dx >= w || dy >= h) {\n    return true;\n  }\n  return false;\n};\n\nexport const intersection = (node, outsidePoint, insidePoint) => {\n  log.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(outsidePoint)}\n  insidePoint : ${JSON.stringify(insidePoint)}\n  node        : x:${node.x} y:${node.y} w:${node.width} h:${node.height}`);\n  const x = node.x;\n  const y = node.y;\n\n  const dx = Math.abs(x - insidePoint.x);\n  // const dy = Math.abs(y - insidePoint.y);\n  const w = node.width / 2;\n  let r = insidePoint.x < outsidePoint.x ? w - dx : w + dx;\n  const h = node.height / 2;\n\n  const Q = Math.abs(outsidePoint.y - insidePoint.y);\n  const R = Math.abs(outsidePoint.x - insidePoint.x);\n\n  if (Math.abs(y - outsidePoint.y) * w > Math.abs(x - outsidePoint.x) * h) {\n    // Intersection is top or bottom of rect.\n    let q = insidePoint.y < outsidePoint.y ? outsidePoint.y - h - y : y - h - outsidePoint.y;\n    r = (R * q) / Q;\n    const res = {\n      x: insidePoint.x < outsidePoint.x ? insidePoint.x + r : insidePoint.x - R + r,\n      y: insidePoint.y < outsidePoint.y ? insidePoint.y + Q - q : insidePoint.y - Q + q,\n    };\n\n    if (r === 0) {\n      res.x = outsidePoint.x;\n      res.y = outsidePoint.y;\n    }\n    if (R === 0) {\n      res.x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      res.y = outsidePoint.y;\n    }\n\n    log.debug(`abc89 topp/bott calc, Q ${Q}, q ${q}, R ${R}, r ${r}`, res); // cspell: disable-line\n\n    return res;\n  } else {\n    // Intersection on sides of rect\n    if (insidePoint.x < outsidePoint.x) {\n      r = outsidePoint.x - w - x;\n    } else {\n      // r = outsidePoint.x - w - x;\n      r = x - w - outsidePoint.x;\n    }\n    let q = (Q * r) / R;\n    //  OK let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x + dx - w;\n    // OK let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : outsidePoint.x + r;\n    let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x - R + r;\n    // let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : outsidePoint.x + r;\n    let _y = insidePoint.y < outsidePoint.y ? insidePoint.y + q : insidePoint.y - q;\n    log.debug(`sides calc abc89, Q ${Q}, q ${q}, R ${R}, r ${r}`, { _x, _y });\n    if (r === 0) {\n      _x = outsidePoint.x;\n      _y = outsidePoint.y;\n    }\n    if (R === 0) {\n      _x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      _y = outsidePoint.y;\n    }\n\n    return { x: _x, y: _y };\n  }\n};\n/**\n * This function will page a path and node where the last point(s) in the path is inside the node\n * and return an update path ending by the border of the node.\n *\n * @param {Array} _points\n * @param {any} boundaryNode\n * @returns {Array} Points\n */\nconst cutPathAtIntersect = (_points, boundaryNode) => {\n  log.debug('abc88 cutPathAtIntersect', _points, boundaryNode);\n  let points = [];\n  let lastPointOutside = _points[0];\n  let isInside = false;\n  _points.forEach((point) => {\n    // check if point is inside the boundary rect\n    if (!outsideNode(boundaryNode, point) && !isInside) {\n      // First point inside the rect found\n      // Calc the intersection coord between the point and the last point outside the rect\n      const inter = intersection(boundaryNode, lastPointOutside, point);\n\n      // // Check case where the intersection is the same as the last point\n      let pointPresent = false;\n      points.forEach((p) => {\n        pointPresent = pointPresent || (p.x === inter.x && p.y === inter.y);\n      });\n      // // if (!pointPresent) {\n      if (!points.some((e) => e.x === inter.x && e.y === inter.y)) {\n        points.push(inter);\n      }\n\n      isInside = true;\n    } else {\n      // Outside\n      lastPointOutside = point;\n      // points.push(point);\n      if (!isInside) {\n        points.push(point);\n      }\n    }\n  });\n  return points;\n};\n\nexport const insertEdge = function (elem, e, edge, clusterDb, diagramType, graph, id) {\n  let points = edge.points;\n  log.debug('abc88 InsertEdge: edge=', edge, 'e=', e);\n  let pointsHasChanged = false;\n  const tail = graph.node(e.v);\n  var head = graph.node(e.w);\n\n  if (head?.intersect && tail?.intersect) {\n    points = points.slice(1, edge.points.length - 1);\n    points.unshift(tail.intersect(points[0]));\n    points.push(head.intersect(points[points.length - 1]));\n  }\n\n  if (edge.toCluster) {\n    log.debug('to cluster abc88', clusterDb[edge.toCluster]);\n    points = cutPathAtIntersect(edge.points, clusterDb[edge.toCluster].node);\n\n    pointsHasChanged = true;\n  }\n\n  if (edge.fromCluster) {\n    log.debug('from cluster abc88', clusterDb[edge.fromCluster]);\n    points = cutPathAtIntersect(points.reverse(), clusterDb[edge.fromCluster].node).reverse();\n\n    pointsHasChanged = true;\n  }\n\n  // The data for our line\n  const lineData = points.filter((p) => !Number.isNaN(p.y));\n\n  // This is the accessor function we talked about above\n  let curve = curveBasis;\n  // Currently only flowcharts get the curve from the settings, perhaps this should\n  // be expanded to a common setting? Restricting it for now in order not to cause side-effects that\n  // have not been thought through\n  if (edge.curve && (diagramType === 'graph' || diagramType === 'flowchart')) {\n    curve = edge.curve;\n  }\n\n  const { x, y } = getLineFunctionsWithOffset(edge);\n  const lineFunction = line().x(x).y(y).curve(curve);\n\n  // Construct stroke classes based on properties\n  let strokeClasses;\n  switch (edge.thickness) {\n    case 'normal':\n      strokeClasses = 'edge-thickness-normal';\n      break;\n    case 'thick':\n      strokeClasses = 'edge-thickness-thick';\n      break;\n    case 'invisible':\n      strokeClasses = 'edge-thickness-thick';\n      break;\n    default:\n      strokeClasses = '';\n  }\n  switch (edge.pattern) {\n    case 'solid':\n      strokeClasses += ' edge-pattern-solid';\n      break;\n    case 'dotted':\n      strokeClasses += ' edge-pattern-dotted';\n      break;\n    case 'dashed':\n      strokeClasses += ' edge-pattern-dashed';\n      break;\n  }\n\n  const svgPath = elem\n    .append('path')\n    .attr('d', lineFunction(lineData))\n    .attr('id', edge.id)\n    .attr('class', ' ' + strokeClasses + (edge.classes ? ' ' + edge.classes : ''))\n    .attr('style', edge.style);\n\n  // DEBUG code, adds a red circle at each edge coordinate\n  // edge.points.forEach((point) => {\n  //   elem\n  //     .append('circle')\n  //     .style('stroke', 'red')\n  //     .style('fill', 'red')\n  //     .attr('r', 1)\n  //     .attr('cx', point.x)\n  //     .attr('cy', point.y);\n  // });\n\n  let url = '';\n  // // TODO: Can we load this config only from the rendered graph type?\n  if (getConfig().flowchart.arrowMarkerAbsolute || getConfig().state.arrowMarkerAbsolute) {\n    url = getUrl(true);\n  }\n\n  addEdgeMarkers(svgPath, edge, url, id, diagramType);\n\n  let paths = {};\n  if (pointsHasChanged) {\n    paths.updatedPath = points;\n  }\n  paths.originalPath = edge.points;\n  return paths;\n};\n", "import type { Direction } from '../../src/diagrams/block/blockTypes.js';\n\nconst expandAndDeduplicateDirections = (directions: Direction[]) => {\n  const uniqueDirections = new Set();\n\n  for (const direction of directions) {\n    switch (direction) {\n      case 'x':\n        uniqueDirections.add('right');\n        uniqueDirections.add('left');\n        break;\n      case 'y':\n        uniqueDirections.add('up');\n        uniqueDirections.add('down');\n        break;\n      default:\n        uniqueDirections.add(direction);\n        break;\n    }\n  }\n\n  return uniqueDirections;\n};\nexport const getArrowPoints = (\n  duplicatedDirections: Direction[],\n  bbox: { width: number; height: number },\n  node: any\n) => {\n  // Expand and deduplicate the provided directions.\n  // for instance: x, right => right, left\n  const directions = expandAndDeduplicateDirections(duplicatedDirections);\n\n  // Factor to divide height for some calculations.\n  const f = 2;\n\n  // Calculated height of the bounding box, accounting for node padding.\n  const height = bbox.height + 2 * node.padding;\n  // Midpoint calculation based on height.\n  const midpoint = height / f;\n  // Calculated width of the bounding box, accounting for additional width and node padding.\n  const width = bbox.width + 2 * midpoint + node.padding;\n  // Padding to use, half of the node padding.\n  const padding = node.padding / 2;\n\n  if (\n    directions.has('right') &&\n    directions.has('left') &&\n    directions.has('up') &&\n    directions.has('down')\n  ) {\n    // SQUARE\n    return [\n      // Bottom\n      { x: 0, y: 0 },\n      { x: midpoint, y: 0 },\n      { x: width / 2, y: 2 * padding },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: 0 },\n\n      // Right\n      { x: width, y: -height / 3 },\n      { x: width + 2 * padding, y: -height / 2 },\n      { x: width, y: (-2 * height) / 3 },\n      { x: width, y: -height },\n\n      // Top\n      { x: width - midpoint, y: -height },\n      { x: width / 2, y: -height - 2 * padding },\n      { x: midpoint, y: -height },\n\n      // Left\n      { x: 0, y: -height },\n      { x: 0, y: (-2 * height) / 3 },\n      { x: -2 * padding, y: -height / 2 },\n      { x: 0, y: -height / 3 },\n    ];\n  }\n  if (directions.has('right') && directions.has('left') && directions.has('up')) {\n    // RECTANGLE_VERTICAL (Top Open)\n    return [\n      { x: midpoint, y: 0 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 },\n    ];\n  }\n  if (directions.has('right') && directions.has('left') && directions.has('down')) {\n    // RECTANGLE_VERTICAL (Bottom Open)\n    return [\n      { x: 0, y: 0 },\n      { x: midpoint, y: -height },\n      { x: width - midpoint, y: -height },\n      { x: width, y: 0 },\n    ];\n  }\n  if (directions.has('right') && directions.has('up') && directions.has('down')) {\n    // RECTANGLE_HORIZONTAL (Right Open)\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: width, y: -height + midpoint },\n      { x: 0, y: -height },\n    ];\n  }\n  if (directions.has('left') && directions.has('up') && directions.has('down')) {\n    // RECTANGLE_HORIZONTAL (Left Open)\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: 0, y: -height + midpoint },\n      { x: width, y: -height },\n    ];\n  }\n  if (directions.has('right') && directions.has('left')) {\n    // HORIZONTAL_LINE\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding },\n      { x: width - midpoint, y: -padding },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding },\n      { x: midpoint, y: -height + padding },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 },\n    ];\n  }\n  if (directions.has('up') && directions.has('down')) {\n    // VERTICAL_LINE\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding },\n      { x: midpoint, y: -padding },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding },\n      { x: 0, y: -height + padding },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding },\n      { x: width - midpoint, y: -padding },\n      { x: width, y: -padding },\n    ];\n  }\n  if (directions.has('right') && directions.has('up')) {\n    // ANGLE_RT\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: 0, y: -height },\n    ];\n  }\n  if (directions.has('right') && directions.has('down')) {\n    // ANGLE_RB\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: 0 },\n      { x: 0, y: -height },\n    ];\n  }\n  if (directions.has('left') && directions.has('up')) {\n    // ANGLE_LT\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: width, y: -height },\n    ];\n  }\n  if (directions.has('left') && directions.has('down')) {\n    // ANGLE_LB\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: 0 },\n      { x: width, y: -height },\n    ];\n  }\n  if (directions.has('right')) {\n    // ARROW_RIGHT\n    return [\n      { x: midpoint, y: -padding },\n      { x: midpoint, y: -padding },\n      { x: width - midpoint, y: -padding },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding },\n      // top left corner of arrow\n      { x: midpoint, y: -height + padding },\n      { x: midpoint, y: -height + padding },\n    ];\n  }\n  if (directions.has('left')) {\n    // ARROW_LEFT\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding },\n      // Two points, the right corners\n      { x: width - midpoint, y: -padding },\n      { x: width - midpoint, y: -height + padding },\n      { x: midpoint, y: -height + padding },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 },\n    ];\n  }\n  if (directions.has('up')) {\n    // ARROW_TOP\n    return [\n      // Bottom center\n      { x: midpoint, y: -padding },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding },\n      { x: 0, y: -height + padding },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding },\n      { x: width - midpoint, y: -padding },\n    ];\n  }\n  if (directions.has('down')) {\n    // ARROW_BOTTOM\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding },\n      { x: midpoint, y: -padding },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding },\n      { x: width - midpoint, y: -height + padding },\n      { x: width - midpoint, y: -padding },\n      { x: width, y: -padding },\n    ];\n  }\n\n  // POINT\n  return [{ x: 0, y: 0 }];\n};\n", "/**\n * @param node\n * @param point\n */\nfunction intersectNode(node, point) {\n  // console.info('Intersect Node');\n  return node.intersect(point);\n}\n\nexport default intersectNode;\n", "/**\n * @param node\n * @param rx\n * @param ry\n * @param point\n */\nfunction intersectEllipse(node, rx, ry, point) {\n  // Formulae from: https://mathworld.wolfram.com/Ellipse-LineIntersection.html\n\n  var cx = node.x;\n  var cy = node.y;\n\n  var px = cx - point.x;\n  var py = cy - point.y;\n\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n\n  var dx = Math.abs((rx * ry * px) / det);\n  if (point.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs((rx * ry * py) / det);\n  if (point.y < cy) {\n    dy = -dy;\n  }\n\n  return { x: cx + dx, y: cy + dy };\n}\n\nexport default intersectEllipse;\n", "import intersectEllipse from './intersect-ellipse.js';\n\n/**\n * @param node\n * @param rx\n * @param point\n */\nfunction intersectCircle(node, rx, point) {\n  return intersectEllipse(node, rx, rx, point);\n}\n\nexport default intersectCircle;\n", "/**\n * Returns the point at which two lines, p and q, intersect or returns undefined if they do not intersect.\n *\n * @param p1\n * @param p2\n * @param q1\n * @param q2\n */\nfunction intersectLine(p1, p2, q1, q2) {\n  // Algorithm from <PERSON><PERSON><PERSON>, (ed.) Graphics Gems, No 2, <PERSON>, 1994,\n  // p7 and p473.\n\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n\n  // Compute a1, b1, c1, where line joining points 1 and 2 is F(x,y) = a1 x +\n  // b1 y + c1 = 0.\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n\n  // Compute r3 and r4.\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n\n  // Check signs of r3 and r4. If both point 3 and point 4 lie on\n  // same side of line 1, the line segments do not intersect.\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Compute a2, b2, c2 where line joining points 3 and 4 is G(x,y) = a2 x + b2 y + c2 = 0\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n\n  // Compute r1 and r2\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n\n  // Check signs of r1 and r2. If both point 1 and point 2 lie\n  // on same side of second line segment, the line segments do\n  // not intersect.\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Line segments intersect: compute intersection point.\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return /*COLLINEAR*/;\n  }\n\n  offset = Math.abs(denom / 2);\n\n  // The denom/2 is to get rounding instead of truncating. It\n  // is added or subtracted to the numerator, depending upon the\n  // sign of the numerator.\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  return { x: x, y: y };\n}\n\n/**\n * @param r1\n * @param r2\n */\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n\nexport default intersectLine;\n", "/* eslint \"no-console\": off */\n\nimport intersectLine from './intersect-line.js';\n\nexport default intersectPolygon;\n\n/**\n * Returns the point ({x, y}) at which the point argument intersects with the node argument assuming\n * that it has the shape specified by polygon.\n *\n * @param node\n * @param polyPoints\n * @param point\n */\nfunction intersectPolygon(node, polyPoints, point) {\n  var x1 = node.x;\n  var y1 = node.y;\n\n  var intersections = [];\n\n  var minX = Number.POSITIVE_INFINITY;\n  var minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === 'function') {\n    polyPoints.forEach(function (entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n\n  var left = x1 - node.width / 2 - minX;\n  var top = y1 - node.height / 2 - minY;\n\n  for (var i = 0; i < polyPoints.length; i++) {\n    var p1 = polyPoints[i];\n    var p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    var intersect = intersectLine(\n      node,\n      point,\n      { x: left + p1.x, y: top + p1.y },\n      { x: left + p2.x, y: top + p2.y }\n    );\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n\n  if (!intersections.length) {\n    // console.log('NO INTERSECTION FOUND, RETURN NODE CENTER', node);\n    return node;\n  }\n\n  if (intersections.length > 1) {\n    // More intersections, find the one nearest to edge end point\n    intersections.sort(function (p, q) {\n      var pdx = p.x - point.x;\n      var pdy = p.y - point.y;\n      var distp = Math.sqrt(pdx * pdx + pdy * pdy);\n\n      var qdx = q.x - point.x;\n      var qdy = q.y - point.y;\n      var distq = Math.sqrt(qdx * qdx + qdy * qdy);\n\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n", "const intersectRect = (node, point) => {\n  var x = node.x;\n  var y = node.y;\n\n  // Rectangle intersection algorithm from:\n  // https://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n};\n\nexport default intersectRect;\n", "/*\n * Borrowed with love from dagre-d3. Many thanks to c<PERSON><PERSON><PERSON>!\n */\n\nimport node from './intersect-node.js';\nimport circle from './intersect-circle.js';\nimport ellipse from './intersect-ellipse.js';\nimport polygon from './intersect-polygon.js';\nimport rect from './intersect-rect.js';\n\nexport default {\n  node,\n  circle,\n  ellipse,\n  polygon,\n  rect,\n};\n", "import createLabel from '../createLabel.js';\nimport { createText } from '../../rendering-util/createText.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { select } from 'd3';\nimport { evaluate, sanitizeText } from '../../diagrams/common/common.js';\nimport { decodeEntities } from '../../utils.js';\n\nexport const labelHelper = async (parent, node, _classes, isNode) => {\n  const config = getConfig();\n  let classes;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(config.flowchart.htmlLabels);\n  if (!_classes) {\n    classes = 'node default';\n  } else {\n    classes = _classes;\n  }\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the label and insert it after the rect\n  const label = shapeSvg.insert('g').attr('class', 'label').attr('style', node.labelStyle);\n\n  // Replace labelText with default value if undefined\n  let labelText;\n  if (node.labelText === undefined) {\n    labelText = '';\n  } else {\n    labelText = typeof node.labelText === 'string' ? node.labelText : node.labelText[0];\n  }\n\n  const textNode = label.node();\n  let text;\n  if (node.labelType === 'markdown') {\n    // text = textNode;\n    text = createText(\n      label,\n      sanitizeText(decodeEntities(labelText), config),\n      {\n        useHtmlLabels,\n        width: node.width || config.flowchart.wrappingWidth,\n        classes: 'markdown-node-label',\n      },\n      config\n    );\n  } else {\n    text = textNode.appendChild(\n      await createLabel(\n        sanitizeText(decodeEntities(labelText), config),\n        node.labelStyle,\n        false,\n        isNode\n      )\n    );\n  }\n  // Get the size of the label\n  let bbox = text.getBBox();\n  const halfPadding = node.padding / 2;\n\n  if (evaluate(config.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n\n    // if there are images, need to wait for them to load before getting the bounding box\n    const images = div.getElementsByTagName('img');\n    if (images) {\n      const noImgText = labelText.replace(/<img[^>]*>/g, '').trim() === '';\n\n      await Promise.all(\n        [...images].map(\n          (img) =>\n            new Promise((res) => {\n              /**\n               *\n               */\n              function setupImage() {\n                img.style.display = 'flex';\n                img.style.flexDirection = 'column';\n\n                if (noImgText) {\n                  // default size if no text\n                  const bodyFontSize = config.fontSize\n                    ? config.fontSize\n                    : window.getComputedStyle(document.body).fontSize;\n                  const enlargingFactor = 5;\n                  const width = parseInt(bodyFontSize, 10) * enlargingFactor + 'px';\n                  img.style.minWidth = width;\n                  img.style.maxWidth = width;\n                } else {\n                  img.style.width = '100%';\n                }\n                res(img);\n              }\n              setTimeout(() => {\n                if (img.complete) {\n                  setupImage();\n                }\n              });\n              img.addEventListener('error', setupImage);\n              img.addEventListener('load', setupImage);\n            })\n        )\n      );\n    }\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center the label\n  if (useHtmlLabels) {\n    label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  } else {\n    label.attr('transform', 'translate(' + 0 + ', ' + -bbox.height / 2 + ')');\n  }\n  if (node.centerLabel) {\n    label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  }\n  label.insert('rect', ':first-child');\n\n  return { shapeSvg, bbox, halfPadding, label };\n};\n\nexport const updateNodeBounds = (node, element) => {\n  const bbox = element.node().getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n};\n\n/**\n * @param parent\n * @param w\n * @param h\n * @param points\n */\nexport function insertPolygonShape(parent, w, h, points) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('class', 'label-container')\n    .attr('transform', 'translate(' + -w / 2 + ',' + h / 2 + ')');\n}\n", "import { updateNodeBounds, labelHelper } from './util.js';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport intersect from '../intersect/index.js';\n\nconst note = async (parent, node) => {\n  const useHtmlLabels = node.useHtmlLabels || getConfig().flowchart.htmlLabels;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    'node ' + node.classes,\n    true\n  );\n\n  log.info('Classes = ', node.classes);\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  rect\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', -bbox.width / 2 - halfPadding)\n    .attr('y', -bbox.height / 2 - halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nexport default note;\n", "import { select } from 'd3';\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport { evaluate } from '../diagrams/common/common.js';\nimport { log } from '../logger.js';\nimport { getArrowPoints } from './blockArrowHelper.js';\nimport createLabel from './createLabel.js';\nimport intersect from './intersect/index.js';\nimport note from './shapes/note.js';\nimport { insertPolygonShape, labelHelper, updateNodeBounds } from './shapes/util.js';\n\nconst formatClass = (str) => {\n  if (str) {\n    return ' ' + str;\n  }\n  return '';\n};\nconst getClassesFromNode = (node, otherClasses) => {\n  return `${otherClasses ? otherClasses : 'node default'}${formatClass(node.classes)} ${formatClass(\n    node.class\n  )}`;\n};\n\nconst question = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n\n  const points = [\n    { x: s / 2, y: 0 },\n    { x: s, y: -s / 2 },\n    { x: s / 2, y: -s },\n    { x: 0, y: -s / 2 },\n  ];\n\n  log.info('Question main (Circle)');\n\n  const questionElem = insertPolygonShape(shapeSvg, s, s, points);\n  questionElem.attr('style', node.style);\n  updateNodeBounds(node, questionElem);\n\n  node.intersect = function (point) {\n    log.warn('Intersect called');\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst choice = (parent, node) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  const s = 28;\n  const points = [\n    { x: 0, y: s / 2 },\n    { x: s / 2, y: 0 },\n    { x: 0, y: -s / 2 },\n    { x: -s / 2, y: 0 },\n  ];\n\n  const choice = shapeSvg.insert('polygon', ':first-child').attr(\n    'points',\n    points\n      .map(function (d) {\n        return d.x + ',' + d.y;\n      })\n      .join(' ')\n  );\n  // center the circle around its coordinate\n  choice.attr('class', 'state-start').attr('r', 7).attr('width', 28).attr('height', 28);\n  node.width = 28;\n  node.height = 28;\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 14, point);\n  };\n\n  return shapeSvg;\n};\n\nconst hexagon = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n\n  const hex = insertPolygonShape(shapeSvg, w, h, points);\n  hex.attr('style', node.style);\n  updateNodeBounds(node, hex);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst block_arrow = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, undefined, true);\n\n  const f = 2;\n  const h = bbox.height + 2 * node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n\n  const points = getArrowPoints(node.directions, bbox, node);\n\n  const blockArrow = insertPolygonShape(shapeSvg, w, h, points);\n  blockArrow.attr('style', node.style);\n  updateNodeBounds(node, blockArrow);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst rect_left_inv_arrow = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -h / 2, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: -h / 2, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n\n  node.width = w + h;\n  node.height = h;\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst lean_right = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node), true);\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (-2 * h) / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + (2 * h) / 6, y: -h },\n    { x: h / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst lean_left = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (2 * h) / 6, y: 0 },\n    { x: w + h / 6, y: 0 },\n    { x: w - (2 * h) / 6, y: -h },\n    { x: -h / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst trapezoid = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (-2 * h) / 6, y: 0 },\n    { x: w + (2 * h) / 6, y: 0 },\n    { x: w - h / 6, y: -h },\n    { x: h / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst inv_trapezoid = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: h / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + (2 * h) / 6, y: -h },\n    { x: (-2 * h) / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst rect_right_inv_arrow = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w + h / 2, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w + h / 2, y: -h },\n    { x: 0, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst cylinder = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = bbox.height + ry + node.padding;\n\n  const shape =\n    'M 0,' +\n    ry +\n    ' a ' +\n    rx +\n    ',' +\n    ry +\n    ' 0,0,0 ' +\n    w +\n    ' 0 a ' +\n    rx +\n    ',' +\n    ry +\n    ' 0,0,0 ' +\n    -w +\n    ' 0 l 0,' +\n    h +\n    ' a ' +\n    rx +\n    ',' +\n    ry +\n    ' 0,0,0 ' +\n    w +\n    ' 0 l 0,' +\n    -h;\n\n  const el = shapeSvg\n    .attr('label-offset-y', ry)\n    .insert('path', ':first-child')\n    .attr('style', node.style)\n    .attr('d', shape)\n    .attr('transform', 'translate(' + -w / 2 + ',' + -(h / 2 + ry) + ')');\n\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const x = pos.x - node.x;\n\n    if (\n      rx != 0 &&\n      (Math.abs(x) < node.width / 2 ||\n        (Math.abs(x) == node.width / 2 && Math.abs(pos.y - node.y) > node.height / 2 - ry))\n    ) {\n      // ellipsis equation: x*x / a*a + y*y / b*b = 1\n      // solve for y to get adjusted value for pos.y\n      let y = ry * ry * (1 - (x * x) / (rx * rx));\n      if (y != 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - node.y > 0) {\n        y = -y;\n      }\n\n      pos.y += y;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n};\n\nconst rect = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    'node ' + node.classes + ' ' + node.class,\n    true\n  );\n\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // console.log('Rect node:', node, 'bbox:', bbox, 'halfPadding:', halfPadding, 'node.padding:', node.padding);\n  // const totalWidth = bbox.width + node.padding * 2;\n  // const totalHeight = bbox.height + node.padding * 2;\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect\n    .attr('class', 'basic label-container')\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', x)\n    .attr('y', y)\n    .attr('width', totalWidth)\n    .attr('height', totalHeight);\n\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete('borders');\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst composite = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    'node ' + node.classes,\n    true\n  );\n\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // const totalWidth = bbox.width + node.padding * 2;\n  // const totalHeight = bbox.height + node.padding * 2;\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect\n    .attr('class', 'basic cluster composite label-container')\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', x)\n    .attr('y', y)\n    .attr('width', totalWidth)\n    .attr('height', totalHeight);\n\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete('borders');\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst labelRect = async (parent, node) => {\n  const { shapeSvg } = await labelHelper(parent, node, 'label', true);\n\n  log.trace('Classes = ', node.class);\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // Hide the rect we are only after the label\n  const totalWidth = 0;\n  const totalHeight = 0;\n  rect.attr('width', totalWidth).attr('height', totalHeight);\n  shapeSvg.attr('class', 'label edgeLabel');\n\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete('borders');\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\n/**\n * @param rect\n * @param borders\n * @param totalWidth\n * @param totalHeight\n */\nfunction applyNodePropertyBorders(rect, borders, totalWidth, totalHeight) {\n  const strokeDashArray = [];\n  const addBorder = (length) => {\n    strokeDashArray.push(length, 0);\n  };\n  const skipBorder = (length) => {\n    strokeDashArray.push(0, length);\n  };\n  if (borders.includes('t')) {\n    log.debug('add top border');\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes('r')) {\n    log.debug('add right border');\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  if (borders.includes('b')) {\n    log.debug('add bottom border');\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes('l')) {\n    log.debug('add left border');\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  rect.attr('stroke-dasharray', strokeDashArray.join(' '));\n}\n\nconst rectWithTitle = async (parent, node) => {\n  // const { shapeSvg, bbox, halfPadding } = labelHelper(parent, node, 'node ' + node.classes);\n\n  let classes;\n  if (!node.classes) {\n    classes = 'node default';\n  } else {\n    classes = 'node ' + node.classes;\n  }\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the title label and insert it after the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n  // const innerRect = shapeSvg.insert('rect');\n  const innerLine = shapeSvg.insert('line');\n\n  const label = shapeSvg.insert('g').attr('class', 'label');\n\n  const text2 = node.labelText.flat ? node.labelText.flat() : node.labelText;\n  // const text2 = typeof text2prim === 'object' ? text2prim[0] : text2prim;\n\n  let title = '';\n  if (typeof text2 === 'object') {\n    title = text2[0];\n  } else {\n    title = text2;\n  }\n  log.info('Label text abc79', title, text2, typeof text2 === 'object');\n\n  const text = label.node().appendChild(await createLabel(title, node.labelStyle, true, true));\n  let bbox = { width: 0, height: 0 };\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  log.info('Text 2', text2);\n  const textRows = text2.slice(1, text2.length);\n  let titleBox = text.getBBox();\n  const descr = label\n    .node()\n    .appendChild(\n      await createLabel(\n        textRows.join ? textRows.join('<br/>') : textRows,\n        node.labelStyle,\n        true,\n        true\n      )\n    );\n\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = descr.children[0];\n    const dv = select(descr);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  // bbox = label.getBBox();\n  // log.info(descr);\n  const halfPadding = node.padding / 2;\n  select(descr).attr(\n    'transform',\n    'translate( ' +\n      // (titleBox.width - bbox.width) / 2 +\n      (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) +\n      ', ' +\n      (titleBox.height + halfPadding + 5) +\n      ')'\n  );\n  select(text).attr(\n    'transform',\n    'translate( ' +\n      // (titleBox.width - bbox.width) / 2 +\n      (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) +\n      ', ' +\n      0 +\n      ')'\n  );\n  // Get the size of the label\n\n  // Bounding box for title and text\n  bbox = label.node().getBBox();\n\n  // Center the label\n  label.attr(\n    'transform',\n    'translate(' + -bbox.width / 2 + ', ' + (-bbox.height / 2 - halfPadding + 3) + ')'\n  );\n\n  rect\n    .attr('class', 'outer title-state')\n    .attr('x', -bbox.width / 2 - halfPadding)\n    .attr('y', -bbox.height / 2 - halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  innerLine\n    .attr('class', 'divider')\n    .attr('x1', -bbox.width / 2 - halfPadding)\n    .attr('x2', bbox.width / 2 + halfPadding)\n    .attr('y1', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding)\n    .attr('y2', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst stadium = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n\n  // add the rect\n  const rect = shapeSvg\n    .insert('rect', ':first-child')\n    .attr('style', node.style)\n    .attr('rx', h / 2)\n    .attr('ry', h / 2)\n    .attr('x', -w / 2)\n    .attr('y', -h / 2)\n    .attr('width', w)\n    .attr('height', h);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst circle = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n  const circle = shapeSvg.insert('circle', ':first-child');\n\n  // center the circle around its coordinate\n  circle\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('r', bbox.width / 2 + halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  log.info('Circle main');\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    log.info('Circle intersect', node, bbox.width / 2 + halfPadding, point);\n    return intersect.circle(node, bbox.width / 2 + halfPadding, point);\n  };\n\n  return shapeSvg;\n};\n\nconst doublecircle = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n  const gap = 5;\n  const circleGroup = shapeSvg.insert('g', ':first-child');\n  const outerCircle = circleGroup.insert('circle');\n  const innerCircle = circleGroup.insert('circle');\n\n  circleGroup.attr('class', node.class);\n\n  // center the circle around its coordinate\n  outerCircle\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('r', bbox.width / 2 + halfPadding + gap)\n    .attr('width', bbox.width + node.padding + gap * 2)\n    .attr('height', bbox.height + node.padding + gap * 2);\n\n  innerCircle\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('r', bbox.width / 2 + halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  log.info('DoubleCircle main');\n\n  updateNodeBounds(node, outerCircle);\n\n  node.intersect = function (point) {\n    log.info('DoubleCircle intersect', node, bbox.width / 2 + halfPadding + gap, point);\n    return intersect.circle(node, bbox.width / 2 + halfPadding + gap, point);\n  };\n\n  return shapeSvg;\n};\n\nconst subroutine = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n    { x: 0, y: 0 },\n    { x: -8, y: 0 },\n    { x: w + 8, y: 0 },\n    { x: w + 8, y: -h },\n    { x: -8, y: -h },\n    { x: -8, y: 0 },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst start = (parent, node) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n  const circle = shapeSvg.insert('circle', ':first-child');\n\n  // center the circle around its coordinate\n  circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n};\n\nconst forkJoin = (parent, node, dir) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  let width = 70;\n  let height = 10;\n\n  if (dir === 'LR') {\n    width = 10;\n    height = 70;\n  }\n\n  const shape = shapeSvg\n    .append('rect')\n    .attr('x', (-1 * width) / 2)\n    .attr('y', (-1 * height) / 2)\n    .attr('width', width)\n    .attr('height', height)\n    .attr('class', 'fork-join');\n\n  updateNodeBounds(node, shape);\n  node.height = node.height + node.padding / 2;\n  node.width = node.width + node.padding / 2;\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst end = (parent, node) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n  const innerCircle = shapeSvg.insert('circle', ':first-child');\n  const circle = shapeSvg.insert('circle', ':first-child');\n\n  circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n\n  innerCircle.attr('class', 'state-end').attr('r', 5).attr('width', 10).attr('height', 10);\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n};\n\nconst class_box = async (parent, node) => {\n  const halfPadding = node.padding / 2;\n  const rowPadding = 4;\n  const lineHeight = 8;\n\n  let classes;\n  if (!node.classes) {\n    classes = 'node default';\n  } else {\n    classes = 'node ' + node.classes;\n  }\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the title label and insert it after the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n  const topLine = shapeSvg.insert('line');\n  const bottomLine = shapeSvg.insert('line');\n  let maxWidth = 0;\n  let maxHeight = rowPadding;\n\n  const labelContainer = shapeSvg.insert('g').attr('class', 'label');\n  let verticalPos = 0;\n  const hasInterface = node.classData.annotations?.[0];\n\n  // 1. Create the labels\n  const interfaceLabelText = node.classData.annotations[0]\n    ? '«' + node.classData.annotations[0] + '»'\n    : '';\n  const interfaceLabel = labelContainer\n    .node()\n    .appendChild(await createLabel(interfaceLabelText, node.labelStyle, true, true));\n  let interfaceBBox = interfaceLabel.getBBox();\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = interfaceLabel.children[0];\n    const dv = select(interfaceLabel);\n    interfaceBBox = div.getBoundingClientRect();\n    dv.attr('width', interfaceBBox.width);\n    dv.attr('height', interfaceBBox.height);\n  }\n  if (node.classData.annotations[0]) {\n    maxHeight += interfaceBBox.height + rowPadding;\n    maxWidth += interfaceBBox.width;\n  }\n\n  let classTitleString = node.classData.label;\n\n  if (node.classData.type !== undefined && node.classData.type !== '') {\n    if (getConfig().flowchart.htmlLabels) {\n      classTitleString += '&lt;' + node.classData.type + '&gt;';\n    } else {\n      classTitleString += '<' + node.classData.type + '>';\n    }\n  }\n  const classTitleLabel = labelContainer\n    .node()\n    .appendChild(await createLabel(classTitleString, node.labelStyle, true, true));\n  select(classTitleLabel).attr('class', 'classTitle');\n  let classTitleBBox = classTitleLabel.getBBox();\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = classTitleLabel.children[0];\n    const dv = select(classTitleLabel);\n    classTitleBBox = div.getBoundingClientRect();\n    dv.attr('width', classTitleBBox.width);\n    dv.attr('height', classTitleBBox.height);\n  }\n  maxHeight += classTitleBBox.height + rowPadding;\n  if (classTitleBBox.width > maxWidth) {\n    maxWidth = classTitleBBox.width;\n  }\n  const classAttributes = [];\n  node.classData.members.forEach(async (member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let parsedText = parsedInfo.displayText;\n    if (getConfig().flowchart.htmlLabels) {\n      parsedText = parsedText.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n    }\n    const lbl = labelContainer\n      .node()\n      .appendChild(\n        await createLabel(\n          parsedText,\n          parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n          true,\n          true\n        )\n      );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr('width', bbox.width);\n      dv.attr('height', bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n    classAttributes.push(lbl);\n  });\n\n  maxHeight += lineHeight;\n\n  const classMethods = [];\n  node.classData.methods.forEach(async (member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let displayText = parsedInfo.displayText;\n    if (getConfig().flowchart.htmlLabels) {\n      displayText = displayText.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n    }\n    const lbl = labelContainer\n      .node()\n      .appendChild(\n        await createLabel(\n          displayText,\n          parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n          true,\n          true\n        )\n      );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr('width', bbox.width);\n      dv.attr('height', bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n\n    classMethods.push(lbl);\n  });\n\n  maxHeight += lineHeight;\n\n  // 2. Position the labels\n\n  // position the interface label\n  if (hasInterface) {\n    let diffX = (maxWidth - interfaceBBox.width) / 2;\n    select(interfaceLabel).attr(\n      'transform',\n      'translate( ' + ((-1 * maxWidth) / 2 + diffX) + ', ' + (-1 * maxHeight) / 2 + ')'\n    );\n    verticalPos = interfaceBBox.height + rowPadding;\n  }\n  // Position the class title label\n  let diffX = (maxWidth - classTitleBBox.width) / 2;\n  select(classTitleLabel).attr(\n    'transform',\n    'translate( ' +\n      ((-1 * maxWidth) / 2 + diffX) +\n      ', ' +\n      ((-1 * maxHeight) / 2 + verticalPos) +\n      ')'\n  );\n  verticalPos += classTitleBBox.height + rowPadding;\n\n  topLine\n    .attr('class', 'divider')\n    .attr('x1', -maxWidth / 2 - halfPadding)\n    .attr('x2', maxWidth / 2 + halfPadding)\n    .attr('y1', -maxHeight / 2 - halfPadding + lineHeight + verticalPos)\n    .attr('y2', -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n\n  verticalPos += lineHeight;\n\n  classAttributes.forEach((lbl) => {\n    select(lbl).attr(\n      'transform',\n      'translate( ' +\n        -maxWidth / 2 +\n        ', ' +\n        ((-1 * maxHeight) / 2 + verticalPos + lineHeight / 2) +\n        ')'\n    );\n    //get the height of the bounding box of each member if exists\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n\n  verticalPos += lineHeight;\n  bottomLine\n    .attr('class', 'divider')\n    .attr('x1', -maxWidth / 2 - halfPadding)\n    .attr('x2', maxWidth / 2 + halfPadding)\n    .attr('y1', -maxHeight / 2 - halfPadding + lineHeight + verticalPos)\n    .attr('y2', -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n\n  verticalPos += lineHeight;\n\n  classMethods.forEach((lbl) => {\n    select(lbl).attr(\n      'transform',\n      'translate( ' + -maxWidth / 2 + ', ' + ((-1 * maxHeight) / 2 + verticalPos) + ')'\n    );\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n\n  rect\n    .attr('style', node.style)\n    .attr('class', 'outer title-state')\n    .attr('x', -maxWidth / 2 - halfPadding)\n    .attr('y', -(maxHeight / 2) - halfPadding)\n    .attr('width', maxWidth + node.padding)\n    .attr('height', maxHeight + node.padding);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst shapes = {\n  rhombus: question,\n  composite,\n  question,\n  rect,\n  labelRect,\n  rectWithTitle,\n  choice,\n  circle,\n  doublecircle,\n  stadium,\n  hexagon,\n  block_arrow,\n  rect_left_inv_arrow,\n  lean_right,\n  lean_left,\n  trapezoid,\n  inv_trapezoid,\n  rect_right_inv_arrow,\n  cylinder,\n  start,\n  end,\n  note,\n  subroutine,\n  fork: forkJoin,\n  join: forkJoin,\n  class_box,\n};\n\nlet nodeElems = {};\n\nexport const insertNode = async (elem, node, renderOptions) => {\n  let newEl;\n  let el;\n\n  // Add link when appropriate\n  if (node.link) {\n    let target;\n    if (getConfig().securityLevel === 'sandbox') {\n      target = '_top';\n    } else if (node.linkTarget) {\n      target = node.linkTarget || '_blank';\n    }\n    newEl = elem.insert('svg:a').attr('xlink:href', node.link).attr('target', target);\n    el = await shapes[node.shape](newEl, node, renderOptions);\n  } else {\n    el = await shapes[node.shape](elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr('title', node.tooltip);\n  }\n  if (node.class) {\n    el.attr('class', 'node default ' + node.class);\n  }\n\n  nodeElems[node.id] = newEl;\n\n  if (node.haveCallback) {\n    nodeElems[node.id].attr('class', nodeElems[node.id].attr('class') + ' clickable');\n  }\n  return newEl;\n};\nexport const setNodeElem = (elem, node) => {\n  nodeElems[node.id] = elem;\n};\nexport const clear = () => {\n  nodeElems = {};\n};\n\nexport const positionNode = (node) => {\n  const el = nodeElems[node.id];\n  log.trace(\n    'Transforming node',\n    node.diff,\n    node,\n    'translate(' + (node.x - node.width / 2 - 5) + ', ' + node.width / 2 + ')'\n  );\n  const padding = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\n      'transform',\n      'translate(' +\n        (node.x + diff - node.width / 2) +\n        ', ' +\n        (node.y - node.height / 2 - padding) +\n        ')'\n    );\n  } else {\n    el.attr('transform', 'translate(' + node.x + ', ' + node.y + ')');\n  }\n  return diff;\n};\n", "import * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport { getConfig } from '../../config.js';\nimport { insertEdge, insertEdgeLabel, positionEdgeLabel } from '../../dagre-wrapper/edges.js';\nimport { insertNode, positionNode } from '../../dagre-wrapper/nodes.js';\nimport { getStylesFromArray } from '../../utils.js';\nimport type { BlockDB } from './blockDB.js';\nimport type { Block } from './blockTypes.js';\n\nfunction getNodeFromBlock(block: Block, db: BlockDB, positioned = false) {\n  const vertex = block;\n\n  let classStr = 'default';\n  if ((vertex?.classes?.length || 0) > 0) {\n    classStr = (vertex?.classes ?? []).join(' ');\n  }\n  classStr = classStr + ' flowchart-label';\n\n  // We create a SVG label, either by delegating to addHtmlLabel or manually\n  let radius = 0;\n  let shape = '';\n  let padding;\n  // Set the shape based parameters\n  switch (vertex.type) {\n    case 'round':\n      radius = 5;\n      shape = 'rect';\n      break;\n    case 'composite':\n      radius = 0;\n      shape = 'composite';\n      padding = 0;\n      break;\n    case 'square':\n      shape = 'rect';\n      break;\n    case 'diamond':\n      shape = 'question';\n      break;\n    case 'hexagon':\n      shape = 'hexagon';\n      break;\n    case 'block_arrow':\n      shape = 'block_arrow';\n      break;\n    case 'odd':\n      shape = 'rect_left_inv_arrow';\n      break;\n    case 'lean_right':\n      shape = 'lean_right';\n      break;\n    case 'lean_left':\n      shape = 'lean_left';\n      break;\n    case 'trapezoid':\n      shape = 'trapezoid';\n      break;\n    case 'inv_trapezoid':\n      shape = 'inv_trapezoid';\n      break;\n    case 'rect_left_inv_arrow':\n      shape = 'rect_left_inv_arrow';\n      break;\n    case 'circle':\n      shape = 'circle';\n      break;\n    case 'ellipse':\n      shape = 'ellipse';\n      break;\n    case 'stadium':\n      shape = 'stadium';\n      break;\n    case 'subroutine':\n      shape = 'subroutine';\n      break;\n    case 'cylinder':\n      shape = 'cylinder';\n      break;\n    case 'group':\n      shape = 'rect';\n      break;\n    case 'doublecircle':\n      shape = 'doublecircle';\n      break;\n    default:\n      shape = 'rect';\n  }\n\n  const styles = getStylesFromArray(vertex?.styles ?? []);\n\n  // Use vertex id as text in the box if no text is provided by the graph definition\n  const vertexText = vertex.label;\n\n  const bounds = vertex.size ?? { width: 0, height: 0, x: 0, y: 0 };\n  // Add the node\n  const node = {\n    labelStyle: styles.labelStyle,\n    shape: shape,\n    labelText: vertexText,\n    rx: radius,\n    ry: radius,\n    class: classStr,\n    style: styles.style,\n    id: vertex.id,\n    directions: vertex.directions,\n    width: bounds.width,\n    height: bounds.height,\n    x: bounds.x,\n    y: bounds.y,\n    positioned,\n    intersect: undefined,\n    type: vertex.type,\n    padding: padding ?? getConfig()?.block?.padding ?? 0,\n  };\n  return node;\n}\nasync function calculateBlockSize(\n  elem: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  block: any,\n  db: any\n) {\n  const node = getNodeFromBlock(block, db, false);\n  if (node.type === 'group') {\n    return;\n  }\n\n  // Add the element to the DOM to size it\n  const config = getConfig();\n  const nodeEl = await insertNode(elem, node, { config });\n  const boundingBox = nodeEl.node().getBBox();\n  const obj = db.getBlock(node.id);\n  obj.size = { width: boundingBox.width, height: boundingBox.height, x: 0, y: 0, node: nodeEl };\n  db.setBlock(obj);\n  nodeEl.remove();\n}\ntype ActionFun = typeof calculateBlockSize;\n\nexport async function insertBlockPositioned(elem: any, block: Block, db: any) {\n  const node = getNodeFromBlock(block, db, true);\n  // Add the element to the DOM to size it\n  const obj = db.getBlock(node.id);\n  if (obj.type !== 'space') {\n    const config = getConfig();\n    await insertNode(elem, node, { config });\n    block.intersect = node?.intersect;\n    positionNode(node);\n  }\n}\n\nexport async function performOperations(\n  elem: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  blocks: Block[],\n  db: BlockDB,\n  operation: ActionFun\n) {\n  for (const block of blocks) {\n    await operation(elem, block, db);\n    if (block.children) {\n      await performOperations(elem, block.children, db, operation);\n    }\n  }\n}\n\nexport async function calculateBlockSizes(elem: any, blocks: Block[], db: BlockDB) {\n  await performOperations(elem, blocks, db, calculateBlockSize);\n}\n\nexport async function insertBlocks(\n  elem: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  blocks: Block[],\n  db: BlockDB\n) {\n  await performOperations(elem, blocks, db, insertBlockPositioned);\n}\n\nexport async function insertEdges(\n  elem: any,\n  edges: Block[],\n  blocks: Block[],\n  db: BlockDB,\n  id: string\n) {\n  const g = new graphlib.Graph({\n    multigraph: true,\n    compound: true,\n  });\n  g.setGraph({\n    rankdir: 'TB',\n    nodesep: 10,\n    ranksep: 10,\n    marginx: 8,\n    marginy: 8,\n  });\n\n  for (const block of blocks) {\n    if (block.size) {\n      g.setNode(block.id, {\n        width: block.size.width,\n        height: block.size.height,\n        intersect: block.intersect,\n      });\n    }\n  }\n\n  for (const edge of edges) {\n    // elem, e, edge, clusterDb, diagramType, graph;\n    if (edge.start && edge.end) {\n      const startBlock = db.getBlock(edge.start);\n      const endBlock = db.getBlock(edge.end);\n\n      if (startBlock?.size && endBlock?.size) {\n        const start = startBlock.size;\n        const end = endBlock.size;\n        const points = [\n          { x: start.x, y: start.y },\n          { x: start.x + (end.x - start.x) / 2, y: start.y + (end.y - start.y) / 2 },\n          { x: end.x, y: end.y },\n        ];\n        // edge.points = points;\n        insertEdge(\n          elem,\n          { v: edge.start, w: edge.end, name: edge.id },\n          {\n            ...edge,\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: 'edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1',\n          },\n          undefined,\n          'block',\n          g,\n          id\n        );\n        if (edge.label) {\n          await insertEdgeLabel(elem, {\n            ...edge,\n            label: edge.label,\n            labelStyle: 'stroke: #333; stroke-width: 1.5px;fill:none;',\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: 'edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1',\n          });\n          positionEdgeLabel(\n            { ...edge, x: points[1].x, y: points[1].y },\n            {\n              originalPath: points,\n            }\n          );\n        }\n      }\n    }\n  }\n}\n", "import { select as d3select } from 'd3';\nimport type { Diagram } from '../../Diagram.js';\nimport * as configApi from '../../config.js';\nimport insertMarkers from '../../dagre-wrapper/markers.js';\nimport { log } from '../../logger.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { BlockDB } from './blockDB.js';\nimport { layout } from './layout.js';\nimport { calculateBlockSizes, insertBlocks, insertEdges } from './renderHelpers.js';\n\nexport const getClasses = function (text: any, diagObj: any) {\n  return diagObj.db.getClasses();\n};\n\nexport const draw = async function (\n  text: string,\n  id: string,\n  _version: string,\n  diagObj: Diagram\n): Promise<void> {\n  const { securityLevel, block: conf } = configApi.getConfig();\n  const db = diagObj.db as BlockDB;\n  let sandboxElement: any;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = d3select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? d3select<HTMLBodyElement, unknown>(sandboxElement.nodes()[0].contentDocument.body)\n      : d3select<HTMLBodyElement, unknown>('body');\n\n  const svg =\n    securityLevel === 'sandbox'\n      ? root.select<SVGSVGElement>(`[id=\"${id}\"]`)\n      : d3select<SVGSVGElement, unknown>(`[id=\"${id}\"]`);\n\n  // Define the supported markers for the diagram\n  const markers = ['point', 'circle', 'cross'];\n\n  // Add the marker definitions to the svg as marker tags\n  insertMarkers(svg, markers, diagObj.type, id);\n\n  const bl = db.getBlocks();\n  const blArr = db.getBlocksFlat();\n  const edges = db.getEdges();\n\n  const nodes = svg.insert('g').attr('class', 'block');\n  await calculateBlockSizes(nodes, bl, db);\n  const bounds = layout(db);\n  await insertBlocks(nodes, bl, db);\n  await insertEdges(nodes, edges, blArr, db, id);\n\n  // Establish svg dimensions and get width and height\n  // Why, oh why ????\n  if (bounds) {\n    const bounds2 = bounds;\n    const magicFactor = Math.max(1, Math.round(0.125 * (bounds2.width / bounds2.height)));\n    const height = bounds2.height + magicFactor + 10;\n    const width = bounds2.width + 10;\n    const { useMaxWidth } = conf!;\n    configureSvgSize(svg, height, width, !!useMaxWidth);\n    log.debug('Here Bounds', bounds, bounds2);\n    svg.attr(\n      'viewBox',\n      `${bounds2.x - 5} ${bounds2.y - 5} ${bounds2.width + 10} ${bounds2.height + 10}`\n    );\n  }\n};\n\nexport default {\n  draw,\n  getClasses,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: jison doesn't export types\nimport parser from './parser/block.jison';\nimport db from './blockDB.js';\nimport flowStyles from './styles.js';\nimport renderer from './blockRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles: flowStyles,\n};\n"], "mappings": "8kBAyEA,IAAIA,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,EAAE,OAAOE,IAAIJ,EAAEE,EAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAC7TpB,EAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,WAAa,EAAE,UAAY,EAAE,GAAK,EAAE,UAAY,EAAE,MAAQ,EAAE,IAAM,EAAE,MAAQ,EAAE,kBAAoB,GAAG,SAAW,GAAG,KAAO,GAAG,UAAY,GAAG,KAAO,GAAG,KAAO,GAAG,WAAa,GAAG,WAAa,GAAG,IAAM,GAAG,cAAgB,GAAG,iBAAmB,GAAG,YAAc,GAAG,eAAiB,GAAG,kBAAoB,GAAG,kBAAoB,GAAG,eAAiB,GAAG,KAAO,GAAG,KAAO,GAAG,QAAU,GAAG,WAAW,GAAG,IAAM,GAAG,MAAQ,GAAG,QAAU,GAAG,gBAAkB,GAAG,QAAU,GAAG,IAAM,GAAG,YAAc,GAAG,UAAY,GAAG,kBAAoB,GAAG,gBAAkB,GAAG,SAAW,GAAG,YAAc,GAAG,mBAAqB,GAAG,QAAU,GAAG,MAAQ,GAAG,gBAAkB,GAAG,WAAa,GAAG,MAAQ,GAAG,iBAAmB,GAAG,sBAAwB,GAAG,QAAU,EAAE,KAAO,CAAC,EACzvB,WAAY,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,GAAG,oBAAoB,GAAG,OAAO,GAAG,aAAa,GAAG,aAAa,GAAG,MAAM,GAAG,cAAc,GAAG,OAAO,GAAG,UAAU,GAAG,WAAW,GAAG,MAAM,GAAG,QAAQ,GAAG,UAAU,GAAG,MAAM,GAAG,cAAc,GAAG,YAAY,GAAG,oBAAoB,GAAG,kBAAkB,GAAG,WAAW,GAAG,cAAc,GAAG,qBAAqB,GAAG,UAAU,GAAG,QAAQ,GAAG,kBAAkB,GAAG,aAAa,GAAG,QAAQ,GAAG,mBAAmB,GAAG,uBAAuB,EACne,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EACnR,cAAeA,EAAA,SAAmBmB,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,EAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GACLD,EAAG,UAAU,EAAE,MAAM,uBAAuB,EAC5C,MACA,IAAK,GACLA,EAAG,UAAU,EAAE,MAAM,0BAA0B,EAC/C,MACA,IAAK,GACLA,EAAG,UAAU,EAAE,MAAM,wBAAwB,EAC7C,MACA,IAAK,GACJA,EAAG,UAAU,EAAE,MAAM,oBAAqBE,EAAGE,EAAG,CAAC,CAAC,EAAGJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAC9E,MACA,IAAK,GACLJ,EAAG,UAAU,EAAE,MAAM,UAAU,EAC/B,MACA,IAAK,GACLA,EAAG,UAAU,EAAE,MAAM,WAAW,EAChC,MACA,IAAK,IACLA,EAAG,UAAU,EAAE,MAAM,WAAW,EAChC,MACA,IAAK,IACLA,EAAG,UAAU,EAAE,MAAM,YAAY,EACjC,MACA,IAAK,IACJA,EAAG,UAAU,EAAE,MAAM,oBAAqBE,EAAGE,CAAE,CAAC,EAAG,OAAOF,EAAGE,CAAE,EAAE,QAAW,SAAS,KAAK,EAAIF,EAAGE,CAAE,EAAE,KAAK,EAAI,CAACF,EAAGE,CAAE,CAAC,EACtH,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,uBAAwBE,EAAGE,EAAG,CAAC,CAAC,EAAG,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,CAAC,EAAE,OAAOF,EAAGE,CAAE,CAAC,EAC1F,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,eAAgBE,EAAGE,CAAE,EAAGP,CAAM,EAAG,KAAK,EAAE,CAAC,YAAaK,EAAGE,CAAE,EAAG,MAAM,EAAE,EAC5F,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,qBAAsBE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAE,CAAC,YAAaF,EAAGE,CAAE,EAAG,MAAMF,EAAGE,EAAG,CAAC,CAAC,EACpH,MACA,IAAK,IACJ,IAAMC,EAAI,SAASH,EAAGE,CAAE,CAAC,EAASE,EAAUN,EAAG,WAAW,EAAG,KAAK,EAAI,CAAE,GAAIM,EAAS,KAAK,QAAS,MAAM,GAAI,MAAOD,EAAK,SAAU,CAAC,CAAE,EACvI,MACA,IAAK,IAEDL,EAAG,UAAU,EAAE,MAAM,mCAAoCE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,EAAG,aAAaF,EAAGE,EAAG,CAAC,EAAE,WAAW,EACtH,IAAMG,EAAWP,EAAG,kBAAkBE,EAAGE,EAAG,CAAC,EAAE,WAAW,EAC1D,KAAK,EAAI,CACP,CAAC,GAAIF,EAAGE,EAAG,CAAC,EAAE,GAAI,MAAOF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAKF,EAAGE,EAAG,CAAC,EAAE,KAAM,WAAYF,EAAGE,EAAG,CAAC,EAAE,UAAU,EAC5F,CAAC,GAAIF,EAAGE,EAAG,CAAC,EAAE,GAAK,IAAMF,EAAGE,CAAE,EAAE,GAAI,MAAOF,EAAGE,EAAG,CAAC,EAAE,GAAI,IAAKF,EAAGE,CAAE,EAAE,GAAI,MAAOF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAM,OAAQ,WAAYF,EAAGE,CAAE,EAAE,WAAY,aAAcG,EAAU,eAAgB,YAAa,EACjM,CAAC,GAAIL,EAAGE,CAAE,EAAE,GAAI,MAAOF,EAAGE,CAAE,EAAE,MAAO,KAAMJ,EAAG,aAAaE,EAAGE,CAAE,EAAE,OAAO,EAAG,WAAYF,EAAGE,CAAE,EAAE,UAAU,CACzG,EAEN,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,yCAA0CE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAAC,GAAIF,EAAGE,EAAG,CAAC,EAAE,GAAI,MAAOF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAMJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,EAAE,OAAO,EAAG,WAAYF,EAAGE,EAAG,CAAC,EAAE,WAAY,eAAgB,SAASF,EAAGE,CAAE,EAAE,EAAE,CAAC,EAClP,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,8BAA+BE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAAC,GAAIF,EAAGE,CAAE,EAAE,GAAI,MAAOF,EAAGE,CAAE,EAAE,MAAO,KAAMJ,EAAG,aAAaE,EAAGE,CAAE,EAAE,OAAO,EAAG,WAAYF,EAAGE,CAAE,EAAE,WAAY,eAAe,CAAC,EAClM,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,SAAU,KAAM,KAAK,IAAI,EAAGA,EAAG,UAAU,EAAE,MAAM,YAAaE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAAC,KAAM,iBAAkB,QAASF,EAAGE,CAAE,IAAM,OAAO,GAAG,SAASF,EAAGE,CAAE,CAAC,CAAE,EAC9K,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,8BAA+BE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,CAAC,EAAG,IAAMI,GAAMR,EAAG,WAAW,EAAG,KAAK,EAAI,CAAE,GAAGE,EAAGE,EAAG,CAAC,EAAG,KAAK,YAAa,SAAUF,EAAGE,EAAG,CAAC,CAAE,EACpK,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,0BAA2BE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAAG,IAAMK,GAAKT,EAAG,WAAW,EAAG,KAAK,EAAI,CAAE,GAAAS,GAAI,KAAK,YAAa,MAAM,GAAI,SAAUP,EAAGE,EAAG,CAAC,CAAE,EACxK,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,mCAAoCE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAAE,GAAIF,EAAGE,CAAE,CAAE,EACzF,MACA,IAAK,IAEDJ,EAAG,UAAU,EAAE,MAAM,mDAAoDE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EACzF,KAAK,EAAI,CAAE,GAAIF,EAAGE,EAAG,CAAC,EAAG,MAAOF,EAAGE,CAAE,EAAE,MAAO,QAASF,EAAGE,CAAE,EAAE,QAAS,WAAYF,EAAGE,CAAE,EAAE,UAAW,EAEzG,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,kBAAmBE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAACF,EAAGE,CAAE,CAAC,EAClE,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,kBAAmBE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,CAAC,EAAE,OAAOF,EAAGE,CAAE,CAAC,EAC7F,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,0BAA2BE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAAE,QAASF,EAAGE,EAAG,CAAC,EAAIF,EAAGE,CAAE,EAAG,MAAOF,EAAGE,EAAG,CAAC,CAAE,EACrI,MACA,IAAK,IACJJ,EAAG,UAAU,EAAE,MAAM,sCAAuCE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAG,OAAOF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAI,CAAE,QAASF,EAAGE,EAAG,CAAC,EAAIF,EAAGE,CAAE,EAAG,MAAOF,EAAGE,EAAG,CAAC,EAAG,WAAYF,EAAGE,EAAG,CAAC,CAAC,EACvL,MACA,IAAK,IAAI,IAAK,IAER,KAAK,EAAI,CAAE,KAAM,WAAY,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,IAAKF,EAAGE,CAAE,EAAE,KAAK,CAAE,EAE3E,MACA,IAAK,IAGG,KAAK,EAAE,CAAE,KAAM,aAAc,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,WAAYF,EAAGE,CAAE,EAAE,KAAK,CAAE,EAEpF,MACA,IAAK,IAEG,KAAK,EAAE,CAAE,KAAM,cAAe,GAAIF,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAG,UAAWF,EAAGE,CAAE,EAAE,KAAK,CAAE,EAEpF,KACA,CACA,EA5Ge,aA6Gf,MAAO,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAGtB,EAAI,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEa,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGR,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEZ,EAAEc,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEhB,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEiB,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAEjB,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGL,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAGJ,EAAI,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEkB,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAElB,EAAEa,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGJ,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAET,EAAEiB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAGF,EAAI,GAAGC,EAAI,GAAG,EAAE,GAAG,EAAE,GAAGX,EAAI,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEkB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAElB,EAAEiB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEjB,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGK,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEnB,EAAEc,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEd,EAAEkB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGC,EAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEnB,EAAEkB,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAC5qC,eAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EACvD,WAAYjB,EAAA,SAAqBgC,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOlC,EAAA,SAAemC,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOtB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGsB,EAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAAS9C,MAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IAC/C8C,EAAY,GAAG9C,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGrC6C,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,GAAQF,EAAM,OAClBN,EAAO,KAAKQ,EAAK,EACjB,IAAIC,GAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJSnD,EAAAkD,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,GACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXarD,EAAAoD,GAAA,OAajB,QADIE,EAAQC,GAAgBC,EAAOC,EAAQC,GAAGC,GAAGC,EAAQ,CAAC,EAAGC,GAAGC,EAAKC,GAAUC,KAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,GAAS,GACbD,GAAW,CAAC,EACZ,IAAKH,MAAKpB,EAAMe,CAAK,EACb,KAAK,WAAWK,EAAC,GAAKA,GAAIlB,IAC1BqB,GAAS,KAAK,IAAO,KAAK,WAAWH,EAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,GAAS,wBAA0B5C,EAAW,GAAK;AAAA,EAAQyB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,GAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0B5C,EAAW,GAAK,iBAAmBiC,GAAUV,GAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,GAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,GACL,SAAUgB,EACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,IASDD,EAASC,GACTA,GAAiB,OATjBnC,EAAS0B,EAAM,OACf3B,EAAS2B,EAAM,OACfzB,EAAWyB,EAAM,SACjBE,GAAQF,EAAM,OACVJ,EAAa,GACbA,KAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,KACAW,EAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,GAAI,KAAK,cAAc,MAAMC,EAAO,CAChCzC,EACAC,EACAC,EACA0B,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,GAAM,IACb,OAAOA,GAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,GAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,EAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAW9C,EAAA,SAAoBgC,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAAShC,EAAA,SAAUmC,EAAOb,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASa,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAMnC,EAAA,UAAY,CACV,IAAIkE,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMlE,EAAA,SAAUkE,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAK9D,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAUmD,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUnD,EAAA,UAAY,CACd,IAAIqE,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAcrE,EAAA,UAAY,CAClB,IAAIsE,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAatE,EAAA,UAAY,CACjB,IAAIuE,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAWxE,EAAA,SAASyE,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAASpD,KAAK0E,EACV,KAAK1E,CAAC,EAAI0E,EAAO1E,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIqD,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAIrD,EAAA,UAAgB,CACZ,IAAI2D,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAM3D,EAAA,SAAgBgF,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAAShF,EAAA,UAAqB,CACtB,IAAImD,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAcnD,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmBmD,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAUnD,EAAA,SAAoBgF,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAehF,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,EACV,cAAeA,EAAA,SAAmBsB,EAAG2D,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GAAG,MAAO,IAEf,IAAK,GAAG,OAAA5D,EAAG,UAAU,EAAE,MAAM,mBAAmB,EAAU,GAC1D,MACA,IAAK,GAAG,OAAAA,EAAG,UAAU,EAAE,MAAM,gBAAgB,EAAU,GACvD,MACA,IAAK,GAAG,OAAAA,EAAG,UAAU,EAAE,MAAM,mBAAmB,EAAU,GAC1D,MACA,IAAK,GAAGA,EAAG,UAAU,EAAE,MAAM,IAAK2D,EAAI,MAAM,EAC5C,MACA,IAAK,GAAE3D,EAAG,UAAU,EAAE,MAAM,IAAK2D,EAAI,MAAM,EAC3C,MACA,IAAK,GAAG,MAAO,GAEf,IAAK,GAAG,OAAAA,EAAI,OAAO,GAAW,GAC9B,MACA,IAAK,GAAG,OAAAA,EAAI,OAASA,EAAI,OAAO,QAAQ,aAAa,EAAE,EAAG3D,EAAG,UAAU,EAAE,MAAM,gBAAiB2D,EAAI,MAAM,EAAU,GACpH,MACA,IAAK,GAAG,KAAK,UAAU,WAAW,EAClC,MACA,IAAK,IAAI,MAAO,SAEhB,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,KAAK,UAAU,QAAQ,EAC/B,MACA,IAAK,IAAI3D,EAAG,UAAU,EAAE,MAAM,oBAAqB2D,EAAI,MAAM,EAAE,KAAK,SAAS,EAC7E,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,gBAAiB2D,EAAI,MAAM,EAAU,MACnE,MACA,IAAK,IAAK,OAAAA,EAAI,OAASA,EAAI,OAAO,QAAQ,UAAU,EAAE,EAAE3D,EAAG,UAAU,EAAE,MAAM,kBAAmB2D,EAAI,MAAM,EAAU,GACpH,MACA,IAAK,IAAI,OAAAA,EAAI,OAAS,IAAK3D,EAAG,UAAU,EAAE,MAAM,gBAAiB2D,EAAI,MAAM,EAAU,GACrF,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,YAEf,IAAK,IAAG,MAAO,cAEf,IAAK,IAAI,YAAK,UAAU,UAAU,EAAU,GAC5C,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,YAAY,EAAU,sBAC/D,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,YAAY,EAAU,GAC/D,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAI,YAAK,UAAU,OAAO,EAAU,GACzC,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,aAAa,EAAU,GAChE,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAI,YAAK,UAAU,aAAa,EAAU,GAC/C,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,UAAU,kBAAkB,EAAU,GACrE,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAI,YAAK,UAAU,WAAW,EAAS,YAC5C,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,YAAK,UAAU,WAAW,EAAS,YAC5C,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,KAAK,UAAU,qBAAqB,EAC7C,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,MAAO,4BAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,YAAK,SAAS,EAAE3D,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,QAAQ,EAAW,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,QAAQ,EAAW,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAU,YACjE,MACA,IAAK,IAAI,YAAK,SAAS,EAAEA,EAAG,UAAU,EAAE,MAAM,QAAQ,EAAU,YAChE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,UAAU,EAAG,KAAK,UAAU,MAAM,EAAS,GACzE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,UAAU,EAAG,KAAK,UAAU,MAAM,EAAS,GACzE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,UAAU,EAAG,KAAK,UAAU,MAAM,EAAS,GACzE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAG,KAAK,UAAU,MAAM,EAAS,GACxE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,UAAU,EAAI,KAAK,UAAU,MAAM,EAAS,GAC1E,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAG,KAAK,UAAU,MAAM,EAAS,GACxE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAG,KAAK,UAAU,MAAM,EAAS,GACxE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAG,KAAK,UAAU,MAAM,EAAS,GACxE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAG,KAAK,UAAU,MAAM,EAAS,GACxE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,UAAU,EAAG,KAAK,UAAU,MAAM,EAAS,GACzE,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAG,KAAK,UAAU,MAAM,EAAS,GACxE,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAS,GACvC,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,SAAS,EAAG,KAAK,UAAU,MAAM,EAAS,GACxE,MACA,IAAK,IAAI,YAAK,UAAU,aAAa,EAAEA,EAAG,UAAU,EAAE,MAAM,eAAe,EAAS,GACpF,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,eAAgB2D,EAAI,MAAM,EAAS,GACjE,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,WAAY2D,EAAI,MAAM,EAAS,EAC7D,MACA,IAAK,IAAI,KAAK,UAAU,WAAW,EACnC,MACA,IAAK,IAAI,KAAK,UAAU,WAAW,EACnC,MACA,IAAK,IAAI,MAAO,aAEhB,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAI3D,EAAG,UAAU,EAAE,MAAM,sBAAsB,EAAE,KAAK,UAAU,QAAQ,EAC7E,MACA,IAAK,IAAIA,EAAG,UAAU,EAAE,MAAM,0BAA0B,EAAE,KAAK,UAAU,QAAQ,EACjF,MACA,IAAK,IAAI,OAAAA,EAAG,UAAU,EAAE,MAAM,mBAAoB2D,EAAI,MAAM,EAAU,aACtE,MACA,IAAK,IAAG3D,EAAG,UAAU,EAAE,MAAM,aAAa,EAAE,KAAK,SAAS,EAC1D,MACA,IAAK,IAAIA,EAAG,UAAU,EAAE,MAAM,YAAY,EAAI,KAAK,UAAU,WAAW,EACxE,MACA,IAAK,IAAI,OAAA2D,EAAI,OAASA,EAAI,OAAO,QAAQ,QAAS,EAAE,EAAG3D,EAAG,UAAU,EAAE,MAAM,oBAAoB2D,EAAI,MAAM,EAAS,MACnH,MACA,IAAK,IAAI,OAAAA,EAAI,OAASA,EAAI,OAAO,QAAQ,QAAS,EAAE,EAAG3D,EAAG,UAAU,EAAE,MAAM,cAAc2D,EAAI,MAAM,EAAS,MAC7G,MACA,IAAK,IAAI,OAAAA,EAAI,OAASA,EAAI,OAAO,QAAQ,QAAS,EAAE,EAAG3D,EAAG,UAAU,EAAE,MAAM,WAAW2D,EAAI,MAAM,EAAU,MAC3G,MACA,IAAK,IAAI,OAAAA,EAAI,OAASA,EAAI,OAAO,QAAQ,QAAS,EAAE,EAAG3D,EAAG,UAAU,EAAE,MAAM,WAAW2D,EAAI,MAAM,EAAU,MAC3G,MACA,IAAK,IAAI,OAAAA,EAAI,OAASA,EAAI,OAAO,QAAQ,QAAS,EAAE,EAAG3D,EAAG,UAAU,EAAE,MAAM,YAAY2D,EAAI,MAAM,EAAU,MAC5G,MACA,IAAK,IAAI,OAAAA,EAAI,OAASA,EAAI,OAAO,QAAQ,QAAS,EAAE,EAAG3D,EAAG,UAAU,EAAE,MAAM,cAAc2D,EAAI,MAAM,EAAU,MAC9G,MACA,IAAK,IAAI,OAAAA,EAAI,OAAO,KAAK3D,EAAG,UAAU,EAAE,MAAM,uBAAuB2D,EAAI,MAAM,EAAE,KAAK,SAAS,EAAE,KAAK,SAAS,EAAS,kBACxH,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,YAAa,IAAI2D,EAAI,OAAO,GAAG,EAAU,GACvE,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,YAAa2D,EAAI,MAAM,EAAU,GAC/D,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,YAAa2D,EAAI,MAAM,EAAU,GAC/D,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,YAAa2D,EAAI,MAAM,EAAU,GAC/D,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,kBAAmB2D,EAAI,MAAM,EAAE,KAAK,UAAU,QAAQ,EAAS,GAC7F,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,kBAAmB2D,EAAI,MAAM,EAAE,KAAK,UAAU,QAAQ,EAAS,GAC7F,MACA,IAAK,IAAI,OAAA3D,EAAG,UAAU,EAAE,MAAM,kBAAmB2D,EAAI,MAAM,EAAE,KAAK,UAAU,QAAQ,EAAS,GAC7F,MACA,IAAK,KAAK,KAAK,UAAU,WAAW,EACpC,MACA,IAAK,KAAK,OAAA3D,EAAG,UAAU,EAAE,MAAM,sBAAsB,EAAE,KAAK,UAAU,QAAQ,EAAU,aACxF,MACA,IAAK,KAAK,YAAK,SAAS,EAAGA,EAAG,UAAU,EAAE,MAAM,YAAa,IAAI2D,EAAI,OAAO,GAAG,EAAU,GACzF,MACA,IAAK,KAAK,YAAK,SAAS,EAAG3D,EAAG,UAAU,EAAE,MAAM,YAAa2D,EAAI,MAAM,EAAU,GACjF,MACA,IAAK,KAAK,YAAK,SAAS,EAAG3D,EAAG,UAAU,EAAE,MAAM,YAAa2D,EAAI,MAAM,EAAU,GACjF,MACA,IAAK,KAAK,OAAA3D,EAAG,UAAU,EAAE,MAAM,aAAc2D,EAAI,MAAM,EAAGA,EAAI,OAAOA,EAAI,OAAO,MAAM,CAAC,EAAS,GAChG,KACA,CACA,EAxNe,aAyNf,MAAO,CAAC,oBAAoB,gBAAgB,gBAAgB,cAAc,aAAa,aAAa,iCAAiC,wBAAwB,uBAAuB,cAAc,cAAc,cAAc,WAAW,WAAW,aAAa,mBAAmB,eAAe,iBAAiB,mBAAmB,qBAAqB,mBAAmB,kBAAkB,cAAc,cAAc,gBAAgB,0BAA0B,cAAc,gBAAgB,0BAA0B,cAAc,uBAAuB,uBAAuB,uBAAuB,uBAAuB,wBAAwB,YAAY,cAAc,gBAAgB,cAAc,cAAc,cAAc,YAAY,UAAU,WAAW,WAAW,YAAY,YAAY,UAAU,YAAY,YAAY,YAAY,YAAY,YAAY,WAAW,YAAY,WAAW,WAAW,YAAY,UAAU,cAAc,YAAY,YAAY,UAAU,SAAS,YAAY,UAAU,YAAY,YAAY,YAAY,cAAc,YAAY,YAAY,YAAY,UAAU,WAAW,iCAAiC,SAAS,cAAc,cAAc,cAAc,cAAc,WAAW,WAAW,aAAa,WAAW,gBAAgB,qBAAqB,oBAAoB,iBAAiB,iBAAiB,kBAAkB,oBAAoB,aAAa,6BAA6B,6BAA6B,gCAAgC,qBAAqB,sBAAsB,sBAAsB,uBAAuB,cAAc,WAAW,6BAA6B,6BAA6B,gCAAgC,WAAW,EAC1qD,WAAY,CAAC,iBAAmB,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,YAAc,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,WAAa,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,SAAW,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,YAAc,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,YAAc,CAAC,MAAQ,CAAC,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,KAAO,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,UAAY,EAAI,CAAC,CAClhC,EACA,OAAOnC,CACP,EAAG,EACHhD,EAAO,MAAQgD,EACf,SAASuC,GAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAArF,EAAAqF,EAAA,UAGTA,EAAO,UAAYvF,EAAOA,EAAO,OAASuF,EACnC,IAAIA,CACX,EAAG,EACFvF,GAAO,OAASA,GAEhB,IAAOwF,GAAQC,GCv3BhB,IAAIC,EAAgB,IAAI,IACpBC,GAAoB,CAAC,EACrBC,GAAY,IAAI,IAEdC,GAAgB,QAChBC,GAAe,OACfC,GAAU,SACVC,GAAiB,IACjBC,GAASC,EAAU,EAErBC,GAAU,IAAI,IAEZC,GAAeC,EAACC,GAAgBC,GAAO,aAAaD,EAAKL,EAAM,EAAhD,gBASRO,GAAgBH,EAAA,SAAUI,EAAYC,EAAkB,GAAI,CAEvE,IAAIC,EAAaR,GAAQ,IAAIM,CAAE,EAC1BE,IACHA,EAAa,CAAE,GAAIF,EAAI,OAAQ,CAAC,EAAG,WAAY,CAAC,CAAE,EAClDN,GAAQ,IAAIM,EAAIE,CAAU,GAG1BD,GAAgB,MAAMV,EAAc,EAAE,QAASY,GAAW,CAExD,IAAMC,EAAcD,EAAO,QAAQ,WAAY,IAAI,EAAE,KAAK,EAG1D,GAAI,OAAOf,EAAa,EAAE,KAAKe,CAAM,EAAG,CAEtC,IAAME,EADYD,EAAY,QAAQf,GAAcC,EAAO,EAC/B,QAAQF,GAAeC,EAAY,EAC/Da,EAAW,WAAW,KAAKG,CAAS,CACtC,CACAH,EAAW,OAAO,KAAKE,CAAW,CACpC,CAAC,CAEL,EArB6B,iBA8BhBE,GAAgBV,EAAA,SAAUI,EAAYO,EAAS,GAAI,CAC9D,IAAMC,EAAavB,EAAc,IAAIe,CAAE,EACXO,GAAW,OACrCC,EAAW,OAASD,EAAO,MAAMhB,EAAc,EAEnD,EAL6B,iBAehBkB,GAAcb,EAAA,SAAUc,EAAiBC,EAAsB,CAC1ED,EAAQ,MAAM,GAAG,EAAE,QAAQ,SAAUV,EAAY,CAC/C,IAAIQ,EAAavB,EAAc,IAAIe,CAAE,EACrC,GAAIQ,IAAe,OAAW,CAC5B,IAAMI,EAAYZ,EAAG,KAAK,EAC1BQ,EAAa,CAAE,GAAII,EAAW,KAAM,KAAM,SAAU,CAAC,CAAE,EACvD3B,EAAc,IAAI2B,EAAWJ,CAAU,CACzC,CACKA,EAAW,UACdA,EAAW,QAAU,CAAC,GAExBA,EAAW,QAAQ,KAAKG,CAAY,CACtC,CAAC,CACH,EAb2B,eAerBE,GAAwBjB,EAAA,CAACkB,EAAqBC,IAAwB,CAC1E,IAAMC,EAAYF,EAAW,KAAK,EAC5BG,EAAW,CAAC,EAClB,QAAWC,KAASF,EAAW,CAI7B,GAHIE,EAAM,QACRA,EAAM,MAAQvB,GAAauB,EAAM,KAAK,GAEpCA,EAAM,OAAS,WAAY,CAC7BnB,GAAcmB,EAAM,GAAIA,EAAM,GAAG,EACjC,QACF,CACA,GAAIA,EAAM,OAAS,aAAc,CAC/BT,GAAYS,EAAM,GAAIA,GAAO,YAAc,EAAE,EAC7C,QACF,CACA,GAAIA,EAAM,OAAS,cAAe,CAC5BA,GAAO,WACTZ,GAAcY,EAAM,GAAIA,GAAO,SAAS,EAE1C,QACF,CACA,GAAIA,EAAM,OAAS,iBACjBH,EAAO,QAAUG,EAAM,SAAW,WACzBA,EAAM,OAAS,OAAQ,CAChC,IAAMC,GAAShC,GAAU,IAAI+B,EAAM,EAAE,GAAK,GAAK,EAC/C/B,GAAU,IAAI+B,EAAM,GAAIC,CAAK,EAC7BD,EAAM,GAAKC,EAAQ,IAAMD,EAAM,GAC/BhC,GAAS,KAAKgC,CAAK,CACrB,KAAO,CACAA,EAAM,QACLA,EAAM,OAAS,YACjBA,EAAM,MAAQ,GAGdA,EAAM,MAAQA,EAAM,IAGxB,IAAME,EAAgBnC,EAAc,IAAIiC,EAAM,EAAE,EAiBhD,GAfIE,IAAkB,OACpBnC,EAAc,IAAIiC,EAAM,GAAIA,CAAK,GAG7BA,EAAM,OAAS,OACjBE,EAAc,KAAOF,EAAM,MAEzBA,EAAM,QAAUA,EAAM,KACxBE,EAAc,MAAQF,EAAM,QAI5BA,EAAM,UACRL,GAAsBK,EAAM,SAAUA,CAAK,EAEzCA,EAAM,OAAS,QAAS,CAE1B,IAAMG,EAAIH,EAAM,OAAS,EACzB,QAASI,EAAI,EAAGA,EAAID,EAAGC,IAAK,CAC1B,IAAMC,EAAWC,GAAMN,CAAK,EAC5BK,EAAS,GAAKA,EAAS,GAAK,IAAMD,EAClCrC,EAAc,IAAIsC,EAAS,GAAIA,CAAQ,EACvCN,EAAS,KAAKM,CAAQ,CACxB,CACF,MAAWH,IAAkB,QAC3BH,EAAS,KAAKC,CAAK,CAEvB,CACF,CACAH,EAAO,SAAWE,CACpB,EArE8B,yBAuE1BQ,GAAkB,CAAC,EACnBC,GAAY,CAAE,GAAI,OAAQ,KAAM,YAAa,SAAU,CAAC,EAAG,QAAS,EAAG,EAErEC,GAAQ/B,EAAA,IAAY,CACxBgC,EAAI,MAAM,cAAc,EACxBD,GAAY,EACZD,GAAY,CAAE,GAAI,OAAQ,KAAM,YAAa,SAAU,CAAC,EAAG,QAAS,EAAG,EACvEzC,EAAgB,IAAI,IAAI,CAAC,CAAC,OAAQyC,EAAS,CAAC,CAAC,EAC7CD,GAAS,CAAC,EACV/B,GAAU,IAAI,IAEdR,GAAW,CAAC,EACZC,GAAY,IAAI,GAClB,EAVc,SAYP,SAAS0C,GAAaC,EAAiB,CAE5C,OADAF,EAAI,MAAM,eAAgBE,CAAO,EACzBA,EAAS,CACf,IAAK,KACH,MAAO,SACT,IAAK,KACH,OAAAF,EAAI,MAAM,iBAAiB,EACpB,QACT,IAAK,OACH,MAAO,SACT,IAAK,KACH,MAAO,sBACT,IAAK,KACH,MAAO,UACT,IAAK,OACH,MAAO,UACT,IAAK,OACH,MAAO,UACT,IAAK,OACH,MAAO,aACT,IAAK,OACH,MAAO,WACT,IAAK,SACH,MAAO,eACT,IAAK,OACH,MAAO,aACT,IAAK,SACH,MAAO,YACT,IAAK,QACH,MAAO,YACT,IAAK,QACH,MAAO,gBACT,IAAK,OACH,MAAO,cACT,QACE,MAAO,IACX,CACF,CArCgBhC,EAAAiC,GAAA,gBAuCT,SAASE,GAAiBD,EAAyB,CAExD,OADAF,EAAI,MAAM,eAAgBE,CAAO,EACzBA,EAAS,CACf,IAAK,KACH,MAAO,QACT,QACE,MAAO,QACX,CACF,CARgBlC,EAAAmC,GAAA,oBAUT,SAASC,GAAkBF,EAAyB,CACzD,OAAQA,EAAQ,KAAK,EAAG,CACtB,IAAK,MACH,MAAO,cACT,IAAK,MACH,MAAO,eACT,QACE,MAAO,aACX,CACF,CATgBlC,EAAAoC,GAAA,qBAWhB,IAAIC,GAAM,EACGC,GAAatC,EAAA,KACxBqC,KACO,MAAQ,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,EAAE,EAAI,IAAMA,IAFxC,cAKpBE,GAAevC,EAACsB,GAAyB,CAC7CQ,GAAU,SAAWR,EACrBL,GAAsBK,EAAOQ,EAAS,EACtCD,GAASC,GAAU,QACrB,EAJqB,gBAMfU,GAAaxC,EAACyC,GAA4B,CAC9C,IAAMnB,EAAQjC,EAAc,IAAIoD,CAAO,EACvC,OAAKnB,EAGDA,EAAM,QACDA,EAAM,QAEVA,EAAM,SAGJA,EAAM,SAAS,OAFb,GANA,EASX,EAZmB,cAkBboB,GAAgB1C,EAAA,IACb,CAAC,GAAGX,EAAc,OAAO,CAAC,EADb,iBAOhBsD,GAAY3C,EAAA,IACT6B,IAAU,CAAC,EADF,aAIZe,GAAW5C,EAAA,IACRV,GADQ,YAGXuD,GAAW7C,EAACI,GACTf,EAAc,IAAIe,CAAE,EADZ,YAIX0C,GAAW9C,EAACsB,GAAiB,CACjCjC,EAAc,IAAIiC,EAAM,GAAIA,CAAK,CACnC,EAFiB,YAIXyB,GAAY/C,EAAA,IAAMgC,EAAN,aAKLgB,GAAahD,EAAA,UAAY,CACpC,OAAOF,EACT,EAF0B,cAIpBmD,GAAK,CACT,UAAWjD,EAAA,IAAgBH,EAAU,EAAE,MAA5B,aACX,aAAcoC,GACd,iBAAkBE,GAClB,kBAAAC,GACA,UAAAW,GACA,cAAAL,GACA,UAAAC,GACA,SAAAC,GACA,aAAAL,GACA,SAAAM,GACA,SAAAC,GACA,WAAAN,GACA,WAAAQ,GACA,MAAAjB,GACA,WAAAO,EACF,EAGOY,GAAQD,GCzSf,IAAME,GAAOC,EAAA,CAACC,EAAeC,IAAoB,CAE/C,IAAMC,EAAiBC,GAEjBC,EAAIF,EAAQF,EAAO,GAAG,EACtBK,EAAIH,EAAQF,EAAO,GAAG,EACtBM,EAAIJ,EAAQF,EAAO,GAAG,EAG5B,OAAcO,GAAKH,EAAGC,EAAGC,EAAGL,CAAO,CACrC,EAVa,QAYPO,GAAYT,EAACU,GACjB;AAAA,mBACiBA,EAAQ,UAAU;AAAA,aACxBA,EAAQ,eAAiBA,EAAQ,SAAS;AAAA;AAAA;AAAA,YAG3CA,EAAQ,UAAU;AAAA;AAAA;AAAA,aAGjBA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMnBA,EAAQ,eAAiBA,EAAQ,SAAS;AAAA,aACzCA,EAAQ,eAAiBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAQ3CA,EAAQ,OAAO;AAAA,cACbA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAqBpBA,EAAQ,cAAc;AAAA;AAAA;AAAA;AAAA,cAIpBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,cAKjBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,wBAKPA,EAAQ,mBAAmB;AAAA;AAAA;AAAA,0BAGzBA,EAAQ,mBAAmB;AAAA,cACvCA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAOjBX,GAAKW,EAAQ,oBAAqB,EAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,eAK/CX,GAAKW,EAAQ,QAAS,EAAG,CAAC;AAAA,YAC7BX,GAAKW,EAAQ,WAAY,EAAG,CAAC;AAAA,cAC3BX,GAAKW,EAAQ,cAAe,EAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMlCA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,aAIjBA,EAAQ,UAAU;AAAA;AAAA;AAAA,aAGlBA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQZA,EAAQ,UAAU;AAAA;AAAA,kBAEnBA,EAAQ,aAAa;AAAA,wBACfA,EAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS3BA,EAAQ,SAAS;AAAA;AAAA,IAEzBC,GAAc,CAAC;AAAA,EAjHD,aAoHXC,GAAQH,GC/If,IAAMI,GAAgBC,EAAA,CAACC,EAAMC,EAAaC,EAAMC,IAAO,CACrDF,EAAY,QAASG,GAAe,CAClCC,GAAQD,CAAU,EAAEJ,EAAME,EAAMC,CAAE,CACpC,CAAC,CACH,EAJsB,iBAMhBG,GAAYP,EAAA,CAACC,EAAME,EAAMC,IAAO,CACpCI,EAAI,MAAM,sBAAuBJ,CAAE,EACnCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,oBAAoB,EAEjCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,eAAe,EAC5C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,oBAAoB,CACnC,EA3BkB,aA6BZM,GAAcT,EAAA,CAACC,EAAME,EAAMC,IAAO,CACtCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,mBAAmB,EAChD,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,EAEvCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,CACzC,EA1BoB,eA2BdO,GAAcV,EAAA,CAACC,EAAME,EAAMC,IAAO,CACtCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,mBAAmB,EAChD,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,EAEvCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,CACzC,EA1BoB,eA2BdQ,GAAaX,EAAA,CAACC,EAAME,EAAMC,IAAO,CACrCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,kBAAkB,EAC/C,KAAK,QAAS,qBAAuBA,CAAI,EACzC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,yBAAyB,EAEtCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,gBAAgB,EAC7C,KAAK,QAAS,qBAAuBA,CAAI,EACzC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,2BAA2B,CAC1C,EA1BmB,cA2BbS,GAAWZ,EAAA,CAACC,EAAME,EAAMC,IAAO,CACnCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,gBAAgB,EAC7C,KAAK,QAAS,mBAAqBA,CAAI,EACvC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,SAAU,OAAO,EACtB,KAAK,OAAQ,aAAa,EAC1B,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EACZ,KAAK,IAAK,CAAC,EAEdF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,cAAc,EAC3C,KAAK,QAAS,mBAAqBA,CAAI,EACvC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,SAAU,OAAO,EACtB,KAAK,OAAQ,aAAa,EAC1B,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EACZ,KAAK,IAAK,CAAC,CAChB,EAlCiB,YAmCXU,GAAQb,EAAA,CAACC,EAAME,EAAMC,IAAO,CAChCH,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,WAAW,EACxC,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,uBAAuB,EACjC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,EAClCF,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,aAAa,EAC1C,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,GAAG,EAChB,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,wBAAwB,EAClC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,CACpC,EAjCc,SAkCRW,GAASd,EAAA,CAACC,EAAME,EAAMC,IAAO,CACjCH,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,YAAY,EACzC,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,KAAM,GAAG,EACd,KAAK,KAAM,GAAG,EACd,KAAK,IAAK,GAAG,EACb,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,EAElCF,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,cAAc,EAC3C,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,KAAM,GAAG,EACd,KAAK,KAAM,GAAG,EACd,KAAK,IAAK,GAAG,EACb,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,CACpC,EAtCe,UAuCTY,GAAQf,EAAA,CAACC,EAAME,EAAMC,IAAO,CAChCH,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,WAAW,EACxC,KAAK,QAAS,gBAAkBA,CAAI,EACpC,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,GAAG,EAChB,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,IAAK,2BAA2B,EACrC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,EAElCF,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,aAAa,EAC1C,KAAK,QAAS,gBAAkBA,CAAI,EACpC,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,GAAG,EAChB,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,IAAK,2BAA2B,EACrC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,CACpC,EApCc,SAqCRa,GAAOhB,EAAA,CAACC,EAAME,EAAMC,IAAO,CAC/BH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,UAAU,EACvC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,cAAe,aAAa,EACjC,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,2BAA2B,CAC1C,EAba,QAgBPG,GAAU,CACd,UAAAC,GACA,YAAAE,GACA,YAAAC,GACA,WAAAC,GACA,SAAAC,GACA,MAAAC,GACA,OAAAC,GACA,MAAAC,GACA,KAAAC,EACF,EACOC,GAAQlB,GChSf,IAAMmB,EAAUC,EAAU,GAAG,OAAO,SAAW,EAOxC,SAASC,GAAuBC,EAAiBC,EAAiC,CAGvF,GAAID,IAAY,GAAK,CAAC,OAAO,UAAUA,CAAO,EAC5C,MAAM,IAAI,MAAM,mCAAmC,EAIrD,GAAIC,EAAW,GAAK,CAAC,OAAO,UAAUA,CAAQ,EAC5C,MAAM,IAAI,MAAM,2CAA6CA,CAAQ,EAGvE,GAAID,EAAU,EAEZ,MAAO,CAAE,GAAIC,EAAU,GAAI,CAAE,EAE/B,GAAID,IAAY,EAEd,MAAO,CAAE,GAAI,EAAG,GAAIC,CAAS,EAG/B,IAAMC,EAAKD,EAAWD,EAChBG,EAAK,KAAK,MAAMF,EAAWD,CAAO,EAExC,MAAO,CAAE,GAAAE,EAAI,GAAAC,CAAG,CAClB,CAzBgBC,EAAAL,GAAA,0BA2BhB,IAAMM,GAAkBD,EAACE,GAAiB,CACxC,IAAIC,EAAW,EACXC,EAAY,EAGhB,QAAWC,KAASH,EAAM,SAAU,CAClC,GAAM,CAAE,MAAAI,EAAO,OAAAC,EAAQ,EAAAC,EAAG,EAAAC,CAAE,EAAIJ,EAAM,MAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,EAAG,EAAG,CAAE,EAChFK,EAAI,MACF,+BACAL,EAAM,GACN,SACAC,EACA,UACAC,EACA,KACAC,EACA,KACAC,EACAJ,EAAM,IACR,EACIA,EAAM,OAAS,UAGfC,EAAQH,IACVA,EAAWG,GAASJ,EAAM,gBAAkB,IAE1CK,EAASH,IACXA,EAAYG,GAEhB,CACA,MAAO,CAAE,MAAOJ,EAAU,OAAQC,CAAU,CAC9C,EA/BwB,mBAiCxB,SAASO,GAAcT,EAAcU,EAAaC,EAAe,EAAGC,EAAgB,EAAG,CACrFJ,EAAI,MACF,8BACAR,EAAM,GACNA,GAAO,MAAM,EACb,gBACAA,GAAO,KACP,eACAW,CACF,EACKX,GAAO,MAAM,QAChBA,EAAM,KAAO,CACX,MAAOW,EACP,OAAQC,EACR,EAAG,EACH,EAAG,CACL,GAEF,IAAIX,EAAW,EACXC,EAAY,EAEhB,GAAIF,EAAM,UAAU,OAAS,EAAG,CAC9B,QAAWG,KAASH,EAAM,SACxBS,GAAcN,EAAOO,CAAE,EAGzB,IAAMG,EAAYd,GAAgBC,CAAK,EACvCC,EAAWY,EAAU,MACrBX,EAAYW,EAAU,OACtBL,EAAI,MAAM,kCAAmCR,EAAM,GAAI,kBAAmBC,EAAUC,CAAS,EAG7F,QAAWC,KAASH,EAAM,SACpBG,EAAM,OACRK,EAAI,MACF,qCAAqCR,EAAM,EAAE,OAAOG,EAAM,EAAE,IAAIF,CAAQ,IAAIC,CAAS,IAAI,KAAK,UAAUC,EAAM,IAAI,CAAC,EACrH,EACAA,EAAM,KAAK,MACTF,GAAYE,EAAM,gBAAkB,GAAKZ,IAAYY,EAAM,gBAAkB,GAAK,GACpFA,EAAM,KAAK,OAASD,EACpBC,EAAM,KAAK,EAAI,EACfA,EAAM,KAAK,EAAI,EAEfK,EAAI,MACF,0BAA0BR,EAAM,EAAE,mBAAmBG,EAAM,EAAE,aAAaF,CAAQ,cAAcC,CAAS,EAC3G,GAGJ,QAAWC,KAASH,EAAM,SACxBS,GAAcN,EAAOO,EAAIT,EAAUC,CAAS,EAG9C,IAAMR,EAAUM,EAAM,SAAW,GAC7Bc,EAAW,EACf,QAAWX,KAASH,EAAM,SACxBc,GAAYX,EAAM,gBAAkB,EAItC,IAAIY,EAAQf,EAAM,SAAS,OACvBN,EAAU,GAAKA,EAAUoB,IAC3BC,EAAQrB,GAGV,IAAMsB,EAAQ,KAAK,KAAKF,EAAWC,CAAK,EAEpCX,EAAQW,GAASd,EAAWV,GAAWA,EACvCc,EAASW,GAASd,EAAYX,GAAWA,EAE7C,GAAIa,EAAQO,EAAc,CACxBH,EAAI,MACF,oCAAoCR,EAAM,EAAE,iBAAiBW,CAAY,kBAAkBC,CAAa,UAAUR,CAAK,EACzH,EACAA,EAAQO,EACRN,EAASO,EACT,IAAMK,GAAcN,EAAeI,EAAQxB,EAAUA,GAAWwB,EAC1DG,GAAeN,EAAgBI,EAAQzB,EAAUA,GAAWyB,EAElER,EAAI,MAAM,oBAAqBR,EAAM,GAAI,aAAciB,EAAY,WAAYhB,CAAQ,EACvFO,EAAI,MAAM,oBAAqBR,EAAM,GAAI,cAAekB,EAAa,YAAahB,CAAS,EAC3FM,EAAI,MAAM,0BAA2BO,EAAO,UAAWxB,CAAO,EAG9D,QAAWY,KAASH,EAAM,SACpBG,EAAM,OACRA,EAAM,KAAK,MAAQc,EACnBd,EAAM,KAAK,OAASe,EACpBf,EAAM,KAAK,EAAI,EACfA,EAAM,KAAK,EAAI,EAGrB,CAOA,GALAK,EAAI,MACF,uBAAuBR,EAAM,EAAE,UAAUe,CAAK,UAAUC,CAAK,YAAYtB,CAAO,GAC9EM,EAAM,SAAS,MACjB,UAAU,KAAK,IAAII,EAAOJ,EAAM,MAAM,OAAS,CAAC,CAAC,EACnD,EACII,GAASJ,GAAO,MAAM,OAAS,GAAI,CACrCI,EAAQJ,GAAO,MAAM,OAAS,EAG9B,IAAMmB,EAAMzB,EAAU,EAAI,KAAK,IAAIM,EAAM,SAAS,OAAQN,CAAO,EAAIM,EAAM,SAAS,OACpF,GAAImB,EAAM,EAAG,CACX,IAAMF,GAAcb,EAAQe,EAAM5B,EAAUA,GAAW4B,EACvDX,EAAI,MAAM,+BAAgCR,EAAM,GAAII,EAAOJ,EAAM,MAAM,MAAOiB,CAAU,EACxF,QAAWd,KAASH,EAAM,SACpBG,EAAM,OACRA,EAAM,KAAK,MAAQc,EAGzB,CACF,CACAjB,EAAM,KAAO,CACX,MAAAI,EACA,OAAAC,EACA,EAAG,EACH,EAAG,CACL,CACF,CAEAG,EAAI,MACF,6BACAR,EAAM,GACNA,GAAO,MAAM,EACbA,GAAO,MAAM,MACbA,GAAO,MAAM,EACbA,GAAO,MAAM,MACf,CACF,CAjISF,EAAAW,GAAA,iBAmIT,SAASW,GAAapB,EAAcU,EAAa,CAC/CF,EAAI,MACF,wCAAwCR,EAAM,EAAE,OAAOA,GAAO,MAAM,CAAC,OAAOA,GAAO,MAAM,CAAC,WAAWA,GAAO,MAAM,KAAK,EACzH,EACA,IAAMN,EAAUM,EAAM,SAAW,GAEjC,GADAQ,EAAI,MAAM,6BAA8BR,EAAM,GAAI,KAAMN,EAASM,CAAK,EAEpEA,EAAM,UACNA,EAAM,SAAS,OAAS,EACxB,CACA,IAAMI,EAAQJ,GAAO,SAAS,CAAC,GAAG,MAAM,OAAS,EAC3CqB,EAAkBrB,EAAM,SAAS,OAASI,GAASJ,EAAM,SAAS,OAAS,GAAKT,EAEtFiB,EAAI,MAAM,qBAAsBa,EAAiB,MAAM,EAGvD,IAAIC,EAAY,EAChBd,EAAI,MAAM,uBAAwBR,EAAM,GAAIA,GAAO,MAAM,CAAC,EAC1D,IAAIuB,EAAevB,GAAO,MAAM,EAAIA,GAAO,MAAM,GAAK,CAACA,GAAO,MAAM,MAAQ,GAAK,GAAK,CAACT,EACnFiC,EAAS,EACb,QAAWrB,KAASH,EAAM,SAAU,CAClC,IAAMyB,EAASzB,EAEf,GAAI,CAACG,EAAM,KACT,SAEF,GAAM,CAAE,MAAAC,EAAO,OAAAC,CAAO,EAAIF,EAAM,KAC1B,CAAE,GAAAP,EAAI,GAAAC,CAAG,EAAIJ,GAAuBC,EAAS4B,CAAS,EAS5D,GARIzB,GAAM2B,IACRA,EAAS3B,EACT0B,EAAevB,GAAO,MAAM,EAAIA,GAAO,MAAM,GAAK,CAACA,GAAO,MAAM,MAAQ,GAAK,GAAK,CAACT,EACnFiB,EAAI,MAAM,8BAA+BR,EAAM,GAAI,cAAeG,EAAM,GAAIqB,CAAM,GAEpFhB,EAAI,MACF,mCAAmCL,EAAM,EAAE,SAASmB,CAAS,aAAa1B,CAAE,IAAIC,CAAE,KAAK4B,GAAQ,MAAM,CAAC,IAAIA,GAAQ,MAAM,CAAC,aAAaA,EAAO,EAAE,WAAWrB,CAAK,GAAGb,CAAO,EAC3K,EACIkC,EAAO,KAAM,CACf,IAAMC,EAAYtB,EAAQ,EAC1BD,EAAM,KAAK,EAAIoB,EAAehC,EAAUmC,EAGxClB,EAAI,MACF,uCACEL,EAAM,EACR,iBAAiBoB,CAAY,oBAC3BpB,EAAM,KAAK,CACb,IAAIuB,CAAS,YAAYnC,CAAO,UAAUa,CAAK,cAAcsB,CAAS,SACpEvB,EAAM,KAAK,CACb,MAAMA,EAAM,KAAK,CAAC,IAAIA,EAAM,cAAc,kCACvCC,GAASD,GAAO,gBAAkB,GAAM,CAC3C,EACF,EAEAoB,EAAepB,EAAM,KAAK,EAAIuB,EAE9BvB,EAAM,KAAK,EACTsB,EAAO,KAAK,EAAIA,EAAO,KAAK,OAAS,EAAI5B,GAAMQ,EAASd,GAAWc,EAAS,EAAId,EAElFiB,EAAI,MACF,uCACEL,EAAM,EACR,eAAeoB,CAAY,GAAGhC,CAAO,GAAGmC,CAAS,OAAOvB,EAAM,KAAK,CAAC,KAAKA,EAAM,KAAK,CAAC,GACnFA,EAAM,cACR,gCAAiCC,GAASD,GAAO,gBAAkB,GAAM,CAAC,EAC5E,CACF,CACIA,EAAM,UACRiB,GAAajB,EAAOO,CAAE,EAExBY,GAAanB,GAAO,gBAAkB,EACtCK,EAAI,MAAM,mBAAoBL,EAAOmB,CAAS,CAChD,CACF,CACAd,EAAI,MACF,mCAAmCR,EAAM,EAAE,OAAOA,GAAO,MAAM,CAAC,OAAOA,GAAO,MAAM,CAAC,WAAWA,GAAO,MAAM,KAAK,EACpH,CACF,CA5ESF,EAAAsB,GAAA,gBA8ET,SAASO,GACP3B,EACA,CAAE,KAAA4B,EAAM,KAAAC,EAAM,KAAAC,EAAM,KAAAC,CAAK,EAAI,CAAE,KAAM,EAAG,KAAM,EAAG,KAAM,EAAG,KAAM,CAAE,EAClE,CACA,GAAI/B,EAAM,MAAQA,EAAM,KAAO,OAAQ,CACrC,GAAM,CAAE,EAAAM,EAAG,EAAAC,EAAG,MAAAH,EAAO,OAAAC,CAAO,EAAIL,EAAM,KAClCM,EAAIF,EAAQ,EAAIwB,IAClBA,EAAOtB,EAAIF,EAAQ,GAEjBG,EAAIF,EAAS,EAAIwB,IACnBA,EAAOtB,EAAIF,EAAS,GAElBC,EAAIF,EAAQ,EAAI0B,IAClBA,EAAOxB,EAAIF,EAAQ,GAEjBG,EAAIF,EAAS,EAAI0B,IACnBA,EAAOxB,EAAIF,EAAS,EAExB,CACA,GAAIL,EAAM,SACR,QAAWG,KAASH,EAAM,UACvB,CAAE,KAAA4B,EAAM,KAAAC,EAAM,KAAAC,EAAM,KAAAC,CAAK,EAAIJ,GAAWxB,EAAO,CAAE,KAAAyB,EAAM,KAAAC,EAAM,KAAAC,EAAM,KAAAC,CAAK,CAAC,GAG9E,MAAO,CAAE,KAAAH,EAAM,KAAAC,EAAM,KAAAC,EAAM,KAAAC,CAAK,CAClC,CAzBSjC,EAAA6B,GAAA,cA2BF,SAASK,GAAOtB,EAAa,CAClC,IAAMuB,EAAOvB,EAAG,SAAS,MAAM,EAC/B,GAAI,CAACuB,EACH,OAGFxB,GAAcwB,EAAMvB,EAAI,EAAG,CAAC,EAC5BU,GAAaa,EAAMvB,CAAE,EAGrBF,EAAI,MAAM,YAAa,KAAK,UAAUyB,EAAM,KAAM,CAAC,CAAC,EAEpD,GAAM,CAAE,KAAAL,EAAM,KAAAC,EAAM,KAAAC,EAAM,KAAAC,CAAK,EAAIJ,GAAWM,CAAI,EAE5C5B,EAAS0B,EAAOF,EAChBzB,EAAQ0B,EAAOF,EACrB,MAAO,CAAE,EAAGA,EAAM,EAAGC,EAAM,MAAAzB,EAAO,OAAAC,CAAO,CAC3C,CAjBgBP,EAAAkC,GAAA,UCzShB,SAASE,GAAWC,EAAKC,EAAS,CAC5BA,GACFD,EAAI,KAAK,QAASC,CAAO,CAE7B,CAJSC,EAAAH,GAAA,cAUT,SAASI,GAAaC,EAAM,CAC1B,IAAMC,EAAKC,EAAO,SAAS,gBAAgB,6BAA8B,eAAe,CAAC,EACnFC,EAAMF,EAAG,OAAO,WAAW,EAE3BG,EAAQJ,EAAK,MACbK,EAAaL,EAAK,OAAS,YAAc,YACzCM,EAAOH,EAAI,OAAO,MAAM,EAC9B,OAAAG,EAAK,KAAKF,CAAK,EACfT,GAAWW,EAAMN,EAAK,UAAU,EAChCM,EAAK,KAAK,QAASD,CAAU,EAE7BV,GAAWQ,EAAKH,EAAK,UAAU,EAC/BG,EAAI,MAAM,UAAW,cAAc,EAEnCA,EAAI,MAAM,cAAe,QAAQ,EACjCA,EAAI,KAAK,QAAS,8BAA8B,EACzCF,EAAG,KAAK,CACjB,CAjBSH,EAAAC,GAAA,gBAyBT,IAAMQ,GAAcT,EAAA,MAAOU,EAAaC,EAAOC,EAASC,IAAW,CACjE,IAAIC,EAAaJ,GAAe,GAIhC,GAHI,OAAOI,GAAe,WACxBA,EAAaA,EAAW,CAAC,GAEvBC,EAASC,EAAU,EAAE,UAAU,UAAU,EAAG,CAE9CF,EAAaA,EAAW,QAAQ,UAAW,QAAQ,EACnDG,EAAI,MAAM,aAAeH,CAAU,EACnC,IAAMR,EAAQ,MAAMY,GAAqBC,GAAeL,CAAU,CAAC,EAC7DZ,EAAO,CACX,OAAAW,EACA,MAAAP,EACA,WAAYK,EAAM,QAAQ,QAAS,QAAQ,CAC7C,EAGA,OAFiBV,GAAaC,CAAI,CAGpC,KAAO,CACL,IAAMkB,EAAW,SAAS,gBAAgB,6BAA8B,MAAM,EAC9EA,EAAS,aAAa,QAAST,EAAM,QAAQ,SAAU,OAAO,CAAC,EAC/D,IAAIU,EAAO,CAAC,EACR,OAAOP,GAAe,SACxBO,EAAOP,EAAW,MAAM,qBAAqB,EACpC,MAAM,QAAQA,CAAU,EACjCO,EAAOP,EAEPO,EAAO,CAAC,EAGV,QAAWC,KAAOD,EAAM,CACtB,IAAME,EAAQ,SAAS,gBAAgB,6BAA8B,OAAO,EAC5EA,EAAM,eAAe,uCAAwC,YAAa,UAAU,EACpFA,EAAM,aAAa,KAAM,KAAK,EAC9BA,EAAM,aAAa,IAAK,GAAG,EACvBX,EACFW,EAAM,aAAa,QAAS,WAAW,EAEvCA,EAAM,aAAa,QAAS,KAAK,EAEnCA,EAAM,YAAcD,EAAI,KAAK,EAC7BF,EAAS,YAAYG,CAAK,CAC5B,CACA,OAAOH,CACT,CACF,EA7CoB,eA+CbI,EAAQf,GCjFR,IAAMgB,GAAiBC,EAAA,CAC5BC,EACAC,EACAC,EACAC,EACAC,IACG,CACCH,EAAK,gBACPI,GAAcL,EAAS,QAASC,EAAK,eAAgBC,EAAKC,EAAIC,CAAW,EAEvEH,EAAK,cACPI,GAAcL,EAAS,MAAOC,EAAK,aAAcC,EAAKC,EAAIC,CAAW,CAEzE,EAb8B,kBAexBE,GAAgB,CACpB,YAAa,QACb,YAAa,QACb,WAAY,OACZ,aAAc,SACd,YAAa,cACb,UAAW,YACX,YAAa,cACb,WAAY,aACZ,SAAU,UACZ,EAEMD,GAAgBN,EAAA,CACpBC,EACAO,EACAC,EACAN,EACAC,EACAC,IACG,CACH,IAAMK,EAAgBH,GAAcE,CAAuC,EAE3E,GAAI,CAACC,EAAe,CAClBC,EAAI,KAAK,uBAAuBF,CAAS,EAAE,EAC3C,MACF,CAEA,IAAMG,EAASJ,IAAa,QAAU,QAAU,MAChDP,EAAQ,KAAK,UAAUO,CAAQ,GAAI,OAAOL,CAAG,IAAIC,CAAE,IAAIC,CAAW,IAAIK,CAAa,GAAGE,CAAM,GAAG,CACjG,EAjBsB,iBC5BtB,IAAIC,GAAa,CAAC,EACdC,EAAiB,CAAC,EAOf,IAAMC,GAAkBC,EAAA,MAAOC,EAAMC,IAAS,CACnD,IAAMC,EAASC,EAAU,EACnBC,EAAgBC,EAASH,EAAO,UAAU,UAAU,EAEpDI,EACJL,EAAK,YAAc,WACfM,GACEP,EACAC,EAAK,MACL,CACE,MAAOA,EAAK,WACZ,cAAAG,EACA,iBAAkB,EACpB,EACAF,CACF,EACA,MAAMM,EAAYP,EAAK,MAAOA,EAAK,UAAU,EAG7CQ,EAAYT,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,WAAW,EAGtDU,EAAQD,EAAU,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EACzDC,EAAM,KAAK,EAAE,YAAYJ,CAAY,EAGrC,IAAIK,EAAOL,EAAa,QAAQ,EAChC,GAAIF,EAAe,CACjB,IAAMQ,EAAMN,EAAa,SAAS,CAAC,EAC7BO,EAAKC,EAAOR,CAAY,EAC9BK,EAAOC,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASF,EAAK,KAAK,EAC3BE,EAAG,KAAK,SAAUF,EAAK,MAAM,CAC/B,CACAD,EAAM,KAAK,YAAa,aAAe,CAACC,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAGtFI,GAAWd,EAAK,EAAE,EAAIQ,EAGtBR,EAAK,MAAQU,EAAK,MAClBV,EAAK,OAASU,EAAK,OAEnB,IAAIK,EACJ,GAAIf,EAAK,eAAgB,CAEvB,IAAMgB,EAAoB,MAAMT,EAAYP,EAAK,eAAgBA,EAAK,UAAU,EAC1EiB,EAAqBlB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EACnEmB,EAAQD,EAAmB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAClEF,EAAKG,EAAM,KAAK,EAAE,YAAYF,CAAiB,EAC/C,IAAMG,EAAQH,EAAkB,QAAQ,EACxCE,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EACnFC,EAAepB,EAAK,EAAE,IACzBoB,EAAepB,EAAK,EAAE,EAAI,CAAC,GAE7BoB,EAAepB,EAAK,EAAE,EAAE,UAAYiB,EACpCI,GAAiBN,EAAIf,EAAK,cAAc,CAC1C,CACA,GAAIA,EAAK,gBAAiB,CAExB,IAAMgB,EAAoB,MAAMT,EAAYP,EAAK,gBAAiBA,EAAK,UAAU,EAC3EsB,EAAsBvB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EACpEmB,EAAQI,EAAoB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EACnEP,EAAKO,EAAoB,KAAK,EAAE,YAAYN,CAAiB,EAC7DE,EAAM,KAAK,EAAE,YAAYF,CAAiB,EAC1C,IAAMG,EAAQH,EAAkB,QAAQ,EACxCE,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EAEnFC,EAAepB,EAAK,EAAE,IACzBoB,EAAepB,EAAK,EAAE,EAAI,CAAC,GAE7BoB,EAAepB,EAAK,EAAE,EAAE,WAAasB,EACrCD,GAAiBN,EAAIf,EAAK,eAAe,CAC3C,CACA,GAAIA,EAAK,aAAc,CAErB,IAAMuB,EAAkB,MAAMhB,EAAYP,EAAK,aAAcA,EAAK,UAAU,EACtEwB,EAAmBzB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EACjEmB,EAAQM,EAAiB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAChET,EAAKG,EAAM,KAAK,EAAE,YAAYK,CAAe,EAC7C,IAAMJ,EAAQI,EAAgB,QAAQ,EACtCL,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EAExFK,EAAiB,KAAK,EAAE,YAAYD,CAAe,EAE9CH,EAAepB,EAAK,EAAE,IACzBoB,EAAepB,EAAK,EAAE,EAAI,CAAC,GAE7BoB,EAAepB,EAAK,EAAE,EAAE,QAAUwB,EAClCH,GAAiBN,EAAIf,EAAK,YAAY,CACxC,CACA,GAAIA,EAAK,cAAe,CAEtB,IAAMuB,EAAkB,MAAMhB,EAAYP,EAAK,cAAeA,EAAK,UAAU,EACvEyB,EAAoB1B,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EAClEmB,EAAQO,EAAkB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAEjEV,EAAKG,EAAM,KAAK,EAAE,YAAYK,CAAe,EAC7C,IAAMJ,EAAQI,EAAgB,QAAQ,EACtCL,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EAExFM,EAAkB,KAAK,EAAE,YAAYF,CAAe,EAC/CH,EAAepB,EAAK,EAAE,IACzBoB,EAAepB,EAAK,EAAE,EAAI,CAAC,GAE7BoB,EAAepB,EAAK,EAAE,EAAE,SAAWyB,EACnCJ,GAAiBN,EAAIf,EAAK,aAAa,CACzC,CACA,OAAOK,CACT,EA7G+B,mBAmH/B,SAASgB,GAAiBN,EAAIW,EAAO,CAC/BxB,EAAU,EAAE,UAAU,YAAca,IACtCA,EAAG,MAAM,MAAQW,EAAM,OAAS,EAAI,KACpCX,EAAG,MAAM,OAAS,OAEtB,CALSjB,EAAAuB,GAAA,oBAOF,IAAMM,GAAoB7B,EAAA,CAACE,EAAM4B,IAAU,CAChDC,EAAI,MAAM,sBAAuB7B,EAAK,GAAIA,EAAK,MAAOc,GAAWd,EAAK,EAAE,EAAG4B,CAAK,EAChF,IAAIE,EAAOF,EAAM,YAAcA,EAAM,YAAcA,EAAM,aACnDG,EAAa7B,EAAU,EACvB,CAAE,yBAAA8B,CAAyB,EAAIC,GAAwBF,CAAU,EACvE,GAAI/B,EAAK,MAAO,CACd,IAAMkC,EAAKpB,GAAWd,EAAK,EAAE,EACzBmC,EAAInC,EAAK,EACToC,EAAIpC,EAAK,EACb,GAAI8B,EAAM,CAER,IAAMO,EAAMC,GAAM,kBAAkBR,CAAI,EACxCD,EAAI,MACF,gBAAkB7B,EAAK,MAAQ,UAC/BmC,EACA,IACAC,EACA,SACAC,EAAI,EACJ,IACAA,EAAI,EACJ,SACF,EACIT,EAAM,cACRO,EAAIE,EAAI,EACRD,EAAIC,EAAI,EAEZ,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,EAAIJ,EAA2B,CAAC,GAAG,CAC7E,CAGA,GAAIhC,EAAK,eAAgB,CACvB,IAAMkC,EAAKd,EAAepB,EAAK,EAAE,EAAE,UAC/BmC,EAAInC,EAAK,EACToC,EAAIpC,EAAK,EACb,GAAI8B,EAAM,CAER,IAAMO,EAAMC,GAAM,0BAA0BtC,EAAK,eAAiB,GAAK,EAAG,aAAc8B,CAAI,EAC5FK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACA,GAAIpC,EAAK,gBAAiB,CACxB,IAAMkC,EAAKd,EAAepB,EAAK,EAAE,EAAE,WAC/BmC,EAAInC,EAAK,EACToC,EAAIpC,EAAK,EACb,GAAI8B,EAAM,CAER,IAAMO,EAAMC,GAAM,0BAChBtC,EAAK,eAAiB,GAAK,EAC3B,cACA8B,CACF,EACAK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACA,GAAIpC,EAAK,aAAc,CACrB,IAAMkC,EAAKd,EAAepB,EAAK,EAAE,EAAE,QAC/BmC,EAAInC,EAAK,EACToC,EAAIpC,EAAK,EACb,GAAI8B,EAAM,CAER,IAAMO,EAAMC,GAAM,0BAA0BtC,EAAK,aAAe,GAAK,EAAG,WAAY8B,CAAI,EACxFK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACA,GAAIpC,EAAK,cAAe,CACtB,IAAMkC,EAAKd,EAAepB,EAAK,EAAE,EAAE,SAC/BmC,EAAInC,EAAK,EACToC,EAAIpC,EAAK,EACb,GAAI8B,EAAM,CAER,IAAMO,EAAMC,GAAM,0BAA0BtC,EAAK,aAAe,GAAK,EAAG,YAAa8B,CAAI,EACzFK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACF,EApFiC,qBAsF3BG,GAAczC,EAAA,CAAC0C,EAAMC,IAAU,CACnC,IAAMN,EAAIK,EAAK,EACTJ,EAAII,EAAK,EACTE,EAAK,KAAK,IAAID,EAAM,EAAIN,CAAC,EACzBQ,EAAK,KAAK,IAAIF,EAAM,EAAIL,CAAC,EACzBQ,EAAIJ,EAAK,MAAQ,EACjBK,EAAIL,EAAK,OAAS,EACxB,OAAIE,GAAME,GAAKD,GAAME,CAIvB,EAXoB,eAaPC,GAAehD,EAAA,CAAC0C,EAAMO,EAAcC,IAAgB,CAC/DnB,EAAI,MAAM;AAAA,kBACM,KAAK,UAAUkB,CAAY,CAAC;AAAA,kBAC5B,KAAK,UAAUC,CAAW,CAAC;AAAA,oBACzBR,EAAK,CAAC,MAAMA,EAAK,CAAC,MAAMA,EAAK,KAAK,MAAMA,EAAK,MAAM,EAAE,EACvE,IAAML,EAAIK,EAAK,EACTJ,EAAII,EAAK,EAETE,EAAK,KAAK,IAAIP,EAAIa,EAAY,CAAC,EAE/BJ,EAAIJ,EAAK,MAAQ,EACnBS,EAAID,EAAY,EAAID,EAAa,EAAIH,EAAIF,EAAKE,EAAIF,EAChDG,EAAIL,EAAK,OAAS,EAElBU,EAAI,KAAK,IAAIH,EAAa,EAAIC,EAAY,CAAC,EAC3CG,EAAI,KAAK,IAAIJ,EAAa,EAAIC,EAAY,CAAC,EAEjD,GAAI,KAAK,IAAIZ,EAAIW,EAAa,CAAC,EAAIH,EAAI,KAAK,IAAIT,EAAIY,EAAa,CAAC,EAAIF,EAAG,CAEvE,IAAIO,EAAIJ,EAAY,EAAID,EAAa,EAAIA,EAAa,EAAIF,EAAIT,EAAIA,EAAIS,EAAIE,EAAa,EACvFE,EAAKE,EAAIC,EAAKF,EACd,IAAMG,EAAM,CACV,EAAGL,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAIC,EAAID,EAAY,EAAIG,EAAIF,EAC5E,EAAGD,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAIE,EAAIE,EAAIJ,EAAY,EAAIE,EAAIE,CAClF,EAEA,OAAIH,IAAM,IACRI,EAAI,EAAIN,EAAa,EACrBM,EAAI,EAAIN,EAAa,GAEnBI,IAAM,IACRE,EAAI,EAAIN,EAAa,GAEnBG,IAAM,IACRG,EAAI,EAAIN,EAAa,GAGvBlB,EAAI,MAAM,2BAA2BqB,CAAC,OAAOE,CAAC,OAAOD,CAAC,OAAOF,CAAC,GAAII,CAAG,EAE9DA,CACT,KAAO,CAEDL,EAAY,EAAID,EAAa,EAC/BE,EAAIF,EAAa,EAAIH,EAAIT,EAGzBc,EAAId,EAAIS,EAAIG,EAAa,EAE3B,IAAIK,EAAKF,EAAID,EAAKE,EAGdG,EAAKN,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAIG,EAAIF,EAAID,EAAY,EAAIG,EAAIF,EAElFM,EAAKP,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAII,EAAIJ,EAAY,EAAII,EAC9E,OAAAvB,EAAI,MAAM,uBAAuBqB,CAAC,OAAOE,CAAC,OAAOD,CAAC,OAAOF,CAAC,GAAI,CAAE,GAAAK,EAAI,GAAAC,CAAG,CAAC,EACpEN,IAAM,IACRK,EAAKP,EAAa,EAClBQ,EAAKR,EAAa,GAEhBI,IAAM,IACRG,EAAKP,EAAa,GAEhBG,IAAM,IACRK,EAAKR,EAAa,GAGb,CAAE,EAAGO,EAAI,EAAGC,CAAG,CACxB,CACF,EApE4B,gBA6EtBC,GAAqB1D,EAAA,CAAC2D,EAASC,IAAiB,CACpD7B,EAAI,MAAM,2BAA4B4B,EAASC,CAAY,EAC3D,IAAIC,EAAS,CAAC,EACVC,EAAmBH,EAAQ,CAAC,EAC5BI,EAAW,GACf,OAAAJ,EAAQ,QAAShB,GAAU,CAEzB,GAAI,CAACF,GAAYmB,EAAcjB,CAAK,GAAK,CAACoB,EAAU,CAGlD,IAAMC,EAAQhB,GAAaY,EAAcE,EAAkBnB,CAAK,EAG5DsB,EAAe,GACnBJ,EAAO,QAASK,GAAM,CACpBD,EAAeA,GAAiBC,EAAE,IAAMF,EAAM,GAAKE,EAAE,IAAMF,EAAM,CACnE,CAAC,EAEIH,EAAO,KAAMM,GAAMA,EAAE,IAAMH,EAAM,GAAKG,EAAE,IAAMH,EAAM,CAAC,GACxDH,EAAO,KAAKG,CAAK,EAGnBD,EAAW,EACb,MAEED,EAAmBnB,EAEdoB,GACHF,EAAO,KAAKlB,CAAK,CAGvB,CAAC,EACMkB,CACT,EAjC2B,sBAmCdO,GAAapE,EAAA,SAAUC,EAAMkE,EAAGjE,EAAMmE,EAAWC,EAAaC,EAAOC,EAAI,CACpF,IAAIX,EAAS3D,EAAK,OAClB6B,EAAI,MAAM,0BAA2B7B,EAAM,KAAMiE,CAAC,EAClD,IAAIM,EAAmB,GACjBC,EAAOH,EAAM,KAAKJ,EAAE,CAAC,EAC3B,IAAIQ,EAAOJ,EAAM,KAAKJ,EAAE,CAAC,EAErBQ,GAAM,WAAaD,GAAM,YAC3Bb,EAASA,EAAO,MAAM,EAAG3D,EAAK,OAAO,OAAS,CAAC,EAC/C2D,EAAO,QAAQa,EAAK,UAAUb,EAAO,CAAC,CAAC,CAAC,EACxCA,EAAO,KAAKc,EAAK,UAAUd,EAAOA,EAAO,OAAS,CAAC,CAAC,CAAC,GAGnD3D,EAAK,YACP6B,EAAI,MAAM,mBAAoBsC,EAAUnE,EAAK,SAAS,CAAC,EACvD2D,EAASH,GAAmBxD,EAAK,OAAQmE,EAAUnE,EAAK,SAAS,EAAE,IAAI,EAEvEuE,EAAmB,IAGjBvE,EAAK,cACP6B,EAAI,MAAM,qBAAsBsC,EAAUnE,EAAK,WAAW,CAAC,EAC3D2D,EAASH,GAAmBG,EAAO,QAAQ,EAAGQ,EAAUnE,EAAK,WAAW,EAAE,IAAI,EAAE,QAAQ,EAExFuE,EAAmB,IAIrB,IAAMG,EAAWf,EAAO,OAAQK,GAAM,CAAC,OAAO,MAAMA,EAAE,CAAC,CAAC,EAGpDW,EAAQC,GAIR5E,EAAK,QAAUoE,IAAgB,SAAWA,IAAgB,eAC5DO,EAAQ3E,EAAK,OAGf,GAAM,CAAE,EAAAmC,EAAG,EAAAC,CAAE,EAAIyC,GAA2B7E,CAAI,EAC1C8E,EAAeC,GAAK,EAAE,EAAE5C,CAAC,EAAE,EAAEC,CAAC,EAAE,MAAMuC,CAAK,EAG7CK,EACJ,OAAQhF,EAAK,UAAW,CACtB,IAAK,SACHgF,EAAgB,wBAChB,MACF,IAAK,QACHA,EAAgB,uBAChB,MACF,IAAK,YACHA,EAAgB,uBAChB,MACF,QACEA,EAAgB,EACpB,CACA,OAAQhF,EAAK,QAAS,CACpB,IAAK,QACHgF,GAAiB,sBACjB,MACF,IAAK,SACHA,GAAiB,uBACjB,MACF,IAAK,SACHA,GAAiB,uBACjB,KACJ,CAEA,IAAMC,EAAUlF,EACb,OAAO,MAAM,EACb,KAAK,IAAK+E,EAAaJ,CAAQ,CAAC,EAChC,KAAK,KAAM1E,EAAK,EAAE,EAClB,KAAK,QAAS,IAAMgF,GAAiBhF,EAAK,QAAU,IAAMA,EAAK,QAAU,GAAG,EAC5E,KAAK,QAASA,EAAK,KAAK,EAavBkF,EAAM,IAENhF,EAAU,EAAE,UAAU,qBAAuBA,EAAU,EAAE,MAAM,uBACjEgF,EAAMC,GAAO,EAAI,GAGnBC,GAAeH,EAASjF,EAAMkF,EAAKZ,EAAIF,CAAW,EAElD,IAAIxC,EAAQ,CAAC,EACb,OAAI2C,IACF3C,EAAM,YAAc+B,GAEtB/B,EAAM,aAAe5B,EAAK,OACnB4B,CACT,EArG0B,cC9V1B,IAAMyD,GAAiCC,EAACC,GAA4B,CAClE,IAAMC,EAAmB,IAAI,IAE7B,QAAWC,KAAaF,EACtB,OAAQE,EAAW,CACjB,IAAK,IACHD,EAAiB,IAAI,OAAO,EAC5BA,EAAiB,IAAI,MAAM,EAC3B,MACF,IAAK,IACHA,EAAiB,IAAI,IAAI,EACzBA,EAAiB,IAAI,MAAM,EAC3B,MACF,QACEA,EAAiB,IAAIC,CAAS,EAC9B,KACJ,CAGF,OAAOD,CACT,EApBuC,kCAqB1BE,GAAiBJ,EAAA,CAC5BK,EACAC,EACAC,IACG,CAGH,IAAMN,EAAaF,GAA+BM,CAAoB,EAGhEG,EAAI,EAGJC,EAASH,EAAK,OAAS,EAAIC,EAAK,QAEhCG,EAAWD,EAASD,EAEpBG,EAAQL,EAAK,MAAQ,EAAII,EAAWH,EAAK,QAEzCK,EAAUL,EAAK,QAAU,EAE/B,OACEN,EAAW,IAAI,OAAO,GACtBA,EAAW,IAAI,MAAM,GACrBA,EAAW,IAAI,IAAI,GACnBA,EAAW,IAAI,MAAM,EAGd,CAEL,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGS,EAAU,EAAG,CAAE,EACpB,CAAE,EAAGC,EAAQ,EAAG,EAAG,EAAIC,CAAQ,EAC/B,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAAE,EAC5B,CAAE,EAAGC,EAAO,EAAG,CAAE,EAGjB,CAAE,EAAGA,EAAO,EAAG,CAACF,EAAS,CAAE,EAC3B,CAAE,EAAGE,EAAQ,EAAIC,EAAS,EAAG,CAACH,EAAS,CAAE,EACzC,CAAE,EAAGE,EAAO,EAAI,GAAKF,EAAU,CAAE,EACjC,CAAE,EAAGE,EAAO,EAAG,CAACF,CAAO,EAGvB,CAAE,EAAGE,EAAQD,EAAU,EAAG,CAACD,CAAO,EAClC,CAAE,EAAGE,EAAQ,EAAG,EAAG,CAACF,EAAS,EAAIG,CAAQ,EACzC,CAAE,EAAGF,EAAU,EAAG,CAACD,CAAO,EAG1B,CAAE,EAAG,EAAG,EAAG,CAACA,CAAO,EACnB,CAAE,EAAG,EAAG,EAAI,GAAKA,EAAU,CAAE,EAC7B,CAAE,EAAG,GAAKG,EAAS,EAAG,CAACH,EAAS,CAAE,EAClC,CAAE,EAAG,EAAG,EAAG,CAACA,EAAS,CAAE,CACzB,EAEER,EAAW,IAAI,OAAO,GAAKA,EAAW,IAAI,MAAM,GAAKA,EAAW,IAAI,IAAI,EAEnE,CACL,CAAE,EAAGS,EAAU,EAAG,CAAE,EACpB,CAAE,EAAGC,EAAQD,EAAU,EAAG,CAAE,EAC5B,CAAE,EAAGC,EAAO,EAAG,CAACF,EAAS,CAAE,EAC3B,CAAE,EAAGE,EAAQD,EAAU,EAAG,CAACD,CAAO,EAClC,CAAE,EAAGC,EAAU,EAAG,CAACD,CAAO,EAC1B,CAAE,EAAG,EAAG,EAAG,CAACA,EAAS,CAAE,CACzB,EAEER,EAAW,IAAI,OAAO,GAAKA,EAAW,IAAI,MAAM,GAAKA,EAAW,IAAI,MAAM,EAErE,CACL,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGS,EAAU,EAAG,CAACD,CAAO,EAC1B,CAAE,EAAGE,EAAQD,EAAU,EAAG,CAACD,CAAO,EAClC,CAAE,EAAGE,EAAO,EAAG,CAAE,CACnB,EAEEV,EAAW,IAAI,OAAO,GAAKA,EAAW,IAAI,IAAI,GAAKA,EAAW,IAAI,MAAM,EAEnE,CACL,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGU,EAAO,EAAG,CAACD,CAAS,EACzB,CAAE,EAAGC,EAAO,EAAG,CAACF,EAASC,CAAS,EAClC,CAAE,EAAG,EAAG,EAAG,CAACD,CAAO,CACrB,EAEER,EAAW,IAAI,MAAM,GAAKA,EAAW,IAAI,IAAI,GAAKA,EAAW,IAAI,MAAM,EAElE,CACL,CAAE,EAAGU,EAAO,EAAG,CAAE,EACjB,CAAE,EAAG,EAAG,EAAG,CAACD,CAAS,EACrB,CAAE,EAAG,EAAG,EAAG,CAACD,EAASC,CAAS,EAC9B,CAAE,EAAGC,EAAO,EAAG,CAACF,CAAO,CACzB,EAEER,EAAW,IAAI,OAAO,GAAKA,EAAW,IAAI,MAAM,EAE3C,CACL,CAAE,EAAGS,EAAU,EAAG,CAAE,EACpB,CAAE,EAAGA,EAAU,EAAG,CAACE,CAAQ,EAC3B,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACE,CAAQ,EACnC,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAAE,EAC5B,CAAE,EAAGC,EAAO,EAAG,CAACF,EAAS,CAAE,EAC3B,CAAE,EAAGE,EAAQD,EAAU,EAAG,CAACD,CAAO,EAClC,CAAE,EAAGE,EAAQD,EAAU,EAAG,CAACD,EAASG,CAAQ,EAC5C,CAAE,EAAGF,EAAU,EAAG,CAACD,EAASG,CAAQ,EACpC,CAAE,EAAGF,EAAU,EAAG,CAACD,CAAO,EAC1B,CAAE,EAAG,EAAG,EAAG,CAACA,EAAS,CAAE,CACzB,EAEER,EAAW,IAAI,IAAI,GAAKA,EAAW,IAAI,MAAM,EAExC,CAEL,CAAE,EAAGU,EAAQ,EAAG,EAAG,CAAE,EAErB,CAAE,EAAG,EAAG,EAAG,CAACC,CAAQ,EACpB,CAAE,EAAGF,EAAU,EAAG,CAACE,CAAQ,EAE3B,CAAE,EAAGF,EAAU,EAAG,CAACD,EAASG,CAAQ,EACpC,CAAE,EAAG,EAAG,EAAG,CAACH,EAASG,CAAQ,EAE7B,CAAE,EAAGD,EAAQ,EAAG,EAAG,CAACF,CAAO,EAC3B,CAAE,EAAGE,EAAO,EAAG,CAACF,EAASG,CAAQ,EAEjC,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACD,EAASG,CAAQ,EAC5C,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACE,CAAQ,EACnC,CAAE,EAAGD,EAAO,EAAG,CAACC,CAAQ,CAC1B,EAEEX,EAAW,IAAI,OAAO,GAAKA,EAAW,IAAI,IAAI,EAEzC,CACL,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGU,EAAO,EAAG,CAACD,CAAS,EACzB,CAAE,EAAG,EAAG,EAAG,CAACD,CAAO,CACrB,EAEER,EAAW,IAAI,OAAO,GAAKA,EAAW,IAAI,MAAM,EAE3C,CACL,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGU,EAAO,EAAG,CAAE,EACjB,CAAE,EAAG,EAAG,EAAG,CAACF,CAAO,CACrB,EAEER,EAAW,IAAI,MAAM,GAAKA,EAAW,IAAI,IAAI,EAExC,CACL,CAAE,EAAGU,EAAO,EAAG,CAAE,EACjB,CAAE,EAAG,EAAG,EAAG,CAACD,CAAS,EACrB,CAAE,EAAGC,EAAO,EAAG,CAACF,CAAO,CACzB,EAEER,EAAW,IAAI,MAAM,GAAKA,EAAW,IAAI,MAAM,EAE1C,CACL,CAAE,EAAGU,EAAO,EAAG,CAAE,EACjB,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAO,EAAG,CAACF,CAAO,CACzB,EAEER,EAAW,IAAI,OAAO,EAEjB,CACL,CAAE,EAAGS,EAAU,EAAG,CAACE,CAAQ,EAC3B,CAAE,EAAGF,EAAU,EAAG,CAACE,CAAQ,EAC3B,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACE,CAAQ,EACnC,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAAE,EAC5B,CAAE,EAAGC,EAAO,EAAG,CAACF,EAAS,CAAE,EAC3B,CAAE,EAAGE,EAAQD,EAAU,EAAG,CAACD,CAAO,EAClC,CAAE,EAAGE,EAAQD,EAAU,EAAG,CAACD,EAASG,CAAQ,EAE5C,CAAE,EAAGF,EAAU,EAAG,CAACD,EAASG,CAAQ,EACpC,CAAE,EAAGF,EAAU,EAAG,CAACD,EAASG,CAAQ,CACtC,EAEEX,EAAW,IAAI,MAAM,EAEhB,CACL,CAAE,EAAGS,EAAU,EAAG,CAAE,EACpB,CAAE,EAAGA,EAAU,EAAG,CAACE,CAAQ,EAE3B,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACE,CAAQ,EACnC,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACD,EAASG,CAAQ,EAC5C,CAAE,EAAGF,EAAU,EAAG,CAACD,EAASG,CAAQ,EACpC,CAAE,EAAGF,EAAU,EAAG,CAACD,CAAO,EAC1B,CAAE,EAAG,EAAG,EAAG,CAACA,EAAS,CAAE,CACzB,EAEER,EAAW,IAAI,IAAI,EAEd,CAEL,CAAE,EAAGS,EAAU,EAAG,CAACE,CAAQ,EAE3B,CAAE,EAAGF,EAAU,EAAG,CAACD,EAASG,CAAQ,EACpC,CAAE,EAAG,EAAG,EAAG,CAACH,EAASG,CAAQ,EAE7B,CAAE,EAAGD,EAAQ,EAAG,EAAG,CAACF,CAAO,EAC3B,CAAE,EAAGE,EAAO,EAAG,CAACF,EAASG,CAAQ,EAEjC,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACD,EAASG,CAAQ,EAC5C,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACE,CAAQ,CACrC,EAEEX,EAAW,IAAI,MAAM,EAEhB,CAEL,CAAE,EAAGU,EAAQ,EAAG,EAAG,CAAE,EAErB,CAAE,EAAG,EAAG,EAAG,CAACC,CAAQ,EACpB,CAAE,EAAGF,EAAU,EAAG,CAACE,CAAQ,EAE3B,CAAE,EAAGF,EAAU,EAAG,CAACD,EAASG,CAAQ,EACpC,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACD,EAASG,CAAQ,EAC5C,CAAE,EAAGD,EAAQD,EAAU,EAAG,CAACE,CAAQ,EACnC,CAAE,EAAGD,EAAO,EAAG,CAACC,CAAQ,CAC1B,EAIK,CAAC,CAAE,EAAG,EAAG,EAAG,CAAE,CAAC,CACxB,EA7N8B,kBCnB9B,SAASC,GAAcC,EAAMC,EAAO,CAElC,OAAOD,EAAK,UAAUC,CAAK,CAC7B,CAHSC,EAAAH,GAAA,iBAKT,IAAOI,GAAQJ,GCHf,SAASK,GAAiBC,EAAMC,EAAIC,EAAIC,EAAO,CAG7C,IAAIC,EAAKJ,EAAK,EACVK,EAAKL,EAAK,EAEVM,EAAKF,EAAKD,EAAM,EAChBI,EAAKF,EAAKF,EAAM,EAEhBK,EAAM,KAAK,KAAKP,EAAKA,EAAKM,EAAKA,EAAKL,EAAKA,EAAKI,EAAKA,CAAE,EAErDG,EAAK,KAAK,IAAKR,EAAKC,EAAKI,EAAME,CAAG,EAClCL,EAAM,EAAIC,IACZK,EAAK,CAACA,GAER,IAAIC,EAAK,KAAK,IAAKT,EAAKC,EAAKK,EAAMC,CAAG,EACtC,OAAIL,EAAM,EAAIE,IACZK,EAAK,CAACA,GAGD,CAAE,EAAGN,EAAKK,EAAI,EAAGJ,EAAKK,CAAG,CAClC,CArBSC,EAAAZ,GAAA,oBAuBT,IAAOa,GAAQb,GCtBf,SAASc,GAAgBC,EAAMC,EAAIC,EAAO,CACxC,OAAOC,GAAiBH,EAAMC,EAAIA,EAAIC,CAAK,CAC7C,CAFSE,EAAAL,GAAA,mBAIT,IAAOM,GAAQN,GCHf,SAASO,GAAcC,EAAIC,EAAIC,EAAIC,EAAI,CAIrC,IAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EACpBC,EAAIC,EAAIC,EAAIC,EACZC,EAAOC,EAAQC,EACfC,EAAGC,EAcP,GAVAd,EAAKH,EAAG,EAAID,EAAG,EACfM,EAAKN,EAAG,EAAIC,EAAG,EACfO,EAAKP,EAAG,EAAID,EAAG,EAAIA,EAAG,EAAIC,EAAG,EAG7BW,EAAKR,EAAKF,EAAG,EAAII,EAAKJ,EAAG,EAAIM,EAC7BK,EAAKT,EAAKD,EAAG,EAAIG,EAAKH,EAAG,EAAIK,EAIzB,EAAAI,IAAO,GAAKC,IAAO,GAAKM,GAASP,EAAIC,CAAE,KAK3CR,EAAKF,EAAG,EAAID,EAAG,EACfK,EAAKL,EAAG,EAAIC,EAAG,EACfM,EAAKN,EAAG,EAAID,EAAG,EAAIA,EAAG,EAAIC,EAAG,EAG7BO,EAAKL,EAAKL,EAAG,EAAIO,EAAKP,EAAG,EAAIS,EAC7BE,EAAKN,EAAKJ,EAAG,EAAIM,EAAKN,EAAG,EAAIQ,EAKzB,EAAAC,IAAO,GAAKC,IAAO,GAAKQ,GAAST,EAAIC,CAAE,KAK3CG,EAAQV,EAAKG,EAAKF,EAAKC,EACnBQ,IAAU,IAId,OAAAC,EAAS,KAAK,IAAID,EAAQ,CAAC,EAK3BE,EAAMV,EAAKG,EAAKF,EAAKC,EACrBS,EAAID,EAAM,GAAKA,EAAMD,GAAUD,GAASE,EAAMD,GAAUD,EAExDE,EAAMX,EAAKG,EAAKJ,EAAKK,EACrBS,EAAIF,EAAM,GAAKA,EAAMD,GAAUD,GAASE,EAAMD,GAAUD,EAEjD,CAAE,EAAGG,EAAG,EAAGC,CAAE,CACtB,CA3DSE,EAAArB,GAAA,iBAiET,SAASoB,GAAST,EAAIC,EAAI,CACxB,OAAOD,EAAKC,EAAK,CACnB,CAFSS,EAAAD,GAAA,YAIT,IAAOE,GAAQtB,GCzEf,IAAOuB,GAAQC,GAUf,SAASA,GAAiBC,EAAMC,EAAYC,EAAO,CACjD,IAAIC,EAAKH,EAAK,EACVI,EAAKJ,EAAK,EAEVK,EAAgB,CAAC,EAEjBC,EAAO,OAAO,kBACdC,EAAO,OAAO,kBACd,OAAON,EAAW,SAAY,WAChCA,EAAW,QAAQ,SAAUO,EAAO,CAClCF,EAAO,KAAK,IAAIA,EAAME,EAAM,CAAC,EAC7BD,EAAO,KAAK,IAAIA,EAAMC,EAAM,CAAC,CAC/B,CAAC,GAEDF,EAAO,KAAK,IAAIA,EAAML,EAAW,CAAC,EAClCM,EAAO,KAAK,IAAIA,EAAMN,EAAW,CAAC,GAMpC,QAHIQ,EAAON,EAAKH,EAAK,MAAQ,EAAIM,EAC7BI,EAAMN,EAAKJ,EAAK,OAAS,EAAIO,EAExBI,EAAI,EAAGA,EAAIV,EAAW,OAAQU,IAAK,CAC1C,IAAIC,EAAKX,EAAWU,CAAC,EACjBE,EAAKZ,EAAWU,EAAIV,EAAW,OAAS,EAAIU,EAAI,EAAI,CAAC,EACrDG,EAAYC,GACdf,EACAE,EACA,CAAE,EAAGO,EAAOG,EAAG,EAAG,EAAGF,EAAME,EAAG,CAAE,EAChC,CAAE,EAAGH,EAAOI,EAAG,EAAG,EAAGH,EAAMG,EAAG,CAAE,CAClC,EACIC,GACFT,EAAc,KAAKS,CAAS,CAEhC,CAEA,OAAKT,EAAc,QAKfA,EAAc,OAAS,GAEzBA,EAAc,KAAK,SAAUW,EAAGC,EAAG,CACjC,IAAIC,EAAMF,EAAE,EAAId,EAAM,EAClBiB,EAAMH,EAAE,EAAId,EAAM,EAClBkB,EAAQ,KAAK,KAAKF,EAAMA,EAAMC,EAAMA,CAAG,EAEvCE,EAAMJ,EAAE,EAAIf,EAAM,EAClBoB,EAAML,EAAE,EAAIf,EAAM,EAClBqB,EAAQ,KAAK,KAAKF,EAAMA,EAAMC,EAAMA,CAAG,EAE3C,OAAOF,EAAQG,EAAQ,GAAKH,IAAUG,EAAQ,EAAI,CACpD,CAAC,EAEIlB,EAAc,CAAC,GAjBbL,CAkBX,CAvDSwB,EAAAzB,GAAA,oBCdT,IAAM0B,GAAgBC,EAAA,CAACC,EAAMC,IAAU,CACrC,IAAIC,EAAIF,EAAK,EACTG,EAAIH,EAAK,EAITI,EAAKH,EAAM,EAAIC,EACfG,EAAKJ,EAAM,EAAIE,EACfG,EAAIN,EAAK,MAAQ,EACjBO,EAAIP,EAAK,OAAS,EAElBQ,EAAIC,EACR,OAAI,KAAK,IAAIJ,CAAE,EAAIC,EAAI,KAAK,IAAIF,CAAE,EAAIG,GAEhCF,EAAK,IACPE,EAAI,CAACA,GAEPC,EAAKH,IAAO,EAAI,EAAKE,EAAIH,EAAMC,EAC/BI,EAAKF,IAGDH,EAAK,IACPE,EAAI,CAACA,GAEPE,EAAKF,EACLG,EAAKL,IAAO,EAAI,EAAKE,EAAID,EAAMD,GAG1B,CAAE,EAAGF,EAAIM,EAAI,EAAGL,EAAIM,CAAG,CAChC,EA7BsB,iBA+BfC,GAAQZ,GCrBf,IAAOa,EAAQ,CACb,KAAAC,GACA,OAAAC,GACA,QAAAC,GACA,QAAAC,GACA,KAAAC,EACF,ECTO,IAAMC,EAAcC,EAAA,MAAOC,EAAQC,EAAMC,EAAUC,IAAW,CACnE,IAAMC,EAASC,EAAU,EACrBC,EACEC,EAAgBN,EAAK,eAAiBO,EAASJ,EAAO,UAAU,UAAU,EAC3EF,EAGHI,EAAUJ,EAFVI,EAAU,eAMZ,IAAMG,EAAWT,EACd,OAAO,GAAG,EACV,KAAK,QAASM,CAAO,EACrB,KAAK,KAAML,EAAK,OAASA,EAAK,EAAE,EAG7BS,EAAQD,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,QAASR,EAAK,UAAU,EAGnFU,EACAV,EAAK,YAAc,OACrBU,EAAY,GAEZA,EAAY,OAAOV,EAAK,WAAc,SAAWA,EAAK,UAAYA,EAAK,UAAU,CAAC,EAGpF,IAAMW,EAAWF,EAAM,KAAK,EACxBG,EACAZ,EAAK,YAAc,WAErBY,EAAOC,GACLJ,EACAK,GAAaC,GAAeL,CAAS,EAAGP,CAAM,EAC9C,CACE,cAAAG,EACA,MAAON,EAAK,OAASG,EAAO,UAAU,cACtC,QAAS,qBACX,EACAA,CACF,EAEAS,EAAOD,EAAS,YACd,MAAMK,EACJF,GAAaC,GAAeL,CAAS,EAAGP,CAAM,EAC9CH,EAAK,WACL,GACAE,CACF,CACF,EAGF,IAAIe,EAAOL,EAAK,QAAQ,EAClBM,EAAclB,EAAK,QAAU,EAEnC,GAAIO,EAASJ,EAAO,UAAU,UAAU,EAAG,CACzC,IAAMgB,EAAMP,EAAK,SAAS,CAAC,EACrBQ,EAAKC,EAAOT,CAAI,EAGhBU,EAASH,EAAI,qBAAqB,KAAK,EAC7C,GAAIG,EAAQ,CACV,IAAMC,EAAYb,EAAU,QAAQ,cAAe,EAAE,EAAE,KAAK,IAAM,GAElE,MAAM,QAAQ,IACZ,CAAC,GAAGY,CAAM,EAAE,IACTE,GACC,IAAI,QAASC,GAAQ,CAInB,SAASC,GAAa,CAIpB,GAHAF,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,SAEtBD,EAAW,CAEb,IAAMI,EAAexB,EAAO,SACxBA,EAAO,SACP,OAAO,iBAAiB,SAAS,IAAI,EAAE,SAErCyB,EAAQ,SAASD,EAAc,EAAE,EADf,EACqC,KAC7DH,EAAI,MAAM,SAAWI,EACrBJ,EAAI,MAAM,SAAWI,CACvB,MACEJ,EAAI,MAAM,MAAQ,OAEpBC,EAAID,CAAG,CACT,CAjBS1B,EAAA4B,EAAA,cAkBT,WAAW,IAAM,CACXF,EAAI,UACNE,EAAW,CAEf,CAAC,EACDF,EAAI,iBAAiB,QAASE,CAAU,EACxCF,EAAI,iBAAiB,OAAQE,CAAU,CACzC,CAAC,CACL,CACF,CACF,CAEAT,EAAOE,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASH,EAAK,KAAK,EAC3BG,EAAG,KAAK,SAAUH,EAAK,MAAM,CAC/B,CAGA,OAAIX,EACFG,EAAM,KAAK,YAAa,aAAe,CAACQ,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAEtFR,EAAM,KAAK,YAAa,gBAA0B,CAACQ,EAAK,OAAS,EAAI,GAAG,EAEtEjB,EAAK,aACPS,EAAM,KAAK,YAAa,aAAe,CAACQ,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAExFR,EAAM,OAAO,OAAQ,cAAc,EAE5B,CAAE,SAAAD,EAAU,KAAAS,EAAM,YAAAC,EAAa,MAAAT,CAAM,CAC9C,EAtH2B,eAwHdoB,EAAmB/B,EAAA,CAACE,EAAM8B,IAAY,CACjD,IAAMb,EAAOa,EAAQ,KAAK,EAAE,QAAQ,EACpC9B,EAAK,MAAQiB,EAAK,MAClBjB,EAAK,OAASiB,EAAK,MACrB,EAJgC,oBAYzB,SAASc,EAAmBhC,EAAQiC,EAAGC,EAAGC,EAAQ,CACvD,OAAOnC,EACJ,OAAO,UAAW,cAAc,EAChC,KACC,SACAmC,EACG,IAAI,SAAUC,EAAG,CAChB,OAAOA,EAAE,EAAI,IAAMA,EAAE,CACvB,CAAC,EACA,KAAK,GAAG,CACb,EACC,KAAK,QAAS,iBAAiB,EAC/B,KAAK,YAAa,aAAe,CAACH,EAAI,EAAI,IAAMC,EAAI,EAAI,GAAG,CAChE,CAbgBnC,EAAAiC,EAAA,sBCtIhB,IAAMK,GAAOC,EAAA,MAAOC,EAAQC,IAAS,CACbA,EAAK,eAAiBC,EAAU,EAAE,UAAU,aAEhED,EAAK,YAAc,IAErB,GAAM,CAAE,SAAAE,EAAU,KAAAC,EAAM,YAAAC,CAAY,EAAI,MAAMC,EAC5CN,EACAC,EACA,QAAUA,EAAK,QACf,EACF,EAEAM,EAAI,KAAK,aAAcN,EAAK,OAAO,EAEnC,IAAMO,EAAOL,EAAS,OAAO,OAAQ,cAAc,EAEnD,OAAAK,EACG,KAAK,KAAMP,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAK,CAACG,EAAK,MAAQ,EAAIC,CAAW,EACvC,KAAK,IAAK,CAACD,EAAK,OAAS,EAAIC,CAAW,EACxC,KAAK,QAASD,EAAK,MAAQH,EAAK,OAAO,EACvC,KAAK,SAAUG,EAAK,OAASH,EAAK,OAAO,EAE5CQ,EAAiBR,EAAMO,CAAI,EAE3BP,EAAK,UAAY,SAAUS,EAAO,CAChC,OAAOC,EAAU,KAAKV,EAAMS,CAAK,CACnC,EAEOP,CACT,EA/Ba,QAiCNS,GAAQd,GC5Bf,IAAMe,GAAcC,EAACC,GACfA,EACK,IAAMA,EAER,GAJW,eAMdC,EAAqBF,EAAA,CAACG,EAAMC,IACzB,GAAGA,GAA8B,cAAc,GAAGL,GAAYI,EAAK,OAAO,CAAC,IAAIJ,GACpFI,EAAK,KACP,CAAC,GAHwB,sBAMrBE,GAAWL,EAAA,MAAOM,EAAQH,IAAS,CACvC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBS,EAAIF,EAAIC,EAERE,EAAS,CACb,CAAE,EAAGD,EAAI,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGA,EAAG,EAAG,CAACA,EAAI,CAAE,EAClB,CAAE,EAAGA,EAAI,EAAG,EAAG,CAACA,CAAE,EAClB,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,CACpB,EAEAE,EAAI,KAAK,wBAAwB,EAEjC,IAAMC,EAAeC,EAAmBT,EAAUK,EAAGA,EAAGC,CAAM,EAC9D,OAAAE,EAAa,KAAK,QAASZ,EAAK,KAAK,EACrCc,EAAiBd,EAAMY,CAAY,EAEnCZ,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAAJ,EAAI,KAAK,kBAAkB,EACpBK,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EA/BiB,YAiCXa,GAASpB,EAAA,CAACM,EAAQH,IAAS,CAC/B,IAAMI,EAAWD,EACd,OAAO,GAAG,EACV,KAAK,QAAS,cAAc,EAC5B,KAAK,KAAMH,EAAK,OAASA,EAAK,EAAE,EAE7BS,EAAI,GACJC,EAAS,CACb,CAAE,EAAG,EAAG,EAAGD,EAAI,CAAE,EACjB,CAAE,EAAGA,EAAI,EAAG,EAAG,CAAE,EACjB,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,EAClB,CAAE,EAAG,CAACA,EAAI,EAAG,EAAG,CAAE,CACpB,EAWA,OATeL,EAAS,OAAO,UAAW,cAAc,EAAE,KACxD,SACAM,EACG,IAAI,SAAUQ,EAAG,CAChB,OAAOA,EAAE,EAAI,IAAMA,EAAE,CACvB,CAAC,EACA,KAAK,GAAG,CACb,EAEO,KAAK,QAAS,aAAa,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,EACpFlB,EAAK,MAAQ,GACbA,EAAK,OAAS,GAEdA,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,OAAOhB,EAAM,GAAIe,CAAK,CACzC,EAEOX,CACT,EAhCe,UAkCTe,GAAUtB,EAAA,MAAOM,EAAQH,IAAS,CACtC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMoB,EAAI,EACJZ,EAAIH,EAAK,OAASL,EAAK,QACvBqB,EAAIb,EAAIY,EACRb,EAAIF,EAAK,MAAQ,EAAIgB,EAAIrB,EAAK,QAC9BU,EAAS,CACb,CAAE,EAAGW,EAAG,EAAG,CAAE,EACb,CAAE,EAAGd,EAAIc,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGd,EAAG,EAAG,CAACC,EAAI,CAAE,EAClB,CAAE,EAAGD,EAAIc,EAAG,EAAG,CAACb,CAAE,EAClB,CAAE,EAAGa,EAAG,EAAG,CAACb,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,CACpB,EAEMc,EAAMT,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACrD,OAAAY,EAAI,KAAK,QAAStB,EAAK,KAAK,EAC5Bc,EAAiBd,EAAMsB,CAAG,EAE1BtB,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EA9BgB,WAgCVmB,GAAc1B,EAAA,MAAOM,EAAQH,IAAS,CAC1C,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYH,EAAQH,EAAM,OAAW,EAAI,EAEpEoB,EAAI,EACJZ,EAAIH,EAAK,OAAS,EAAIL,EAAK,QAC3BqB,EAAIb,EAAIY,EACRb,EAAIF,EAAK,MAAQ,EAAIgB,EAAIrB,EAAK,QAE9BU,EAASc,GAAexB,EAAK,WAAYK,EAAML,CAAI,EAEnDyB,EAAaZ,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EAC5D,OAAAe,EAAW,KAAK,QAASzB,EAAK,KAAK,EACnCc,EAAiBd,EAAMyB,CAAU,EAEjCzB,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EAnBoB,eAqBdsB,GAAsB7B,EAAA,MAAOM,EAAQH,IAAS,CAClD,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAG,CAACF,EAAI,EAAG,EAAG,CAAE,EAClB,CAAE,EAAGD,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAG,EAAG,CAACC,CAAE,EACd,CAAE,EAAG,CAACA,EAAI,EAAG,EAAG,CAACA,CAAE,EACnB,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,CACpB,EAGA,OADWK,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACjD,KAAK,QAASV,EAAK,KAAK,EAE3BA,EAAK,MAAQO,EAAIC,EACjBR,EAAK,OAASQ,EAEdR,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EA7B4B,uBA+BtBuB,GAAa9B,EAAA,MAAOM,EAAQH,IAAS,CACzC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYH,EAAQH,EAAMD,EAAmBC,CAAI,EAAG,EAAI,EAEnFO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAI,GAAKF,EAAK,EAAG,EAAG,CAAE,EACxB,CAAE,EAAGD,EAAIC,EAAI,EAAG,EAAG,CAAE,EACrB,CAAE,EAAGD,EAAK,EAAIC,EAAK,EAAG,EAAG,CAACA,CAAE,EAC5B,CAAE,EAAGA,EAAI,EAAG,EAAG,CAACA,CAAE,CACpB,EAEMoB,EAAKf,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACpD,OAAAkB,EAAG,KAAK,QAAS5B,EAAK,KAAK,EAC3Bc,EAAiBd,EAAM4B,CAAE,EAEzB5B,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EArBmB,cAuBbyB,GAAYhC,EAAA,MAAOM,EAAQH,IAAS,CACxC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAI,EAAIF,EAAK,EAAG,EAAG,CAAE,EACvB,CAAE,EAAGD,EAAIC,EAAI,EAAG,EAAG,CAAE,EACrB,CAAE,EAAGD,EAAK,EAAIC,EAAK,EAAG,EAAG,CAACA,CAAE,EAC5B,CAAE,EAAG,CAACA,EAAI,EAAG,EAAG,CAACA,CAAE,CACrB,EAEMoB,EAAKf,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACpD,OAAAkB,EAAG,KAAK,QAAS5B,EAAK,KAAK,EAC3Bc,EAAiBd,EAAM4B,CAAE,EAEzB5B,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EA1BkB,aA4BZ0B,GAAYjC,EAAA,MAAOM,EAAQH,IAAS,CACxC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAI,GAAKF,EAAK,EAAG,EAAG,CAAE,EACxB,CAAE,EAAGD,EAAK,EAAIC,EAAK,EAAG,EAAG,CAAE,EAC3B,CAAE,EAAGD,EAAIC,EAAI,EAAG,EAAG,CAACA,CAAE,EACtB,CAAE,EAAGA,EAAI,EAAG,EAAG,CAACA,CAAE,CACpB,EAEMoB,EAAKf,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACpD,OAAAkB,EAAG,KAAK,QAAS5B,EAAK,KAAK,EAC3Bc,EAAiBd,EAAM4B,CAAE,EAEzB5B,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EA1BkB,aA4BZ2B,GAAgBlC,EAAA,MAAOM,EAAQH,IAAS,CAC5C,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAGF,EAAI,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGD,EAAIC,EAAI,EAAG,EAAG,CAAE,EACrB,CAAE,EAAGD,EAAK,EAAIC,EAAK,EAAG,EAAG,CAACA,CAAE,EAC5B,CAAE,EAAI,GAAKA,EAAK,EAAG,EAAG,CAACA,CAAE,CAC3B,EAEMoB,EAAKf,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACpD,OAAAkB,EAAG,KAAK,QAAS5B,EAAK,KAAK,EAC3Bc,EAAiBd,EAAM4B,CAAE,EAEzB5B,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EA1BsB,iBA4BhB4B,GAAuBnC,EAAA,MAAOM,EAAQH,IAAS,CACnD,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGH,EAAIC,EAAI,EAAG,EAAG,CAAE,EACrB,CAAE,EAAGD,EAAG,EAAG,CAACC,EAAI,CAAE,EAClB,CAAE,EAAGD,EAAIC,EAAI,EAAG,EAAG,CAACA,CAAE,EACtB,CAAE,EAAG,EAAG,EAAG,CAACA,CAAE,CAChB,EAEMoB,EAAKf,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACpD,OAAAkB,EAAG,KAAK,QAAS5B,EAAK,KAAK,EAC3Bc,EAAiBd,EAAM4B,CAAE,EAEzB5B,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EA3B6B,wBA6BvB6B,GAAWpC,EAAA,MAAOM,EAAQH,IAAS,CACvC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBkC,EAAK3B,EAAI,EACT4B,EAAKD,GAAM,IAAM3B,EAAI,IACrBC,EAAIH,EAAK,OAAS8B,EAAKnC,EAAK,QAE5BoC,EACJ,OACAD,EACA,MACAD,EACA,IACAC,EACA,UACA5B,EACA,QACA2B,EACA,IACAC,EACA,UACA,CAAC5B,EACD,UACAC,EACA,MACA0B,EACA,IACAC,EACA,UACA5B,EACA,UACA,CAACC,EAEGoB,EAAKxB,EACR,KAAK,iBAAkB+B,CAAE,EACzB,OAAO,OAAQ,cAAc,EAC7B,KAAK,QAASnC,EAAK,KAAK,EACxB,KAAK,IAAKoC,CAAK,EACf,KAAK,YAAa,aAAe,CAAC7B,EAAI,EAAI,IAAM,EAAEC,EAAI,EAAI2B,GAAM,GAAG,EAEtE,OAAArB,EAAiBd,EAAM4B,CAAE,EAEzB5B,EAAK,UAAY,SAAUe,EAAO,CAChC,IAAMsB,EAAMrB,EAAU,KAAKhB,EAAMe,CAAK,EAChCuB,EAAID,EAAI,EAAIrC,EAAK,EAEvB,GACEkC,GAAM,IACL,KAAK,IAAII,CAAC,EAAItC,EAAK,MAAQ,GACzB,KAAK,IAAIsC,CAAC,GAAKtC,EAAK,MAAQ,GAAK,KAAK,IAAIqC,EAAI,EAAIrC,EAAK,CAAC,EAAIA,EAAK,OAAS,EAAImC,GACjF,CAGA,IAAII,EAAIJ,EAAKA,GAAM,EAAKG,EAAIA,GAAMJ,EAAKA,IACnCK,GAAK,IACPA,EAAI,KAAK,KAAKA,CAAC,GAEjBA,EAAIJ,EAAKI,EACLxB,EAAM,EAAIf,EAAK,EAAI,IACrBuC,EAAI,CAACA,GAGPF,EAAI,GAAKE,CACX,CAEA,OAAOF,CACT,EAEOjC,CACT,EA3EiB,YA6EXoC,GAAO3C,EAAA,MAAOM,EAAQH,IAAS,CACnC,GAAM,CAAE,SAAAI,EAAU,KAAAC,EAAM,YAAAoC,CAAY,EAAI,MAAMnC,EAC5CH,EACAH,EACA,QAAUA,EAAK,QAAU,IAAMA,EAAK,MACpC,EACF,EAGMwC,EAAOpC,EAAS,OAAO,OAAQ,cAAc,EAK7CsC,EAAa1C,EAAK,WAAaA,EAAK,MAAQK,EAAK,MAAQL,EAAK,QAC9D2C,EAAc3C,EAAK,WAAaA,EAAK,OAASK,EAAK,OAASL,EAAK,QACjEsC,EAAItC,EAAK,WAAa,CAAC0C,EAAa,EAAI,CAACrC,EAAK,MAAQ,EAAIoC,EAC1DF,EAAIvC,EAAK,WAAa,CAAC2C,EAAc,EAAI,CAACtC,EAAK,OAAS,EAAIoC,EAWlE,GAVAD,EACG,KAAK,QAAS,uBAAuB,EACrC,KAAK,QAASxC,EAAK,KAAK,EACxB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKsC,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASG,CAAU,EACxB,KAAK,SAAUC,CAAW,EAEzB3C,EAAK,MAAO,CACd,IAAM4C,EAAW,IAAI,IAAI,OAAO,KAAK5C,EAAK,KAAK,CAAC,EAC5CA,EAAK,MAAM,UACb6C,GAAyBL,EAAMxC,EAAK,MAAM,QAAS0C,EAAYC,CAAW,EAC1EC,EAAS,OAAO,SAAS,GAE3BA,EAAS,QAASE,GAAY,CAC5BnC,EAAI,KAAK,yBAAyBmC,CAAO,EAAE,CAC7C,CAAC,CACH,CAEA,OAAAhC,EAAiBd,EAAMwC,CAAI,EAE3BxC,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,KAAKhB,EAAMe,CAAK,CACnC,EAEOX,CACT,EA9Ca,QAgDP2C,GAAYlD,EAAA,MAAOM,EAAQH,IAAS,CACxC,GAAM,CAAE,SAAAI,EAAU,KAAAC,EAAM,YAAAoC,CAAY,EAAI,MAAMnC,EAC5CH,EACAH,EACA,QAAUA,EAAK,QACf,EACF,EAGMwC,EAAOpC,EAAS,OAAO,OAAQ,cAAc,EAI7CsC,EAAa1C,EAAK,WAAaA,EAAK,MAAQK,EAAK,MAAQL,EAAK,QAC9D2C,EAAc3C,EAAK,WAAaA,EAAK,OAASK,EAAK,OAASL,EAAK,QACjEsC,EAAItC,EAAK,WAAa,CAAC0C,EAAa,EAAI,CAACrC,EAAK,MAAQ,EAAIoC,EAC1DF,EAAIvC,EAAK,WAAa,CAAC2C,EAAc,EAAI,CAACtC,EAAK,OAAS,EAAIoC,EAWlE,GAVAD,EACG,KAAK,QAAS,yCAAyC,EACvD,KAAK,QAASxC,EAAK,KAAK,EACxB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKsC,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASG,CAAU,EACxB,KAAK,SAAUC,CAAW,EAEzB3C,EAAK,MAAO,CACd,IAAM4C,EAAW,IAAI,IAAI,OAAO,KAAK5C,EAAK,KAAK,CAAC,EAC5CA,EAAK,MAAM,UACb6C,GAAyBL,EAAMxC,EAAK,MAAM,QAAS0C,EAAYC,CAAW,EAC1EC,EAAS,OAAO,SAAS,GAE3BA,EAAS,QAASE,GAAY,CAC5BnC,EAAI,KAAK,yBAAyBmC,CAAO,EAAE,CAC7C,CAAC,CACH,CAEA,OAAAhC,EAAiBd,EAAMwC,CAAI,EAE3BxC,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,KAAKhB,EAAMe,CAAK,CACnC,EAEOX,CACT,EA7CkB,aA+CZ4C,GAAYnD,EAAA,MAAOM,EAAQH,IAAS,CACxC,GAAM,CAAE,SAAAI,CAAS,EAAI,MAAME,EAAYH,EAAQH,EAAM,QAAS,EAAI,EAElEW,EAAI,MAAM,aAAcX,EAAK,KAAK,EAElC,IAAMwC,EAAOpC,EAAS,OAAO,OAAQ,cAAc,EAG7CsC,EAAa,EACbC,EAAc,EAIpB,GAHAH,EAAK,KAAK,QAASE,CAAU,EAAE,KAAK,SAAUC,CAAW,EACzDvC,EAAS,KAAK,QAAS,iBAAiB,EAEpCJ,EAAK,MAAO,CACd,IAAM4C,EAAW,IAAI,IAAI,OAAO,KAAK5C,EAAK,KAAK,CAAC,EAC5CA,EAAK,MAAM,UACb6C,GAAyBL,EAAMxC,EAAK,MAAM,QAAS0C,EAAYC,CAAW,EAC1EC,EAAS,OAAO,SAAS,GAE3BA,EAAS,QAASE,GAAY,CAC5BnC,EAAI,KAAK,yBAAyBmC,CAAO,EAAE,CAC7C,CAAC,CACH,CAEA,OAAAhC,EAAiBd,EAAMwC,CAAI,EAE3BxC,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,KAAKhB,EAAMe,CAAK,CACnC,EAEOX,CACT,EA/BkB,aAuClB,SAASyC,GAAyBL,EAAMS,EAASP,EAAYC,EAAa,CACxE,IAAMO,EAAkB,CAAC,EACnBC,EAAYtD,EAACuD,GAAW,CAC5BF,EAAgB,KAAKE,EAAQ,CAAC,CAChC,EAFkB,aAGZC,EAAaxD,EAACuD,GAAW,CAC7BF,EAAgB,KAAK,EAAGE,CAAM,CAChC,EAFmB,cAGfH,EAAQ,SAAS,GAAG,GACtBtC,EAAI,MAAM,gBAAgB,EAC1BwC,EAAUT,CAAU,GAEpBW,EAAWX,CAAU,EAEnBO,EAAQ,SAAS,GAAG,GACtBtC,EAAI,MAAM,kBAAkB,EAC5BwC,EAAUR,CAAW,GAErBU,EAAWV,CAAW,EAEpBM,EAAQ,SAAS,GAAG,GACtBtC,EAAI,MAAM,mBAAmB,EAC7BwC,EAAUT,CAAU,GAEpBW,EAAWX,CAAU,EAEnBO,EAAQ,SAAS,GAAG,GACtBtC,EAAI,MAAM,iBAAiB,EAC3BwC,EAAUR,CAAW,GAErBU,EAAWV,CAAW,EAExBH,EAAK,KAAK,mBAAoBU,EAAgB,KAAK,GAAG,CAAC,CACzD,CAjCSrD,EAAAgD,GAAA,4BAmCT,IAAMS,GAAgBzD,EAAA,MAAOM,EAAQH,IAAS,CAG5C,IAAIuD,EACCvD,EAAK,QAGRuD,EAAU,QAAUvD,EAAK,QAFzBuD,EAAU,eAKZ,IAAMnD,EAAWD,EACd,OAAO,GAAG,EACV,KAAK,QAASoD,CAAO,EACrB,KAAK,KAAMvD,EAAK,OAASA,EAAK,EAAE,EAG7BwC,EAAOpC,EAAS,OAAO,OAAQ,cAAc,EAE7CoD,EAAYpD,EAAS,OAAO,MAAM,EAElCqD,EAAQrD,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAElDsD,EAAQ1D,EAAK,UAAU,KAAOA,EAAK,UAAU,KAAK,EAAIA,EAAK,UAG7D2D,EAAQ,GACR,OAAOD,GAAU,SACnBC,EAAQD,EAAM,CAAC,EAEfC,EAAQD,EAEV/C,EAAI,KAAK,mBAAoBgD,EAAOD,EAAO,OAAOA,GAAU,QAAQ,EAEpE,IAAME,EAAOH,EAAM,KAAK,EAAE,YAAY,MAAMI,EAAYF,EAAO3D,EAAK,WAAY,GAAM,EAAI,CAAC,EACvFK,EAAO,CAAE,MAAO,EAAG,OAAQ,CAAE,EACjC,GAAIyD,EAASC,EAAU,EAAE,UAAU,UAAU,EAAG,CAC9C,IAAMC,EAAMJ,EAAK,SAAS,CAAC,EACrBK,EAAKC,EAAON,CAAI,EACtBvD,EAAO2D,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAAS5D,EAAK,KAAK,EAC3B4D,EAAG,KAAK,SAAU5D,EAAK,MAAM,CAC/B,CACAM,EAAI,KAAK,SAAU+C,CAAK,EACxB,IAAMS,EAAWT,EAAM,MAAM,EAAGA,EAAM,MAAM,EACxCU,EAAWR,EAAK,QAAQ,EACtBS,EAAQZ,EACX,KAAK,EACL,YACC,MAAMI,EACJM,EAAS,KAAOA,EAAS,KAAK,OAAO,EAAIA,EACzCnE,EAAK,WACL,GACA,EACF,CACF,EAEF,GAAI8D,EAASC,EAAU,EAAE,UAAU,UAAU,EAAG,CAC9C,IAAMC,EAAMK,EAAM,SAAS,CAAC,EACtBJ,EAAKC,EAAOG,CAAK,EACvBhE,EAAO2D,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAAS5D,EAAK,KAAK,EAC3B4D,EAAG,KAAK,SAAU5D,EAAK,MAAM,CAC/B,CAGA,IAAMoC,EAAczC,EAAK,QAAU,EACnC,OAAAkE,EAAOG,CAAK,EAAE,KACZ,YACA,eAEGhE,EAAK,MAAQ+D,EAAS,MAAQ,GAAKA,EAAS,MAAQ/D,EAAK,OAAS,GACnE,MACC+D,EAAS,OAAS3B,EAAc,GACjC,GACJ,EACAyB,EAAON,CAAI,EAAE,KACX,YACA,eAEGvD,EAAK,MAAQ+D,EAAS,MAAQ,EAAI,EAAEA,EAAS,MAAQ/D,EAAK,OAAS,GACpE,MAGJ,EAIAA,EAAOoD,EAAM,KAAK,EAAE,QAAQ,EAG5BA,EAAM,KACJ,YACA,aAAe,CAACpD,EAAK,MAAQ,EAAI,MAAQ,CAACA,EAAK,OAAS,EAAIoC,EAAc,GAAK,GACjF,EAEAD,EACG,KAAK,QAAS,mBAAmB,EACjC,KAAK,IAAK,CAACnC,EAAK,MAAQ,EAAIoC,CAAW,EACvC,KAAK,IAAK,CAACpC,EAAK,OAAS,EAAIoC,CAAW,EACxC,KAAK,QAASpC,EAAK,MAAQL,EAAK,OAAO,EACvC,KAAK,SAAUK,EAAK,OAASL,EAAK,OAAO,EAE5CwD,EACG,KAAK,QAAS,SAAS,EACvB,KAAK,KAAM,CAACnD,EAAK,MAAQ,EAAIoC,CAAW,EACxC,KAAK,KAAMpC,EAAK,MAAQ,EAAIoC,CAAW,EACvC,KAAK,KAAM,CAACpC,EAAK,OAAS,EAAIoC,EAAc2B,EAAS,OAAS3B,CAAW,EACzE,KAAK,KAAM,CAACpC,EAAK,OAAS,EAAIoC,EAAc2B,EAAS,OAAS3B,CAAW,EAE5E3B,EAAiBd,EAAMwC,CAAI,EAE3BxC,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,KAAKhB,EAAMe,CAAK,CACnC,EAEOX,CACT,EApHsB,iBAsHhBkE,GAAUzE,EAAA,MAAOM,EAAQH,IAAS,CACtC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMQ,EAAIH,EAAK,OAASL,EAAK,QACvBO,EAAIF,EAAK,MAAQG,EAAI,EAAIR,EAAK,QAG9BwC,EAAOpC,EACV,OAAO,OAAQ,cAAc,EAC7B,KAAK,QAASJ,EAAK,KAAK,EACxB,KAAK,KAAMQ,EAAI,CAAC,EAChB,KAAK,KAAMA,EAAI,CAAC,EAChB,KAAK,IAAK,CAACD,EAAI,CAAC,EAChB,KAAK,IAAK,CAACC,EAAI,CAAC,EAChB,KAAK,QAASD,CAAC,EACf,KAAK,SAAUC,CAAC,EAEnB,OAAAM,EAAiBd,EAAMwC,CAAI,EAE3BxC,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,KAAKhB,EAAMe,CAAK,CACnC,EAEOX,CACT,EA7BgB,WA+BVmE,GAAS1E,EAAA,MAAOM,EAAQH,IAAS,CACrC,GAAM,CAAE,SAAAI,EAAU,KAAAC,EAAM,YAAAoC,CAAY,EAAI,MAAMnC,EAC5CH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EACMuE,EAASnE,EAAS,OAAO,SAAU,cAAc,EAGvD,OAAAmE,EACG,KAAK,QAASvE,EAAK,KAAK,EACxB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKK,EAAK,MAAQ,EAAIoC,CAAW,EACtC,KAAK,QAASpC,EAAK,MAAQL,EAAK,OAAO,EACvC,KAAK,SAAUK,EAAK,OAASL,EAAK,OAAO,EAE5CW,EAAI,KAAK,aAAa,EAEtBG,EAAiBd,EAAMuE,CAAM,EAE7BvE,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAAJ,EAAI,KAAK,mBAAoBX,EAAMK,EAAK,MAAQ,EAAIoC,EAAa1B,CAAK,EAC/DC,EAAU,OAAOhB,EAAMK,EAAK,MAAQ,EAAIoC,EAAa1B,CAAK,CACnE,EAEOX,CACT,EA5Be,UA8BToE,GAAe3E,EAAA,MAAOM,EAAQH,IAAS,CAC3C,GAAM,CAAE,SAAAI,EAAU,KAAAC,EAAM,YAAAoC,CAAY,EAAI,MAAMnC,EAC5CH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EACMyE,EAAM,EACNC,EAActE,EAAS,OAAO,IAAK,cAAc,EACjDuE,EAAcD,EAAY,OAAO,QAAQ,EACzCE,EAAcF,EAAY,OAAO,QAAQ,EAE/C,OAAAA,EAAY,KAAK,QAAS1E,EAAK,KAAK,EAGpC2E,EACG,KAAK,QAAS3E,EAAK,KAAK,EACxB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKK,EAAK,MAAQ,EAAIoC,EAAcgC,CAAG,EAC5C,KAAK,QAASpE,EAAK,MAAQL,EAAK,QAAUyE,EAAM,CAAC,EACjD,KAAK,SAAUpE,EAAK,OAASL,EAAK,QAAUyE,EAAM,CAAC,EAEtDG,EACG,KAAK,QAAS5E,EAAK,KAAK,EACxB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKK,EAAK,MAAQ,EAAIoC,CAAW,EACtC,KAAK,QAASpC,EAAK,MAAQL,EAAK,OAAO,EACvC,KAAK,SAAUK,EAAK,OAASL,EAAK,OAAO,EAE5CW,EAAI,KAAK,mBAAmB,EAE5BG,EAAiBd,EAAM2E,CAAW,EAElC3E,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAAJ,EAAI,KAAK,yBAA0BX,EAAMK,EAAK,MAAQ,EAAIoC,EAAcgC,EAAK1D,CAAK,EAC3EC,EAAU,OAAOhB,EAAMK,EAAK,MAAQ,EAAIoC,EAAcgC,EAAK1D,CAAK,CACzE,EAEOX,CACT,EAzCqB,gBA2CfyE,GAAahF,EAAA,MAAOM,EAAQH,IAAS,CACzC,GAAM,CAAE,SAAAI,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAC/BH,EACAH,EACAD,EAAmBC,EAAM,MAAS,EAClC,EACF,EAEMO,EAAIF,EAAK,MAAQL,EAAK,QACtBQ,EAAIH,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGH,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAG,EAAG,CAACC,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAACA,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAG,GAAI,EAAG,CAAE,EACd,CAAE,EAAGD,EAAI,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGA,EAAI,EAAG,EAAG,CAACC,CAAE,EAClB,CAAE,EAAG,GAAI,EAAG,CAACA,CAAE,EACf,CAAE,EAAG,GAAI,EAAG,CAAE,CAChB,EAEMoB,EAAKf,EAAmBT,EAAUG,EAAGC,EAAGE,CAAM,EACpD,OAAAkB,EAAG,KAAK,QAAS5B,EAAK,KAAK,EAC3Bc,EAAiBd,EAAM4B,CAAE,EAEzB5B,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,QAAQhB,EAAMU,EAAQK,CAAK,CAC9C,EAEOX,CACT,EAhCmB,cAkCb0E,GAAQjF,EAAA,CAACM,EAAQH,IAAS,CAC9B,IAAMI,EAAWD,EACd,OAAO,GAAG,EACV,KAAK,QAAS,cAAc,EAC5B,KAAK,KAAMH,EAAK,OAASA,EAAK,EAAE,EAC7BuE,EAASnE,EAAS,OAAO,SAAU,cAAc,EAGvD,OAAAmE,EAAO,KAAK,QAAS,aAAa,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,EAEpFzD,EAAiBd,EAAMuE,CAAM,EAE7BvE,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,OAAOhB,EAAM,EAAGe,CAAK,CACxC,EAEOX,CACT,EAjBc,SAmBR2E,GAAWlF,EAAA,CAACM,EAAQH,EAAMgF,IAAQ,CACtC,IAAM5E,EAAWD,EACd,OAAO,GAAG,EACV,KAAK,QAAS,cAAc,EAC5B,KAAK,KAAMH,EAAK,OAASA,EAAK,EAAE,EAE/BiF,EAAQ,GACRC,EAAS,GAETF,IAAQ,OACVC,EAAQ,GACRC,EAAS,IAGX,IAAM9C,EAAQhC,EACX,OAAO,MAAM,EACb,KAAK,IAAM,GAAK6E,EAAS,CAAC,EAC1B,KAAK,IAAM,GAAKC,EAAU,CAAC,EAC3B,KAAK,QAASD,CAAK,EACnB,KAAK,SAAUC,CAAM,EACrB,KAAK,QAAS,WAAW,EAE5B,OAAApE,EAAiBd,EAAMoC,CAAK,EAC5BpC,EAAK,OAASA,EAAK,OAASA,EAAK,QAAU,EAC3CA,EAAK,MAAQA,EAAK,MAAQA,EAAK,QAAU,EACzCA,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,KAAKhB,EAAMe,CAAK,CACnC,EAEOX,CACT,EA9BiB,YAgCX+E,GAAMtF,EAAA,CAACM,EAAQH,IAAS,CAC5B,IAAMI,EAAWD,EACd,OAAO,GAAG,EACV,KAAK,QAAS,cAAc,EAC5B,KAAK,KAAMH,EAAK,OAASA,EAAK,EAAE,EAC7B4E,EAAcxE,EAAS,OAAO,SAAU,cAAc,EACtDmE,EAASnE,EAAS,OAAO,SAAU,cAAc,EAEvD,OAAAmE,EAAO,KAAK,QAAS,aAAa,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,EAEpFK,EAAY,KAAK,QAAS,WAAW,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,EAEvF9D,EAAiBd,EAAMuE,CAAM,EAE7BvE,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,OAAOhB,EAAM,EAAGe,CAAK,CACxC,EAEOX,CACT,EAnBY,OAqBNgF,GAAYvF,EAAA,MAAOM,EAAQH,IAAS,CACxC,IAAMyC,EAAczC,EAAK,QAAU,EAC7BqF,EAAa,EACbC,EAAa,EAEf/B,EACCvD,EAAK,QAGRuD,EAAU,QAAUvD,EAAK,QAFzBuD,EAAU,eAKZ,IAAMnD,EAAWD,EACd,OAAO,GAAG,EACV,KAAK,QAASoD,CAAO,EACrB,KAAK,KAAMvD,EAAK,OAASA,EAAK,EAAE,EAG7BwC,EAAOpC,EAAS,OAAO,OAAQ,cAAc,EAC7CmF,EAAUnF,EAAS,OAAO,MAAM,EAChCoF,EAAapF,EAAS,OAAO,MAAM,EACrCqF,EAAW,EACXC,EAAYL,EAEVM,EAAiBvF,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAC7DwF,EAAc,EACZC,EAAe7F,EAAK,UAAU,cAAc,CAAC,EAG7C8F,EAAqB9F,EAAK,UAAU,YAAY,CAAC,EACnD,OAAMA,EAAK,UAAU,YAAY,CAAC,EAAI,OACtC,GACE+F,EAAiBJ,EACpB,KAAK,EACL,YAAY,MAAM9B,EAAYiC,EAAoB9F,EAAK,WAAY,GAAM,EAAI,CAAC,EAC7EgG,EAAgBD,EAAe,QAAQ,EAC3C,GAAIjC,EAASC,EAAU,EAAE,UAAU,UAAU,EAAG,CAC9C,IAAMC,EAAM+B,EAAe,SAAS,CAAC,EAC/B9B,EAAKC,EAAO6B,CAAc,EAChCC,EAAgBhC,EAAI,sBAAsB,EAC1CC,EAAG,KAAK,QAAS+B,EAAc,KAAK,EACpC/B,EAAG,KAAK,SAAU+B,EAAc,MAAM,CACxC,CACIhG,EAAK,UAAU,YAAY,CAAC,IAC9B0F,GAAaM,EAAc,OAASX,EACpCI,GAAYO,EAAc,OAG5B,IAAIC,EAAmBjG,EAAK,UAAU,MAElCA,EAAK,UAAU,OAAS,QAAaA,EAAK,UAAU,OAAS,KAC3D+D,EAAU,EAAE,UAAU,WACxBkC,GAAoB,OAASjG,EAAK,UAAU,KAAO,OAEnDiG,GAAoB,IAAMjG,EAAK,UAAU,KAAO,KAGpD,IAAMkG,EAAkBP,EACrB,KAAK,EACL,YAAY,MAAM9B,EAAYoC,EAAkBjG,EAAK,WAAY,GAAM,EAAI,CAAC,EAC/EkE,EAAOgC,CAAe,EAAE,KAAK,QAAS,YAAY,EAClD,IAAIC,EAAiBD,EAAgB,QAAQ,EAC7C,GAAIpC,EAASC,EAAU,EAAE,UAAU,UAAU,EAAG,CAC9C,IAAMC,EAAMkC,EAAgB,SAAS,CAAC,EAChCjC,EAAKC,EAAOgC,CAAe,EACjCC,EAAiBnC,EAAI,sBAAsB,EAC3CC,EAAG,KAAK,QAASkC,EAAe,KAAK,EACrClC,EAAG,KAAK,SAAUkC,EAAe,MAAM,CACzC,CACAT,GAAaS,EAAe,OAASd,EACjCc,EAAe,MAAQV,IACzBA,EAAWU,EAAe,OAE5B,IAAMC,EAAkB,CAAC,EACzBpG,EAAK,UAAU,QAAQ,QAAQ,MAAOqG,GAAW,CAC/C,IAAMC,EAAaD,EAAO,kBAAkB,EACxCE,EAAaD,EAAW,YACxBvC,EAAU,EAAE,UAAU,aACxBwC,EAAaA,EAAW,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,GAEpE,IAAMC,EAAMb,EACT,KAAK,EACL,YACC,MAAM9B,EACJ0C,EACAD,EAAW,SAAWA,EAAW,SAAWtG,EAAK,WACjD,GACA,EACF,CACF,EACEK,EAAOmG,EAAI,QAAQ,EACvB,GAAI1C,EAASC,EAAU,EAAE,UAAU,UAAU,EAAG,CAC9C,IAAMC,EAAMwC,EAAI,SAAS,CAAC,EACpBvC,EAAKC,EAAOsC,CAAG,EACrBnG,EAAO2D,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAAS5D,EAAK,KAAK,EAC3B4D,EAAG,KAAK,SAAU5D,EAAK,MAAM,CAC/B,CACIA,EAAK,MAAQoF,IACfA,EAAWpF,EAAK,OAElBqF,GAAarF,EAAK,OAASgF,EAC3Be,EAAgB,KAAKI,CAAG,CAC1B,CAAC,EAEDd,GAAaJ,EAEb,IAAMmB,EAAe,CAAC,EAsCtB,GArCAzG,EAAK,UAAU,QAAQ,QAAQ,MAAOqG,GAAW,CAC/C,IAAMC,EAAaD,EAAO,kBAAkB,EACxCK,EAAcJ,EAAW,YACzBvC,EAAU,EAAE,UAAU,aACxB2C,EAAcA,EAAY,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,GAEtE,IAAMF,EAAMb,EACT,KAAK,EACL,YACC,MAAM9B,EACJ6C,EACAJ,EAAW,SAAWA,EAAW,SAAWtG,EAAK,WACjD,GACA,EACF,CACF,EACEK,EAAOmG,EAAI,QAAQ,EACvB,GAAI1C,EAASC,EAAU,EAAE,UAAU,UAAU,EAAG,CAC9C,IAAMC,EAAMwC,EAAI,SAAS,CAAC,EACpBvC,EAAKC,EAAOsC,CAAG,EACrBnG,EAAO2D,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAAS5D,EAAK,KAAK,EAC3B4D,EAAG,KAAK,SAAU5D,EAAK,MAAM,CAC/B,CACIA,EAAK,MAAQoF,IACfA,EAAWpF,EAAK,OAElBqF,GAAarF,EAAK,OAASgF,EAE3BoB,EAAa,KAAKD,CAAG,CACvB,CAAC,EAEDd,GAAaJ,EAKTO,EAAc,CAChB,IAAIc,GAASlB,EAAWO,EAAc,OAAS,EAC/C9B,EAAO6B,CAAc,EAAE,KACrB,YACA,eAAkB,GAAKN,EAAY,EAAIkB,GAAS,KAAQ,GAAKjB,EAAa,EAAI,GAChF,EACAE,EAAcI,EAAc,OAASX,CACvC,CAEA,IAAIsB,GAASlB,EAAWU,EAAe,OAAS,EAChD,OAAAjC,EAAOgC,CAAe,EAAE,KACtB,YACA,eACI,GAAKT,EAAY,EAAIkB,GACvB,MACE,GAAKjB,EAAa,EAAIE,GACxB,GACJ,EACAA,GAAeO,EAAe,OAASd,EAEvCE,EACG,KAAK,QAAS,SAAS,EACvB,KAAK,KAAM,CAACE,EAAW,EAAIhD,CAAW,EACtC,KAAK,KAAMgD,EAAW,EAAIhD,CAAW,EACrC,KAAK,KAAM,CAACiD,EAAY,EAAIjD,EAAc6C,EAAaM,CAAW,EAClE,KAAK,KAAM,CAACF,EAAY,EAAIjD,EAAc6C,EAAaM,CAAW,EAErEA,GAAeN,EAEfc,EAAgB,QAASI,GAAQ,CAC/BtC,EAAOsC,CAAG,EAAE,KACV,YACA,cACE,CAACf,EAAW,EACZ,MACE,GAAKC,EAAa,EAAIE,EAAcN,EAAa,GACnD,GACJ,EAEA,IAAMsB,EAAaJ,GAAK,QAAQ,EAChCZ,IAAgBgB,GAAY,QAAU,GAAKvB,CAC7C,CAAC,EAEDO,GAAeN,EACfE,EACG,KAAK,QAAS,SAAS,EACvB,KAAK,KAAM,CAACC,EAAW,EAAIhD,CAAW,EACtC,KAAK,KAAMgD,EAAW,EAAIhD,CAAW,EACrC,KAAK,KAAM,CAACiD,EAAY,EAAIjD,EAAc6C,EAAaM,CAAW,EAClE,KAAK,KAAM,CAACF,EAAY,EAAIjD,EAAc6C,EAAaM,CAAW,EAErEA,GAAeN,EAEfmB,EAAa,QAASD,GAAQ,CAC5BtC,EAAOsC,CAAG,EAAE,KACV,YACA,cAAgB,CAACf,EAAW,EAAI,MAAS,GAAKC,EAAa,EAAIE,GAAe,GAChF,EACA,IAAMgB,EAAaJ,GAAK,QAAQ,EAChCZ,IAAgBgB,GAAY,QAAU,GAAKvB,CAC7C,CAAC,EAED7C,EACG,KAAK,QAASxC,EAAK,KAAK,EACxB,KAAK,QAAS,mBAAmB,EACjC,KAAK,IAAK,CAACyF,EAAW,EAAIhD,CAAW,EACrC,KAAK,IAAK,EAAEiD,EAAY,GAAKjD,CAAW,EACxC,KAAK,QAASgD,EAAWzF,EAAK,OAAO,EACrC,KAAK,SAAU0F,EAAY1F,EAAK,OAAO,EAE1Cc,EAAiBd,EAAMwC,CAAI,EAE3BxC,EAAK,UAAY,SAAUe,EAAO,CAChC,OAAOC,EAAU,KAAKhB,EAAMe,CAAK,CACnC,EAEOX,CACT,EA9NkB,aAgOZyG,GAAS,CACb,QAAS3G,GACT,UAAA6C,GACA,SAAA7C,GACA,KAAAsC,GACA,UAAAQ,GACA,cAAAM,GACA,OAAArC,GACA,OAAAsD,GACA,aAAAC,GACA,QAAAF,GACA,QAAAnD,GACA,YAAAI,GACA,oBAAAG,GACA,WAAAC,GACA,UAAAE,GACA,UAAAC,GACA,cAAAC,GACA,qBAAAC,GACA,SAAAC,GACA,MAAA6C,GACA,IAAAK,GACA,KAAA2B,GACA,WAAAjC,GACA,KAAME,GACN,KAAMA,GACN,UAAAK,EACF,EAEI2B,GAAY,CAAC,EAEJC,GAAanH,EAAA,MAAOoH,EAAMjH,EAAMkH,IAAkB,CAC7D,IAAIC,EACAvF,EAGJ,GAAI5B,EAAK,KAAM,CACb,IAAIoH,EACArD,EAAU,EAAE,gBAAkB,UAChCqD,EAAS,OACApH,EAAK,aACdoH,EAASpH,EAAK,YAAc,UAE9BmH,EAAQF,EAAK,OAAO,OAAO,EAAE,KAAK,aAAcjH,EAAK,IAAI,EAAE,KAAK,SAAUoH,CAAM,EAChFxF,EAAK,MAAMiF,GAAO7G,EAAK,KAAK,EAAEmH,EAAOnH,EAAMkH,CAAa,CAC1D,MACEtF,EAAK,MAAMiF,GAAO7G,EAAK,KAAK,EAAEiH,EAAMjH,EAAMkH,CAAa,EACvDC,EAAQvF,EAEV,OAAI5B,EAAK,SACP4B,EAAG,KAAK,QAAS5B,EAAK,OAAO,EAE3BA,EAAK,OACP4B,EAAG,KAAK,QAAS,gBAAkB5B,EAAK,KAAK,EAG/C+G,GAAU/G,EAAK,EAAE,EAAImH,EAEjBnH,EAAK,cACP+G,GAAU/G,EAAK,EAAE,EAAE,KAAK,QAAS+G,GAAU/G,EAAK,EAAE,EAAE,KAAK,OAAO,EAAI,YAAY,EAE3EmH,CACT,EA/B0B,cAuCnB,IAAME,GAAeC,EAACC,GAAS,CACpC,IAAMC,EAAKC,GAAUF,EAAK,EAAE,EAC5BG,EAAI,MACF,oBACAH,EAAK,KACLA,EACA,cAAgBA,EAAK,EAAIA,EAAK,MAAQ,EAAI,GAAK,KAAOA,EAAK,MAAQ,EAAI,GACzE,EACA,IAAMI,EAAU,EACVC,EAAOL,EAAK,MAAQ,EAC1B,OAAIA,EAAK,YACPC,EAAG,KACD,YACA,cACGD,EAAK,EAAIK,EAAOL,EAAK,MAAQ,GAC9B,MACCA,EAAK,EAAIA,EAAK,OAAS,EAAII,GAC5B,GACJ,EAEAH,EAAG,KAAK,YAAa,aAAeD,EAAK,EAAI,KAAOA,EAAK,EAAI,GAAG,EAE3DK,CACT,EAvB4B,gBCjpC5B,SAASC,GAAiBC,EAAcC,EAAaC,EAAa,GAAO,CACvE,IAAMC,EAASH,EAEXI,EAAW,WACVD,GAAQ,SAAS,QAAU,GAAK,IACnCC,GAAYD,GAAQ,SAAW,CAAC,GAAG,KAAK,GAAG,GAE7CC,EAAWA,EAAW,mBAGtB,IAAIC,EAAS,EACTC,EAAQ,GACRC,EAEJ,OAAQJ,EAAO,KAAM,CACnB,IAAK,QACHE,EAAS,EACTC,EAAQ,OACR,MACF,IAAK,YACHD,EAAS,EACTC,EAAQ,YACRC,EAAU,EACV,MACF,IAAK,SACHD,EAAQ,OACR,MACF,IAAK,UACHA,EAAQ,WACR,MACF,IAAK,UACHA,EAAQ,UACR,MACF,IAAK,cACHA,EAAQ,cACR,MACF,IAAK,MACHA,EAAQ,sBACR,MACF,IAAK,aACHA,EAAQ,aACR,MACF,IAAK,YACHA,EAAQ,YACR,MACF,IAAK,YACHA,EAAQ,YACR,MACF,IAAK,gBACHA,EAAQ,gBACR,MACF,IAAK,sBACHA,EAAQ,sBACR,MACF,IAAK,SACHA,EAAQ,SACR,MACF,IAAK,UACHA,EAAQ,UACR,MACF,IAAK,UACHA,EAAQ,UACR,MACF,IAAK,aACHA,EAAQ,aACR,MACF,IAAK,WACHA,EAAQ,WACR,MACF,IAAK,QACHA,EAAQ,OACR,MACF,IAAK,eACHA,EAAQ,eACR,MACF,QACEA,EAAQ,MACZ,CAEA,IAAME,EAASC,GAAmBN,GAAQ,QAAU,CAAC,CAAC,EAGhDO,EAAaP,EAAO,MAEpBQ,EAASR,EAAO,MAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,EAAG,EAAG,CAAE,EAqBhE,MAnBa,CACX,WAAYK,EAAO,WACnB,MAAOF,EACP,UAAWI,EACX,GAAIL,EACJ,GAAIA,EACJ,MAAOD,EACP,MAAOI,EAAO,MACd,GAAIL,EAAO,GACX,WAAYA,EAAO,WACnB,MAAOQ,EAAO,MACd,OAAQA,EAAO,OACf,EAAGA,EAAO,EACV,EAAGA,EAAO,EACV,WAAAT,EACA,UAAW,OACX,KAAMC,EAAO,KACb,QAASI,GAAWK,EAAU,GAAG,OAAO,SAAW,CACrD,CAEF,CA1GSC,EAAAd,GAAA,oBA2GT,eAAee,GACbC,EACAf,EACAC,EACA,CACA,IAAMe,EAAOjB,GAAiBC,EAAOC,EAAI,EAAK,EAC9C,GAAIe,EAAK,OAAS,QAChB,OAIF,IAAMC,EAASL,EAAU,EACnBM,EAAS,MAAMC,GAAWJ,EAAMC,EAAM,CAAE,OAAAC,CAAO,CAAC,EAChDG,EAAcF,EAAO,KAAK,EAAE,QAAQ,EACpCG,EAAMpB,EAAG,SAASe,EAAK,EAAE,EAC/BK,EAAI,KAAO,CAAE,MAAOD,EAAY,MAAO,OAAQA,EAAY,OAAQ,EAAG,EAAG,EAAG,EAAG,KAAMF,CAAO,EAC5FjB,EAAG,SAASoB,CAAG,EACfH,EAAO,OAAO,CAChB,CAlBeL,EAAAC,GAAA,sBAqBf,eAAsBQ,GAAsBP,EAAWf,EAAcC,EAAS,CAC5E,IAAMe,EAAOjB,GAAiBC,EAAOC,EAAI,EAAI,EAG7C,GADYA,EAAG,SAASe,EAAK,EAAE,EACvB,OAAS,QAAS,CACxB,IAAMC,EAASL,EAAU,EACzB,MAAMO,GAAWJ,EAAMC,EAAM,CAAE,OAAAC,CAAO,CAAC,EACvCjB,EAAM,UAAYgB,GAAM,UACxBO,GAAaP,CAAI,CACnB,CACF,CAVsBH,EAAAS,GAAA,yBAYtB,eAAsBE,GACpBT,EACAU,EACAxB,EACAyB,EACA,CACA,QAAW1B,KAASyB,EAClB,MAAMC,EAAUX,EAAMf,EAAOC,CAAE,EAC3BD,EAAM,UACR,MAAMwB,GAAkBT,EAAMf,EAAM,SAAUC,EAAIyB,CAAS,CAGjE,CAZsBb,EAAAW,GAAA,qBActB,eAAsBG,GAAoBZ,EAAWU,EAAiBxB,EAAa,CACjF,MAAMuB,GAAkBT,EAAMU,EAAQxB,EAAIa,EAAkB,CAC9D,CAFsBD,EAAAc,GAAA,uBAItB,eAAsBC,GACpBb,EACAU,EACAxB,EACA,CACA,MAAMuB,GAAkBT,EAAMU,EAAQxB,EAAIqB,EAAqB,CACjE,CANsBT,EAAAe,GAAA,gBAQtB,eAAsBC,GACpBd,EACAe,EACAL,EACAxB,EACA8B,EACA,CACA,IAAMC,EAAI,IAAaC,GAAM,CAC3B,WAAY,GACZ,SAAU,EACZ,CAAC,EACDD,EAAE,SAAS,CACT,QAAS,KACT,QAAS,GACT,QAAS,GACT,QAAS,EACT,QAAS,CACX,CAAC,EAED,QAAWhC,KAASyB,EACdzB,EAAM,MACRgC,EAAE,QAAQhC,EAAM,GAAI,CAClB,MAAOA,EAAM,KAAK,MAClB,OAAQA,EAAM,KAAK,OACnB,UAAWA,EAAM,SACnB,CAAC,EAIL,QAAWkC,KAAQJ,EAEjB,GAAII,EAAK,OAASA,EAAK,IAAK,CAC1B,IAAMC,EAAalC,EAAG,SAASiC,EAAK,KAAK,EACnCE,EAAWnC,EAAG,SAASiC,EAAK,GAAG,EAErC,GAAIC,GAAY,MAAQC,GAAU,KAAM,CACtC,IAAMC,EAAQF,EAAW,KACnBG,EAAMF,EAAS,KACfG,EAAS,CACb,CAAE,EAAGF,EAAM,EAAG,EAAGA,EAAM,CAAE,EACzB,CAAE,EAAGA,EAAM,GAAKC,EAAI,EAAID,EAAM,GAAK,EAAG,EAAGA,EAAM,GAAKC,EAAI,EAAID,EAAM,GAAK,CAAE,EACzE,CAAE,EAAGC,EAAI,EAAG,EAAGA,EAAI,CAAE,CACvB,EAEAE,GACEzB,EACA,CAAE,EAAGmB,EAAK,MAAO,EAAGA,EAAK,IAAK,KAAMA,EAAK,EAAG,EAC5C,CACE,GAAGA,EACH,aAAcA,EAAK,aACnB,eAAgBA,EAAK,eACrB,OAAAK,EACA,QAAS,qEACX,EACA,OACA,QACAP,EACAD,CACF,EACIG,EAAK,QACP,MAAMO,GAAgB1B,EAAM,CAC1B,GAAGmB,EACH,MAAOA,EAAK,MACZ,WAAY,+CACZ,aAAcA,EAAK,aACnB,eAAgBA,EAAK,eACrB,OAAAK,EACA,QAAS,qEACX,CAAC,EACDG,GACE,CAAE,GAAGR,EAAM,EAAGK,EAAO,CAAC,EAAE,EAAG,EAAGA,EAAO,CAAC,EAAE,CAAE,EAC1C,CACE,aAAcA,CAChB,CACF,EAEJ,CACF,CAEJ,CA/EsB1B,EAAAgB,GAAA,eCpKf,IAAMc,GAAaC,EAAA,SAAUC,EAAWC,EAAc,CAC3D,OAAOA,EAAQ,GAAG,WAAW,CAC/B,EAF0B,cAIbC,GAAOH,EAAA,eAClBC,EACAG,EACAC,EACAH,EACe,CACf,GAAM,CAAE,cAAAI,EAAe,MAAOC,CAAK,EAAcC,EAAU,EACrDC,EAAKP,EAAQ,GACfQ,EACAJ,IAAkB,YACpBI,EAAiBC,EAAS,KAAOP,CAAE,GAErC,IAAMQ,EACJN,IAAkB,UACdK,EAAmCD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACjFC,EAAmC,MAAM,EAEzCE,EACJP,IAAkB,UACdM,EAAK,OAAsB,QAAQR,CAAE,IAAI,EACzCO,EAAiC,QAAQP,CAAE,IAAI,EAMrDU,GAAcD,EAHE,CAAC,QAAS,SAAU,OAAO,EAGfX,EAAQ,KAAME,CAAE,EAE5C,IAAMW,EAAKN,EAAG,UAAU,EAClBO,EAAQP,EAAG,cAAc,EACzBQ,EAAQR,EAAG,SAAS,EAEpBS,EAAQL,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EACnD,MAAMM,GAAoBD,EAAOH,EAAIN,CAAE,EACvC,IAAMW,EAASC,GAAOZ,CAAE,EAMxB,GALA,MAAMa,GAAaJ,EAAOH,EAAIN,CAAE,EAChC,MAAMc,GAAYL,EAAOD,EAAOD,EAAOP,EAAIL,CAAE,EAIzCgB,EAAQ,CACV,IAAMI,EAAUJ,EACVK,EAAc,KAAK,IAAI,EAAG,KAAK,MAAM,MAASD,EAAQ,MAAQA,EAAQ,OAAO,CAAC,EAC9EE,EAASF,EAAQ,OAASC,EAAc,GACxCE,EAAQH,EAAQ,MAAQ,GACxB,CAAE,YAAAI,CAAY,EAAIrB,EACxBsB,GAAiBhB,EAAKa,EAAQC,EAAO,CAAC,CAACC,CAAW,EAClDE,EAAI,MAAM,cAAeV,EAAQI,CAAO,EACxCX,EAAI,KACF,UACA,GAAGW,EAAQ,EAAI,CAAC,IAAIA,EAAQ,EAAI,CAAC,IAAIA,EAAQ,MAAQ,EAAE,IAAIA,EAAQ,OAAS,EAAE,EAChF,CACF,CACF,EArDoB,QAuDbO,GAAQ,CACb,KAAA5B,GACA,WAAAJ,EACF,ECjEO,IAAMiC,GAA6B,CACxC,OAAAC,GACA,GAAAC,GACA,SAAAC,GACA,OAAQC,EACV", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "num", "spaceId", "edgeData", "id2", "id", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "block_default", "parser", "blockDatabase", "edgeList", "edgeCount", "COLOR_KEYWORD", "FILL_KEYWORD", "BG_FILL", "STYLECLASS_SEP", "config", "getConfig", "classes", "sanitizeText", "__name", "txt", "common_default", "addStyleClass", "id", "styleAttributes", "foundClass", "attrib", "fixedAttrib", "newStyle2", "addStyle2Node", "styles", "found<PERSON>lock", "setCssClass", "itemIds", "cssClassName", "trimmedId", "populateBlockDatabase", "_blockList", "parent", "blockList", "children", "block", "count", "existingBlock", "w", "j", "newBlock", "clone_default", "blocks", "rootBlock", "clear", "log", "typeStr2Type", "typeStr", "edgeTypeStr2Type", "edgeStrToEdgeData", "cnt", "generateId", "setHierarchy", "getColumns", "blockId", "getBlocksFlat", "getBlocks", "get<PERSON>dges", "getBlock", "setBlock", "<PERSON><PERSON><PERSON><PERSON>", "getClasses", "db", "blockDB_default", "fade", "__name", "color", "opacity", "channel", "channel_default", "r", "g", "b", "rgba_default", "getStyles", "options", "getIconStyles", "styles_default", "insertMarkers", "__name", "elem", "markerArray", "type", "id", "markerName", "markers", "extension", "log", "composition", "aggregation", "dependency", "lollipop", "point", "circle", "cross", "barb", "markers_default", "padding", "getConfig", "calculateBlockPosition", "columns", "position", "px", "py", "__name", "getMaxChildSize", "block", "max<PERSON><PERSON><PERSON>", "maxHeight", "child", "width", "height", "x", "y", "log", "setBlockSizes", "db", "<PERSON><PERSON><PERSON><PERSON>", "siblingHeight", "childSize", "numItems", "xSize", "ySize", "<PERSON><PERSON><PERSON><PERSON>", "childHeight", "num", "layoutBlocks", "widthOfChildren", "columnPos", "startingPosX", "rowPos", "parent", "halfWidth", "findBounds", "minX", "minY", "maxX", "maxY", "layout", "root", "applyStyle", "dom", "styleFn", "__name", "addHtmlLabel", "node", "fo", "select_default", "div", "label", "labelClass", "span", "createLabel", "_vertexText", "style", "isTitle", "isNode", "vertexText", "evaluate", "getConfig", "log", "replaceIconSubstring", "decodeEntities", "svgLabel", "rows", "row", "tspan", "createLabel_default", "addEdgeMarkers", "__name", "svgPath", "edge", "url", "id", "diagramType", "addEdgeMarker", "arrowTypesMap", "position", "arrowType", "endMarkerType", "log", "suffix", "edgeLabels", "terminalLabels", "insertEdgeLabel", "__name", "elem", "edge", "config", "getConfig", "useHtmlLabels", "evaluate", "labelElement", "createText", "createLabel_default", "edgeLabel", "label", "bbox", "div", "dv", "select_default", "edgeLabels", "fo", "startLabelElement", "startEdgeLabelLeft", "inner", "slBox", "terminalLabels", "setTerminalWidth", "startEdgeLabelRight", "endLabelElement", "endEdgeLabelLeft", "endEdgeLabelRight", "value", "positionEdgeLabel", "paths", "log", "path", "siteConfig", "subGraphTitleTotalMargin", "getSubGraphTitleMargins", "el", "x", "y", "pos", "utils_default", "outsideNode", "node", "point", "dx", "dy", "w", "h", "intersection", "outsidePoint", "insidePoint", "r", "Q", "R", "q", "res", "_x", "_y", "cutPathAtIntersect", "_points", "boundaryNode", "points", "lastPointOutside", "isInside", "inter", "pointPresent", "p", "e", "insertEdge", "clusterDb", "diagramType", "graph", "id", "pointsHas<PERSON><PERSON>ed", "tail", "head", "lineData", "curve", "basis_default", "getLineFunctionsWithOffset", "lineFunction", "line_default", "strokeClasses", "svgPath", "url", "getUrl", "addEdgeMarkers", "expandAndDeduplicateDirections", "__name", "directions", "uniqueDirections", "direction", "getArrowPoints", "duplicatedDirections", "bbox", "node", "f", "height", "midpoint", "width", "padding", "intersectNode", "node", "point", "__name", "intersect_node_default", "intersectEllipse", "node", "rx", "ry", "point", "cx", "cy", "px", "py", "det", "dx", "dy", "__name", "intersect_ellipse_default", "intersectCircle", "node", "rx", "point", "intersect_ellipse_default", "__name", "intersect_circle_default", "intersectLine", "p1", "p2", "q1", "q2", "a1", "a2", "b1", "b2", "c1", "c2", "r1", "r2", "r3", "r4", "denom", "offset", "num", "x", "y", "sameSign", "__name", "intersect_line_default", "intersect_polygon_default", "intersectPolygon", "node", "polyPoints", "point", "x1", "y1", "intersections", "minX", "minY", "entry", "left", "top", "i", "p1", "p2", "intersect", "intersect_line_default", "p", "q", "pdx", "pdy", "distp", "qdx", "qdy", "distq", "__name", "intersectRect", "__name", "node", "point", "x", "y", "dx", "dy", "w", "h", "sx", "sy", "intersect_rect_default", "intersect_default", "intersect_node_default", "intersect_circle_default", "intersect_ellipse_default", "intersect_polygon_default", "intersect_rect_default", "labelHelper", "__name", "parent", "node", "_classes", "isNode", "config", "getConfig", "classes", "useHtmlLabels", "evaluate", "shapeSvg", "label", "labelText", "textNode", "text", "createText", "sanitizeText", "decodeEntities", "createLabel_default", "bbox", "halfPadding", "div", "dv", "select_default", "images", "noImgText", "img", "res", "setupImage", "bodyFontSize", "width", "updateNodeBounds", "element", "insertPolygonShape", "w", "h", "points", "d", "note", "__name", "parent", "node", "getConfig", "shapeSvg", "bbox", "halfPadding", "labelHelper", "log", "rect", "updateNodeBounds", "point", "intersect_default", "note_default", "formatClass", "__name", "str", "getClassesFromNode", "node", "otherClasses", "question", "parent", "shapeSvg", "bbox", "labelHelper", "w", "h", "s", "points", "log", "questionElem", "insertPolygonShape", "updateNodeBounds", "point", "intersect_default", "choice", "d", "hexagon", "f", "m", "hex", "block_arrow", "getArrowPoints", "blockArrow", "rect_left_inv_arrow", "lean_right", "el", "lean_left", "trapezoid", "inv_trapezoid", "rect_right_inv_arrow", "cylinder", "rx", "ry", "shape", "pos", "x", "y", "rect", "halfPadding", "totalWidth", "totalHeight", "propKeys", "applyNodePropertyBorders", "<PERSON><PERSON><PERSON>", "composite", "labelRect", "borders", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addBorder", "length", "skip<PERSON><PERSON><PERSON>", "rectWithTitle", "classes", "innerLine", "label", "text2", "title", "text", "createLabel_default", "evaluate", "getConfig", "div", "dv", "select_default", "textRows", "titleBox", "descr", "stadium", "circle", "doublecircle", "gap", "circleGroup", "outerCircle", "innerCircle", "subroutine", "start", "fork<PERSON><PERSON>n", "dir", "width", "height", "end", "class_box", "rowPadding", "lineHeight", "topLine", "bottomLine", "max<PERSON><PERSON><PERSON>", "maxHeight", "labelContainer", "verticalPos", "hasInterface", "interfaceLabelText", "interfaceLabel", "interfaceBBox", "classTitleString", "classTitleLabel", "classTitleBBox", "classAttributes", "member", "parsedInfo", "parsedText", "lbl", "classMethods", "displayText", "diffX", "memberBBox", "shapes", "note_default", "nodeElems", "insertNode", "elem", "renderOptions", "newEl", "target", "positionNode", "__name", "node", "el", "nodeElems", "log", "padding", "diff", "getNodeFromBlock", "block", "db", "positioned", "vertex", "classStr", "radius", "shape", "padding", "styles", "getStylesFromArray", "vertexText", "bounds", "getConfig", "__name", "calculateBlockSize", "elem", "node", "config", "nodeEl", "insertNode", "boundingBox", "obj", "insertBlockPositioned", "positionNode", "performOperations", "blocks", "operation", "calculateBlockSizes", "insertBlocks", "insertEdges", "edges", "id", "g", "Graph", "edge", "startBlock", "endBlock", "start", "end", "points", "insertEdge", "insertEdgeLabel", "positionEdgeLabel", "getClasses", "__name", "text", "diagObj", "draw", "id", "_version", "securityLevel", "conf", "getConfig", "db", "sandboxElement", "select_default", "root", "svg", "markers_default", "bl", "blArr", "edges", "nodes", "calculateBlockSizes", "bounds", "layout", "insertBlocks", "insertEdges", "bounds2", "magicFactor", "height", "width", "useMaxWidth", "configureSvgSize", "log", "blockRenderer_default", "diagram", "block_default", "blockDB_default", "blockRenderer_default", "styles_default"]}