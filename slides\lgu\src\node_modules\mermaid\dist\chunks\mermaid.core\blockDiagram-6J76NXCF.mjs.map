{"version": 3, "sources": ["../../../src/diagrams/block/parser/block.jison", "../../../src/diagrams/block/blockDB.ts", "../../../src/diagrams/block/styles.ts", "../../../src/diagrams/block/blockRenderer.ts", "../../../src/dagre-wrapper/markers.js", "../../../src/diagrams/block/layout.ts", "../../../src/diagrams/block/renderHelpers.ts", "../../../src/dagre-wrapper/createLabel.js", "../../../src/dagre-wrapper/edges.js", "../../../src/dagre-wrapper/edgeMarker.ts", "../../../src/dagre-wrapper/nodes.js", "../../../src/dagre-wrapper/blockArrowHelper.ts", "../../../src/dagre-wrapper/intersect/intersect-node.js", "../../../src/dagre-wrapper/intersect/intersect-ellipse.js", "../../../src/dagre-wrapper/intersect/intersect-circle.js", "../../../src/dagre-wrapper/intersect/intersect-line.js", "../../../src/dagre-wrapper/intersect/intersect-polygon.js", "../../../src/dagre-wrapper/intersect/intersect-rect.js", "../../../src/dagre-wrapper/intersect/index.js", "../../../src/dagre-wrapper/shapes/util.js", "../../../src/dagre-wrapper/shapes/note.js", "../../../src/diagrams/block/blockDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,7],$V1=[1,13],$V2=[1,14],$V3=[1,15],$V4=[1,19],$V5=[1,16],$V6=[1,17],$V7=[1,18],$V8=[8,30],$V9=[8,21,28,29,30,31,32,40,44,47],$Va=[1,23],$Vb=[1,24],$Vc=[8,15,16,21,28,29,30,31,32,40,44,47],$Vd=[8,15,16,21,27,28,29,30,31,32,40,44,47],$Ve=[1,49];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"spaceLines\":3,\"SPACELINE\":4,\"NL\":5,\"separator\":6,\"SPACE\":7,\"EOF\":8,\"start\":9,\"BLOCK_DIAGRAM_KEY\":10,\"document\":11,\"stop\":12,\"statement\":13,\"link\":14,\"LINK\":15,\"START_LINK\":16,\"LINK_LABEL\":17,\"STR\":18,\"nodeStatement\":19,\"columnsStatement\":20,\"SPACE_BLOCK\":21,\"blockStatement\":22,\"classDefStatement\":23,\"cssClassStatement\":24,\"styleStatement\":25,\"node\":26,\"SIZE\":27,\"COLUMNS\":28,\"id-block\":29,\"end\":30,\"block\":31,\"NODE_ID\":32,\"nodeShapeNLabel\":33,\"dirList\":34,\"DIR\":35,\"NODE_DSTART\":36,\"NODE_DEND\":37,\"BLOCK_ARROW_START\":38,\"BLOCK_ARROW_END\":39,\"classDef\":40,\"CLASSDEF_ID\":41,\"CLASSDEF_STYLEOPTS\":42,\"DEFAULT\":43,\"class\":44,\"CLASSENTITY_IDS\":45,\"STYLECLASS\":46,\"style\":47,\"STYLE_ENTITY_IDS\":48,\"STYLE_DEFINITION_DATA\":49,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SPACELINE\",5:\"NL\",7:\"SPACE\",8:\"EOF\",10:\"BLOCK_DIAGRAM_KEY\",15:\"LINK\",16:\"START_LINK\",17:\"LINK_LABEL\",18:\"STR\",21:\"SPACE_BLOCK\",27:\"SIZE\",28:\"COLUMNS\",29:\"id-block\",30:\"end\",31:\"block\",32:\"NODE_ID\",35:\"DIR\",36:\"NODE_DSTART\",37:\"NODE_DEND\",38:\"BLOCK_ARROW_START\",39:\"BLOCK_ARROW_END\",40:\"classDef\",41:\"CLASSDEF_ID\",42:\"CLASSDEF_STYLEOPTS\",43:\"DEFAULT\",44:\"class\",45:\"CLASSENTITY_IDS\",46:\"STYLECLASS\",47:\"style\",48:\"STYLE_ENTITY_IDS\",49:\"STYLE_DEFINITION_DATA\"},\nproductions_: [0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 4:\nyy.getLogger().debug('Rule: separator (NL) ');\nbreak;\ncase 5:\nyy.getLogger().debug('Rule: separator (Space) ');\nbreak;\ncase 6:\nyy.getLogger().debug('Rule: separator (EOF) ');\nbreak;\ncase 7:\n yy.getLogger().debug(\"Rule: hierarchy: \", $$[$0-1]); yy.setHierarchy($$[$0-1]); \nbreak;\ncase 8:\nyy.getLogger().debug('Stop NL ');\nbreak;\ncase 9:\nyy.getLogger().debug('Stop EOF ');\nbreak;\ncase 10:\nyy.getLogger().debug('Stop NL2 ');\nbreak;\ncase 11:\nyy.getLogger().debug('Stop EOF2 ');\nbreak;\ncase 12:\n yy.getLogger().debug(\"Rule: statement: \", $$[$0]); typeof $$[$0].length === 'number'?this.$ = $$[$0]:this.$ = [$$[$0]]; \nbreak;\ncase 13:\n yy.getLogger().debug(\"Rule: statement #2: \", $$[$0-1]); this.$ = [$$[$0-1]].concat($$[$0]); \nbreak;\ncase 14:\n yy.getLogger().debug(\"Rule: link: \", $$[$0], yytext); this.$={edgeTypeStr: $$[$0], label:''}; \nbreak;\ncase 15:\n yy.getLogger().debug(\"Rule: LABEL link: \", $$[$0-3], $$[$0-1], $$[$0]); this.$={edgeTypeStr: $$[$0], label:$$[$0-1]}; \nbreak;\ncase 18:\n const num=parseInt($$[$0]); const spaceId = yy.generateId(); this.$ = { id: spaceId, type:'space', label:'', width: num, children: [] }\nbreak;\ncase 23:\n\n    yy.getLogger().debug('Rule: (nodeStatement link node) ', $$[$0-2], $$[$0-1], $$[$0], ' typestr: ',$$[$0-1].edgeTypeStr);\n    const edgeData = yy.edgeStrToEdgeData($$[$0-1].edgeTypeStr)\n    this.$ = [\n      {id: $$[$0-2].id, label: $$[$0-2].label, type:$$[$0-2].type, directions: $$[$0-2].directions},\n      {id: $$[$0-2].id + '-' + $$[$0].id, start: $$[$0-2].id, end: $$[$0].id, label: $$[$0-1].label, type: 'edge', directions: $$[$0].directions, arrowTypeEnd: edgeData, arrowTypeStart: 'arrow_open' },\n      {id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions}\n      ];\n    \nbreak;\ncase 24:\n yy.getLogger().debug('Rule: nodeStatement (abc88 node size) ', $$[$0-1], $$[$0]); this.$ = {id: $$[$0-1].id, label: $$[$0-1].label, type: yy.typeStr2Type($$[$0-1].typeStr), directions: $$[$0-1].directions, widthInColumns: parseInt($$[$0],10)}; \nbreak;\ncase 25:\n yy.getLogger().debug('Rule: nodeStatement (node) ', $$[$0]); this.$ = {id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions, widthInColumns:1}; \nbreak;\ncase 26:\n yy.getLogger().debug('APA123', this? this:'na'); yy.getLogger().debug(\"COLUMNS: \", $$[$0]); this.$ = {type: 'column-setting', columns: $$[$0] === 'auto'?-1:parseInt($$[$0]) } \nbreak;\ncase 27:\n yy.getLogger().debug('Rule: id-block statement : ', $$[$0-2], $$[$0-1]); const id2 = yy.generateId(); this.$ = { ...$$[$0-2], type:'composite', children: $$[$0-1] }; \nbreak;\ncase 28:\n yy.getLogger().debug('Rule: blockStatement : ', $$[$0-2], $$[$0-1], $$[$0]); const id = yy.generateId(); this.$ = { id, type:'composite', label:'', children: $$[$0-1] }; \nbreak;\ncase 29:\n yy.getLogger().debug(\"Rule: node (NODE_ID separator): \", $$[$0]); this.$ = { id: $$[$0] }; \nbreak;\ncase 30:\n\n    yy.getLogger().debug(\"Rule: node (NODE_ID nodeShapeNLabel separator): \", $$[$0-1], $$[$0]);\n    this.$ = { id: $$[$0-1], label: $$[$0].label, typeStr: $$[$0].typeStr, directions: $$[$0].directions };\n  \nbreak;\ncase 31:\n yy.getLogger().debug(\"Rule: dirList: \", $$[$0]); this.$ = [$$[$0]]; \nbreak;\ncase 32:\n yy.getLogger().debug(\"Rule: dirList: \", $$[$0-1], $$[$0]); this.$ = [$$[$0-1]].concat($$[$0]); \nbreak;\ncase 33:\n yy.getLogger().debug(\"Rule: nodeShapeNLabel: \", $$[$0-2], $$[$0-1], $$[$0]); this.$ = { typeStr: $$[$0-2] + $$[$0], label: $$[$0-1] }; \nbreak;\ncase 34:\n yy.getLogger().debug(\"Rule: BLOCK_ARROW nodeShapeNLabel: \", $$[$0-3], $$[$0-2], \" #3:\",$$[$0-1], $$[$0]); this.$ = { typeStr: $$[$0-3] + $$[$0], label: $$[$0-2], directions: $$[$0-1]}; \nbreak;\ncase 35: case 36:\n\n      this.$ = { type: 'classDef', id: $$[$0-1].trim(), css: $$[$0].trim() };\n      \nbreak;\ncase 37:\n\n        //log.debug('apply class: id(s): ',$$[$0-1], '  style class: ', $$[$0]);\n        this.$={ type: 'applyClass', id: $$[$0-1].trim(), styleClass: $$[$0].trim() };\n        \nbreak;\ncase 38:\n\n        this.$={ type: 'applyStyles', id: $$[$0-1].trim(), stylesStr: $$[$0].trim() };\n        \nbreak;\n}\n},\ntable: [{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:$V0,22:8,23:9,24:10,25:11,26:12,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7},{8:[1,20]},o($V8,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:$V0,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7}),o($V9,[2,16],{14:22,15:$Va,16:$Vb}),o($V9,[2,17]),o($V9,[2,18]),o($V9,[2,19]),o($V9,[2,20]),o($V9,[2,21]),o($V9,[2,22]),o($Vc,[2,25],{27:[1,25]}),o($V9,[2,26]),{19:26,26:12,32:$V4},{11:27,13:4,19:5,20:6,21:$V0,22:8,23:9,24:10,25:11,26:12,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},o($Vd,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},o($V8,[2,13]),{26:35,32:$V4},{32:[2,14]},{17:[1,36]},o($Vc,[2,24]),{11:37,13:4,14:22,15:$Va,16:$Vb,19:5,20:6,21:$V0,22:8,23:9,24:10,25:11,26:12,28:$V1,29:$V2,31:$V3,32:$V4,40:$V5,44:$V6,47:$V7},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},o($Vd,[2,30]),{18:[1,43]},{18:[1,44]},o($Vc,[2,23]),{18:[1,45]},{30:[1,46]},o($V9,[2,28]),o($V9,[2,35]),o($V9,[2,36]),o($V9,[2,37]),o($V9,[2,38]),{37:[1,47]},{34:48,35:$Ve},{15:[1,50]},o($V9,[2,27]),o($Vd,[2,33]),{39:[1,51]},{34:52,35:$Ve,39:[2,31]},{32:[2,15]},o($Vd,[2,34]),{39:[2,32]}],\ndefaultActions: {20:[2,7],23:[2,14],50:[2,15],52:[2,32]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0: return 10; \nbreak;\ncase 1: yy.getLogger().debug('Found space-block'); return 31;\nbreak;\ncase 2: yy.getLogger().debug('Found nl-block'); return 31;\nbreak;\ncase 3: yy.getLogger().debug('Found space-block'); return 29;\nbreak;\ncase 4: yy.getLogger().debug('.', yy_.yytext); /* skip all whitespace */  \nbreak;\ncase 5:yy.getLogger().debug('_', yy_.yytext);                 /* skip all whitespace */   \nbreak;\ncase 6: return 5 \nbreak;\ncase 7: yy_.yytext=-1; return 28; \nbreak;\ncase 8: yy_.yytext = yy_.yytext.replace(/columns\\s+/,''); yy.getLogger().debug('COLUMNS (LEX)', yy_.yytext); return 28; \nbreak;\ncase 9: this.pushState(\"md_string\");\nbreak;\ncase 10: return \"MD_STR\";\nbreak;\ncase 11: this.popState();\nbreak;\ncase 12:this.pushState(\"string\");\nbreak;\ncase 13: yy.getLogger().debug('LEX: POPPING STR:', yy_.yytext);this.popState();\nbreak;\ncase 14: yy.getLogger().debug('LEX: STR end:', yy_.yytext); return \"STR\";\nbreak;\ncase 15:  yy_.yytext = yy_.yytext.replace(/space\\:/,'');yy.getLogger().debug('SPACE NUM (LEX)', yy_.yytext); return 21; \nbreak;\ncase 16: yy_.yytext = '1'; yy.getLogger().debug('COLUMNS (LEX)', yy_.yytext); return 21; \nbreak;\ncase 17:return 43;\nbreak;\ncase 18:return 'LINKSTYLE';\nbreak;\ncase 19:return 'INTERPOLATE';\nbreak;\ncase 20: this.pushState('CLASSDEF'); return 40; \nbreak;\ncase 21: this.popState(); this.pushState('CLASSDEFID'); return 'DEFAULT_CLASSDEF_ID' \nbreak;\ncase 22: this.popState(); this.pushState('CLASSDEFID'); return 41 \nbreak;\ncase 23: this.popState(); return 42 \nbreak;\ncase 24: this.pushState('CLASS'); return 44; \nbreak;\ncase 25: this.popState(); this.pushState('CLASS_STYLE'); return 45 \nbreak;\ncase 26: this.popState(); return 46 \nbreak;\ncase 27: this.pushState('STYLE_STMNT'); return 47; \nbreak;\ncase 28: this.popState(); this.pushState('STYLE_DEFINITION'); return 48 \nbreak;\ncase 29: this.popState(); return 49 \nbreak;\ncase 30: this.pushState(\"acc_title\");return 'acc_title'; \nbreak;\ncase 31: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 32: this.pushState(\"acc_descr\");return 'acc_descr'; \nbreak;\ncase 33: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 34: this.pushState(\"acc_descr_multiline\");\nbreak;\ncase 35: this.popState(); \nbreak;\ncase 36:return \"acc_descr_multiline_value\";\nbreak;\ncase 37:return 30;\nbreak;\ncase 38: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 39: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 40: this.popState();yy.getLogger().debug('Lex: ))'); return \"NODE_DEND\"; \nbreak;\ncase 41: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 42: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 43: this.popState();yy.getLogger().debug('Lex: (-'); return \"NODE_DEND\"; \nbreak;\ncase 44: this.popState();yy.getLogger().debug('Lex: -)'); return \"NODE_DEND\"; \nbreak;\ncase 45: this.popState();yy.getLogger().debug('Lex: (('); return \"NODE_DEND\"; \nbreak;\ncase 46: this.popState();yy.getLogger().debug('Lex: ]]'); return \"NODE_DEND\"; \nbreak;\ncase 47: this.popState();yy.getLogger().debug('Lex: (');  return \"NODE_DEND\";  \nbreak;\ncase 48: this.popState();yy.getLogger().debug('Lex: ])'); return \"NODE_DEND\"; \nbreak;\ncase 49: this.popState();yy.getLogger().debug('Lex: /]'); return \"NODE_DEND\"; \nbreak;\ncase 50: this.popState();yy.getLogger().debug('Lex: /]'); return \"NODE_DEND\"; \nbreak;\ncase 51: this.popState();yy.getLogger().debug('Lex: )]'); return \"NODE_DEND\"; \nbreak;\ncase 52: this.popState();yy.getLogger().debug('Lex: )');  return \"NODE_DEND\"; \nbreak;\ncase 53: this.popState();yy.getLogger().debug('Lex: ]>'); return \"NODE_DEND\"; \nbreak;\ncase 54: this.popState();yy.getLogger().debug('Lex: ]'); return \"NODE_DEND\"; \nbreak;\ncase 55: yy.getLogger().debug('Lexa: -)'); this.pushState('NODE');return 36; \nbreak;\ncase 56: yy.getLogger().debug('Lexa: (-'); this.pushState('NODE');return 36; \nbreak;\ncase 57: yy.getLogger().debug('Lexa: ))'); this.pushState('NODE');return 36;  \nbreak;\ncase 58: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36;      \nbreak;\ncase 59: yy.getLogger().debug('Lex: (((');  this.pushState('NODE');return 36; \nbreak;\ncase 60: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 61: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 62: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 63: yy.getLogger().debug('Lexc: >'); this.pushState('NODE');return 36; \nbreak;\ncase 64: yy.getLogger().debug('Lexa: (['); this.pushState('NODE');return 36; \nbreak;\ncase 65: yy.getLogger().debug('Lexa: )'); this.pushState('NODE');return 36; \nbreak;\ncase 66: this.pushState('NODE');return 36; \nbreak;\ncase 67: this.pushState('NODE');return 36; \nbreak;\ncase 68: this.pushState('NODE');return 36; \nbreak;\ncase 69: this.pushState('NODE');return 36; \nbreak;\ncase 70: this.pushState('NODE');return 36; \nbreak;\ncase 71: this.pushState('NODE');return 36; \nbreak;\ncase 72: this.pushState('NODE');return 36; \nbreak;\ncase 73: yy.getLogger().debug('Lexa: ['); this.pushState('NODE');return 36; \nbreak;\ncase 74: this.pushState('BLOCK_ARROW');yy.getLogger().debug('LEX ARR START');return 38; \nbreak;\ncase 75: yy.getLogger().debug('Lex: NODE_ID', yy_.yytext);return 32; \nbreak;\ncase 76: yy.getLogger().debug('Lex: EOF', yy_.yytext);return 8; \nbreak;\ncase 77: this.pushState(\"md_string\");\nbreak;\ncase 78: this.pushState(\"md_string\");\nbreak;\ncase 79: return \"NODE_DESCR\";\nbreak;\ncase 80: this.popState();\nbreak;\ncase 81: yy.getLogger().debug('Lex: Starting string');this.pushState(\"string\");\nbreak;\ncase 82: yy.getLogger().debug('LEX ARR: Starting string');this.pushState(\"string\");\nbreak;\ncase 83: yy.getLogger().debug('LEX: NODE_DESCR:', yy_.yytext); return \"NODE_DESCR\";\nbreak;\ncase 84:yy.getLogger().debug('LEX POPPING');this.popState();\nbreak;\ncase 85: yy.getLogger().debug('Lex: =>BAE');  this.pushState('ARROW_DIR');  \nbreak;\ncase 86: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (right): dir:',yy_.yytext);return \"DIR\"; \nbreak;\ncase 87: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (left):',yy_.yytext);return \"DIR\"; \nbreak;\ncase 88: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (x):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 89: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (y):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 90: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (up):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 91: yy_.yytext = yy_.yytext.replace(/^,\\s*/, ''); yy.getLogger().debug('Lex (down):',yy_.yytext); return \"DIR\"; \nbreak;\ncase 92: yy_.yytext=']>';yy.getLogger().debug('Lex (ARROW_DIR end):',yy_.yytext);this.popState();this.popState();return \"BLOCK_ARROW_END\"; \nbreak;\ncase 93: yy.getLogger().debug('Lex: LINK', '#'+yy_.yytext+'#'); return 15; \nbreak;\ncase 94: yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 95: yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 96: yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 97: yy.getLogger().debug('Lex: START_LINK', yy_.yytext);this.pushState(\"LLABEL\");return 16; \nbreak;\ncase 98: yy.getLogger().debug('Lex: START_LINK', yy_.yytext);this.pushState(\"LLABEL\");return 16; \nbreak;\ncase 99: yy.getLogger().debug('Lex: START_LINK', yy_.yytext);this.pushState(\"LLABEL\");return 16; \nbreak;\ncase 100: this.pushState(\"md_string\");\nbreak;\ncase 101: yy.getLogger().debug('Lex: Starting string');this.pushState(\"string\"); return \"LINK_LABEL\";\nbreak;\ncase 102: this.popState(); yy.getLogger().debug('Lex: LINK', '#'+yy_.yytext+'#'); return 15; \nbreak;\ncase 103: this.popState(); yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 104: this.popState(); yy.getLogger().debug('Lex: LINK', yy_.yytext); return 15; \nbreak;\ncase 105: yy.getLogger().debug('Lex: COLON', yy_.yytext); yy_.yytext=yy_.yytext.slice(1);return 27; \nbreak;\n}\n},\nrules: [/^(?:block-beta\\b)/,/^(?:block\\s+)/,/^(?:block\\n+)/,/^(?:block:)/,/^(?:[\\s]+)/,/^(?:[\\n]+)/,/^(?:((\\u000D\\u000A)|(\\u000A)))/,/^(?:columns\\s+auto\\b)/,/^(?:columns\\s+[\\d]+)/,/^(?:[\"][`])/,/^(?:[^`\"]+)/,/^(?:[`][\"])/,/^(?:[\"])/,/^(?:[\"])/,/^(?:[^\"]*)/,/^(?:space[:]\\d+)/,/^(?:space\\b)/,/^(?:default\\b)/,/^(?:linkStyle\\b)/,/^(?:interpolate\\b)/,/^(?:classDef\\s+)/,/^(?:DEFAULT\\s+)/,/^(?:\\w+\\s+)/,/^(?:[^\\n]*)/,/^(?:class\\s+)/,/^(?:(\\w+)+((,\\s*\\w+)*))/,/^(?:[^\\n]*)/,/^(?:style\\s+)/,/^(?:(\\w+)+((,\\s*\\w+)*))/,/^(?:[^\\n]*)/,/^(?:accTitle\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*\\{\\s*)/,/^(?:[\\}])/,/^(?:[^\\}]*)/,/^(?:end\\b\\s*)/,/^(?:\\(\\(\\()/,/^(?:\\)\\)\\))/,/^(?:[\\)]\\))/,/^(?:\\}\\})/,/^(?:\\})/,/^(?:\\(-)/,/^(?:-\\))/,/^(?:\\(\\()/,/^(?:\\]\\])/,/^(?:\\()/,/^(?:\\]\\))/,/^(?:\\\\\\])/,/^(?:\\/\\])/,/^(?:\\)\\])/,/^(?:[\\)])/,/^(?:\\]>)/,/^(?:[\\]])/,/^(?:-\\))/,/^(?:\\(-)/,/^(?:\\)\\))/,/^(?:\\))/,/^(?:\\(\\(\\()/,/^(?:\\(\\()/,/^(?:\\{\\{)/,/^(?:\\{)/,/^(?:>)/,/^(?:\\(\\[)/,/^(?:\\()/,/^(?:\\[\\[)/,/^(?:\\[\\|)/,/^(?:\\[\\()/,/^(?:\\)\\)\\))/,/^(?:\\[\\\\)/,/^(?:\\[\\/)/,/^(?:\\[\\\\)/,/^(?:\\[)/,/^(?:<\\[)/,/^(?:[^\\(\\[\\n\\-\\)\\{\\}\\s\\<\\>:]+)/,/^(?:$)/,/^(?:[\"][`])/,/^(?:[\"][`])/,/^(?:[^`\"]+)/,/^(?:[`][\"])/,/^(?:[\"])/,/^(?:[\"])/,/^(?:[^\"]+)/,/^(?:[\"])/,/^(?:\\]>\\s*\\()/,/^(?:,?\\s*right\\s*)/,/^(?:,?\\s*left\\s*)/,/^(?:,?\\s*x\\s*)/,/^(?:,?\\s*y\\s*)/,/^(?:,?\\s*up\\s*)/,/^(?:,?\\s*down\\s*)/,/^(?:\\)\\s*)/,/^(?:\\s*[xo<]?--+[-xo>]\\s*)/,/^(?:\\s*[xo<]?==+[=xo>]\\s*)/,/^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/,/^(?:\\s*~~[\\~]+\\s*)/,/^(?:\\s*[xo<]?--\\s*)/,/^(?:\\s*[xo<]?==\\s*)/,/^(?:\\s*[xo<]?-\\.\\s*)/,/^(?:[\"][`])/,/^(?:[\"])/,/^(?:\\s*[xo<]?--+[-xo>]\\s*)/,/^(?:\\s*[xo<]?==+[=xo>]\\s*)/,/^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/,/^(?::\\d+)/],\nconditions: {\"STYLE_DEFINITION\":{\"rules\":[29],\"inclusive\":false},\"STYLE_STMNT\":{\"rules\":[28],\"inclusive\":false},\"CLASSDEFID\":{\"rules\":[23],\"inclusive\":false},\"CLASSDEF\":{\"rules\":[21,22],\"inclusive\":false},\"CLASS_STYLE\":{\"rules\":[26],\"inclusive\":false},\"CLASS\":{\"rules\":[25],\"inclusive\":false},\"LLABEL\":{\"rules\":[100,101,102,103,104],\"inclusive\":false},\"ARROW_DIR\":{\"rules\":[86,87,88,89,90,91,92],\"inclusive\":false},\"BLOCK_ARROW\":{\"rules\":[77,82,85],\"inclusive\":false},\"NODE\":{\"rules\":[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],\"inclusive\":false},\"md_string\":{\"rules\":[10,11,79,80],\"inclusive\":false},\"space\":{\"rules\":[],\"inclusive\":false},\"string\":{\"rules\":[13,14,83,84],\"inclusive\":false},\"acc_descr_multiline\":{\"rules\":[35,36],\"inclusive\":false},\"acc_descr\":{\"rules\":[33],\"inclusive\":false},\"acc_title\":{\"rules\":[31],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import clone from 'lodash-es/clone.js';\nimport * as configApi from '../../config.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DiagramDB } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport common from '../common/common.js';\nimport { clear as commonClear } from '../common/commonDb.js';\nimport type { Block, ClassDef } from './blockTypes.js';\n\n// Initialize the node database for simple lookups\nlet blockDatabase = new Map<string, Block>();\nlet edgeList: Block[] = [];\nlet edgeCount = new Map<string, number>();\n\nconst COLOR_KEYWORD = 'color';\nconst FILL_KEYWORD = 'fill';\nconst BG_FILL = 'bgFill';\nconst STYLECLASS_SEP = ',';\nconst config = getConfig();\n\nlet classes = new Map<string, ClassDef>();\n\nconst sanitizeText = (txt: string) => common.sanitizeText(txt, config);\n\n/**\n * Called when the parser comes across a (style) class definition\n * @example classDef my-style fill:#f96;\n *\n * @param id - the id of this (style) class\n * @param styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n */\nexport const addStyleClass = function (id: string, styleAttributes = '') {\n  // create a new style class object with this id\n  let foundClass = classes.get(id);\n  if (!foundClass) {\n    foundClass = { id: id, styles: [], textStyles: [] };\n    classes.set(id, foundClass); // This is a classDef\n  }\n  if (styleAttributes !== undefined && styleAttributes !== null) {\n    styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n      // remove any trailing ;\n      const fixedAttrib = attrib.replace(/([^;]*);/, '$1').trim();\n\n      // replace some style keywords\n      if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n        const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n        const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n        foundClass.textStyles.push(newStyle2);\n      }\n      foundClass.styles.push(fixedAttrib);\n    });\n  }\n};\n\n/**\n * Called when the parser comes across a style definition\n * @example style my-block-id fill:#f96;\n *\n * @param id - the id of the block to style\n * @param styles - the string with 1 or more style attributes (each separated by a comma)\n */\nexport const addStyle2Node = function (id: string, styles = '') {\n  const foundBlock = blockDatabase.get(id)!;\n  if (styles !== undefined && styles !== null) {\n    foundBlock.styles = styles.split(STYLECLASS_SEP);\n  }\n};\n\n/**\n * Add a CSS/style class to the block with the given id.\n * If the block isn't already in the list of known blocks, add it.\n * Might be called by parser when a CSS/style class should be applied to a block\n *\n * @param itemIds - The id or a list of ids of the item(s) to apply the css class to\n * @param cssClassName - CSS class name\n */\nexport const setCssClass = function (itemIds: string, cssClassName: string) {\n  itemIds.split(',').forEach(function (id: string) {\n    let foundBlock = blockDatabase.get(id);\n    if (foundBlock === undefined) {\n      const trimmedId = id.trim();\n      foundBlock = { id: trimmedId, type: 'na', children: [] } as Block;\n      blockDatabase.set(trimmedId, foundBlock);\n    }\n    if (!foundBlock.classes) {\n      foundBlock.classes = [];\n    }\n    foundBlock.classes.push(cssClassName);\n  });\n};\n\nconst populateBlockDatabase = (_blockList: Block[], parent: Block): void => {\n  const blockList = _blockList.flat();\n  const children = [];\n  for (const block of blockList) {\n    if (block.label) {\n      block.label = sanitizeText(block.label);\n    }\n    if (block.type === 'classDef') {\n      addStyleClass(block.id, block.css);\n      continue;\n    }\n    if (block.type === 'applyClass') {\n      setCssClass(block.id, block?.styleClass ?? '');\n      continue;\n    }\n    if (block.type === 'applyStyles') {\n      if (block?.stylesStr) {\n        addStyle2Node(block.id, block?.stylesStr);\n      }\n      continue;\n    }\n    if (block.type === 'column-setting') {\n      parent.columns = block.columns ?? -1;\n    } else if (block.type === 'edge') {\n      const count = (edgeCount.get(block.id) ?? 0) + 1;\n      edgeCount.set(block.id, count);\n      block.id = count + '-' + block.id;\n      edgeList.push(block);\n    } else {\n      if (!block.label) {\n        if (block.type === 'composite') {\n          block.label = '';\n          // log.debug('abc89 composite', block);\n        } else {\n          block.label = block.id;\n        }\n      }\n      const existingBlock = blockDatabase.get(block.id);\n\n      if (existingBlock === undefined) {\n        blockDatabase.set(block.id, block);\n      } else {\n        // Add newer relevant data to aggregated node\n        if (block.type !== 'na') {\n          existingBlock.type = block.type;\n        }\n        if (block.label !== block.id) {\n          existingBlock.label = block.label;\n        }\n      }\n\n      if (block.children) {\n        populateBlockDatabase(block.children, block);\n      }\n      if (block.type === 'space') {\n        // log.debug('abc95 space', block);\n        const w = block.width ?? 1;\n        for (let j = 0; j < w; j++) {\n          const newBlock = clone(block);\n          newBlock.id = newBlock.id + '-' + j;\n          blockDatabase.set(newBlock.id, newBlock);\n          children.push(newBlock);\n        }\n      } else if (existingBlock === undefined) {\n        children.push(block);\n      }\n    }\n  }\n  parent.children = children;\n};\n\nlet blocks: Block[] = [];\nlet rootBlock = { id: 'root', type: 'composite', children: [], columns: -1 } as Block;\n\nconst clear = (): void => {\n  log.debug('Clear called');\n  commonClear();\n  rootBlock = { id: 'root', type: 'composite', children: [], columns: -1 } as Block;\n  blockDatabase = new Map([['root', rootBlock]]);\n  blocks = [];\n  classes = new Map();\n\n  edgeList = [];\n  edgeCount = new Map();\n};\n\nexport function typeStr2Type(typeStr: string) {\n  log.debug('typeStr2Type', typeStr);\n  switch (typeStr) {\n    case '[]':\n      return 'square';\n    case '()':\n      log.debug('we have a round');\n      return 'round';\n    case '(())':\n      return 'circle';\n    case '>]':\n      return 'rect_left_inv_arrow';\n    case '{}':\n      return 'diamond';\n    case '{{}}':\n      return 'hexagon';\n    case '([])':\n      return 'stadium';\n    case '[[]]':\n      return 'subroutine';\n    case '[()]':\n      return 'cylinder';\n    case '((()))':\n      return 'doublecircle';\n    case '[//]':\n      return 'lean_right';\n    case '[\\\\\\\\]':\n      return 'lean_left';\n    case '[/\\\\]':\n      return 'trapezoid';\n    case '[\\\\/]':\n      return 'inv_trapezoid';\n    case '<[]>':\n      return 'block_arrow';\n    default:\n      return 'na';\n  }\n}\n\nexport function edgeTypeStr2Type(typeStr: string): string {\n  log.debug('typeStr2Type', typeStr);\n  switch (typeStr) {\n    case '==':\n      return 'thick';\n    default:\n      return 'normal';\n  }\n}\n\nexport function edgeStrToEdgeData(typeStr: string): string {\n  switch (typeStr.trim()) {\n    case '--x':\n      return 'arrow_cross';\n    case '--o':\n      return 'arrow_circle';\n    default:\n      return 'arrow_point';\n  }\n}\n\nlet cnt = 0;\nexport const generateId = () => {\n  cnt++;\n  return 'id-' + Math.random().toString(36).substr(2, 12) + '-' + cnt;\n};\n\nconst setHierarchy = (block: Block[]): void => {\n  rootBlock.children = block;\n  populateBlockDatabase(block, rootBlock);\n  blocks = rootBlock.children;\n};\n\nconst getColumns = (blockId: string): number => {\n  const block = blockDatabase.get(blockId);\n  if (!block) {\n    return -1;\n  }\n  if (block.columns) {\n    return block.columns;\n  }\n  if (!block.children) {\n    return -1;\n  }\n  return block.children.length;\n};\n\n/**\n * Returns all the blocks as a flat array\n * @returns\n */\nconst getBlocksFlat = () => {\n  return [...blockDatabase.values()];\n};\n/**\n * Returns the hierarchy of blocks\n * @returns\n */\nconst getBlocks = () => {\n  return blocks || [];\n};\n\nconst getEdges = () => {\n  return edgeList;\n};\nconst getBlock = (id: string) => {\n  return blockDatabase.get(id);\n};\n\nconst setBlock = (block: Block) => {\n  blockDatabase.set(block.id, block);\n};\n\nconst getLogger = () => log;\n\n/**\n * Return all of the style classes\n */\nexport const getClasses = function () {\n  return classes;\n};\n\nconst db = {\n  getConfig: () => configApi.getConfig().block,\n  typeStr2Type: typeStr2Type,\n  edgeTypeStr2Type: edgeTypeStr2Type,\n  edgeStrToEdgeData,\n  getLogger,\n  getBlocksFlat,\n  getBlocks,\n  getEdges,\n  setHierarchy,\n  getBlock,\n  setBlock,\n  getColumns,\n  getClasses,\n  clear,\n  generateId,\n} as const;\n\nexport type BlockDB = typeof db & DiagramDB;\nexport default db;\n", "import * as khroma from 'khroma';\nimport { getIconStyles } from '../globalStyles.js';\n\n/** Returns the styles given options */\nexport interface BlockChartStyleOptions {\n  arrowheadColor: string;\n  border2: string;\n  clusterBkg: string;\n  clusterBorder: string;\n  edgeLabelBackground: string;\n  fontFamily: string;\n  lineColor: string;\n  mainBkg: string;\n  nodeBorder: string;\n  nodeTextColor: string;\n  tertiaryColor: string;\n  textColor: string;\n  titleColor: string;\n}\n\nconst fade = (color: string, opacity: number) => {\n  // @ts-ignore TODO: incorrect types from khroma\n  const channel = khroma.channel;\n\n  const r = channel(color, 'r');\n  const g = channel(color, 'g');\n  const b = channel(color, 'b');\n\n  // @ts-ignore incorrect types from khroma\n  return khroma.rgba(r, g, b, opacity);\n};\n\nconst getStyles = (options: BlockChartStyleOptions) =>\n  `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .cluster-label text {\n    fill: ${options.titleColor};\n  }\n  .cluster-label span,p {\n    color: ${options.titleColor};\n  }\n\n\n\n  .label text,span,p {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n  .flowchart-label text {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n      background-color: ${options.edgeLabelBackground};\n      fill: ${options.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${fade(options.edgeLabelBackground, 0.5)};\n    // background-color:\n  }\n\n  .node .cluster {\n    // fill: ${fade(options.mainBkg, 0.5)};\n    fill: ${fade(options.clusterBkg, 0.5)};\n    stroke: ${fade(options.clusterBorder, 0.2)};\n    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  .cluster span,p {\n    color: ${options.titleColor};\n  }\n  /* .cluster div {\n    color: ${options.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n  ${getIconStyles()}\n`;\n\nexport default getStyles;\n", "import { select as d3select } from 'd3';\nimport type { Diagram } from '../../Diagram.js';\nimport * as configApi from '../../config.js';\nimport insertMarkers from '../../dagre-wrapper/markers.js';\nimport { log } from '../../logger.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { BlockDB } from './blockDB.js';\nimport { layout } from './layout.js';\nimport { calculateBlockSizes, insertBlocks, insertEdges } from './renderHelpers.js';\n\nexport const getClasses = function (text: any, diagObj: any) {\n  return diagObj.db.getClasses();\n};\n\nexport const draw = async function (\n  text: string,\n  id: string,\n  _version: string,\n  diagObj: Diagram\n): Promise<void> {\n  const { securityLevel, block: conf } = configApi.getConfig();\n  const db = diagObj.db as BlockDB;\n  let sandboxElement: any;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = d3select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? d3select<HTMLBodyElement, unknown>(sandboxElement.nodes()[0].contentDocument.body)\n      : d3select<HTMLBodyElement, unknown>('body');\n\n  const svg =\n    securityLevel === 'sandbox'\n      ? root.select<SVGSVGElement>(`[id=\"${id}\"]`)\n      : d3select<SVGSVGElement, unknown>(`[id=\"${id}\"]`);\n\n  // Define the supported markers for the diagram\n  const markers = ['point', 'circle', 'cross'];\n\n  // Add the marker definitions to the svg as marker tags\n  insertMarkers(svg, markers, diagObj.type, id);\n\n  const bl = db.getBlocks();\n  const blArr = db.getBlocksFlat();\n  const edges = db.getEdges();\n\n  const nodes = svg.insert('g').attr('class', 'block');\n  await calculateBlockSizes(nodes, bl, db);\n  const bounds = layout(db);\n  await insertBlocks(nodes, bl, db);\n  await insertEdges(nodes, edges, blArr, db, id);\n\n  // Establish svg dimensions and get width and height\n  // Why, oh why ????\n  if (bounds) {\n    const bounds2 = bounds;\n    const magicFactor = Math.max(1, Math.round(0.125 * (bounds2.width / bounds2.height)));\n    const height = bounds2.height + magicFactor + 10;\n    const width = bounds2.width + 10;\n    const { useMaxWidth } = conf!;\n    configureSvgSize(svg, height, width, !!useMaxWidth);\n    log.debug('Here Bounds', bounds, bounds2);\n    svg.attr(\n      'viewBox',\n      `${bounds2.x - 5} ${bounds2.y - 5} ${bounds2.width + 10} ${bounds2.height + 10}`\n    );\n  }\n};\n\nexport default {\n  draw,\n  getClasses,\n};\n", "/** Setup arrow head and define the marker. The result is appended to the svg. */\n\nimport { log } from '../logger.js';\n\n// Only add the number of markers that the diagram needs\nconst insertMarkers = (elem, markerArray, type, id) => {\n  markerArray.forEach((markerName) => {\n    markers[markerName](elem, type, id);\n  });\n};\n\nconst extension = (elem, type, id) => {\n  log.trace('Making markers for ', id);\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionStart')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,7 L18,13 V 1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionEnd')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,1 V 13 L18,7 Z'); // this is actual shape for arrowhead\n};\n\nconst composition = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionStart')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionEnd')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst aggregation = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationStart')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationEnd')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst dependency = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyStart')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 6)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 5,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyEnd')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\nconst lollipop = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopStart')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopEnd')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n};\nconst point = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 6)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 0 L 10 5 L 0 10 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 4.5)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 5 L 10 10 L 10 0 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst circle = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 11)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', -1)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst cross = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossEnd')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', 12)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossStart')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', -1)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n};\nconst barb = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-barbEnd')\n    .attr('refX', 19)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 14)\n    .attr('markerUnits', 'strokeWidth')\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 19,7 L9,13 L14,7 L9,1 Z');\n};\n\n// TODO rename the class diagram markers to something shape descriptive and semantic free\nconst markers = {\n  extension,\n  composition,\n  aggregation,\n  dependency,\n  lollipop,\n  point,\n  circle,\n  cross,\n  barb,\n};\nexport default insertMarkers;\n", "import type { BlockDB } from './blockDB.js';\nimport type { Block } from './blockTypes.js';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\n// TODO: This means the number we provide in diagram's config will never be used. Should fix.\nconst padding = getConfig()?.block?.padding ?? 8;\n\ninterface BlockPosition {\n  px: number;\n  py: number;\n}\n\nexport function calculateBlockPosition(columns: number, position: number): BlockPosition {\n  // log.debug('calculateBlockPosition abc89', columns, position);\n  // Ensure that columns is a positive integer\n  if (columns === 0 || !Number.isInteger(columns)) {\n    throw new Error('Columns must be an integer !== 0.');\n  }\n\n  // Ensure that position is a non-negative integer\n  if (position < 0 || !Number.isInteger(position)) {\n    throw new Error('Position must be a non-negative integer.' + position);\n  }\n\n  if (columns < 0) {\n    // Auto columns is set\n    return { px: position, py: 0 };\n  }\n  if (columns === 1) {\n    // Auto columns is set\n    return { px: 0, py: position };\n  }\n  // Calculate posX and posY\n  const px = position % columns;\n  const py = Math.floor(position / columns);\n  // log.debug('calculateBlockPosition abc89', columns, position, '=> (', px, py, ')');\n  return { px, py };\n}\n\nconst getMaxChildSize = (block: Block) => {\n  let maxWidth = 0;\n  let maxHeight = 0;\n  // find max width of children\n  // log.debug('getMaxChildSize abc95 (start) parent:', block.id);\n  for (const child of block.children) {\n    const { width, height, x, y } = child.size ?? { width: 0, height: 0, x: 0, y: 0 };\n    log.debug(\n      'getMaxChildSize abc95 child:',\n      child.id,\n      'width:',\n      width,\n      'height:',\n      height,\n      'x:',\n      x,\n      'y:',\n      y,\n      child.type\n    );\n    if (child.type === 'space') {\n      continue;\n    }\n    if (width > maxWidth) {\n      maxWidth = width / (block.widthInColumns ?? 1);\n    }\n    if (height > maxHeight) {\n      maxHeight = height;\n    }\n  }\n  return { width: maxWidth, height: maxHeight };\n};\n\nfunction setBlockSizes(block: Block, db: BlockDB, siblingWidth = 0, siblingHeight = 0) {\n  log.debug(\n    'setBlockSizes abc95 (start)',\n    block.id,\n    block?.size?.x,\n    'block width =',\n    block?.size,\n    'siblingWidth',\n    siblingWidth\n  );\n  if (!block?.size?.width) {\n    block.size = {\n      width: siblingWidth,\n      height: siblingHeight,\n      x: 0,\n      y: 0,\n    };\n  }\n  let maxWidth = 0;\n  let maxHeight = 0;\n\n  if (block.children?.length > 0) {\n    for (const child of block.children) {\n      setBlockSizes(child, db);\n    }\n    // find max width of children\n    const childSize = getMaxChildSize(block);\n    maxWidth = childSize.width;\n    maxHeight = childSize.height;\n    log.debug('setBlockSizes abc95 maxWidth of', block.id, ':s children is ', maxWidth, maxHeight);\n\n    // set width of block to max width of children\n    for (const child of block.children) {\n      if (child.size) {\n        log.debug(\n          `abc95 Setting size of children of ${block.id} id=${child.id} ${maxWidth} ${maxHeight} ${JSON.stringify(child.size)}`\n        );\n        child.size.width =\n          maxWidth * (child.widthInColumns ?? 1) + padding * ((child.widthInColumns ?? 1) - 1);\n        child.size.height = maxHeight;\n        child.size.x = 0;\n        child.size.y = 0;\n\n        log.debug(\n          `abc95 updating size of ${block.id} children child:${child.id} maxWidth:${maxWidth} maxHeight:${maxHeight}`\n        );\n      }\n    }\n    for (const child of block.children) {\n      setBlockSizes(child, db, maxWidth, maxHeight);\n    }\n\n    const columns = block.columns ?? -1;\n    let numItems = 0;\n    for (const child of block.children) {\n      numItems += child.widthInColumns ?? 1;\n    }\n\n    // The width and height in number blocks\n    let xSize = block.children.length;\n    if (columns > 0 && columns < numItems) {\n      xSize = columns;\n    }\n\n    const ySize = Math.ceil(numItems / xSize);\n\n    let width = xSize * (maxWidth + padding) + padding;\n    let height = ySize * (maxHeight + padding) + padding;\n    // If maxWidth\n    if (width < siblingWidth) {\n      log.debug(\n        `Detected to small sibling: abc95 ${block.id} siblingWidth ${siblingWidth} siblingHeight ${siblingHeight} width ${width}`\n      );\n      width = siblingWidth;\n      height = siblingHeight;\n      const childWidth = (siblingWidth - xSize * padding - padding) / xSize;\n      const childHeight = (siblingHeight - ySize * padding - padding) / ySize;\n      // cspell:ignore indata\n      log.debug('Size indata abc88', block.id, 'childWidth', childWidth, 'maxWidth', maxWidth);\n      log.debug('Size indata abc88', block.id, 'childHeight', childHeight, 'maxHeight', maxHeight);\n      log.debug('Size indata abc88 xSize', xSize, 'padding', padding);\n\n      // set width of block to max width of children\n      for (const child of block.children) {\n        if (child.size) {\n          child.size.width = childWidth;\n          child.size.height = childHeight;\n          child.size.x = 0;\n          child.size.y = 0;\n        }\n      }\n    }\n\n    log.debug(\n      `abc95 (finale calc) ${block.id} xSize ${xSize} ySize ${ySize} columns ${columns}${\n        block.children.length\n      } width=${Math.max(width, block.size?.width || 0)}`\n    );\n    if (width < (block?.size?.width || 0)) {\n      width = block?.size?.width || 0;\n\n      // Grow children to fit\n      const num = columns > 0 ? Math.min(block.children.length, columns) : block.children.length;\n      if (num > 0) {\n        const childWidth = (width - num * padding - padding) / num;\n        log.debug('abc95 (growing to fit) width', block.id, width, block.size?.width, childWidth);\n        for (const child of block.children) {\n          if (child.size) {\n            child.size.width = childWidth;\n          }\n        }\n      }\n    }\n    block.size = {\n      width,\n      height,\n      x: 0,\n      y: 0,\n    };\n  }\n\n  log.debug(\n    'setBlockSizes abc94 (done)',\n    block.id,\n    block?.size?.x,\n    block?.size?.width,\n    block?.size?.y,\n    block?.size?.height\n  );\n}\n\nfunction layoutBlocks(block: Block, db: BlockDB) {\n  log.debug(\n    `abc85 layout blocks (=>layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n  const columns = block.columns ?? -1;\n  log.debug('layoutBlocks columns abc95', block.id, '=>', columns, block);\n  if (\n    block.children && // find max width of children\n    block.children.length > 0\n  ) {\n    const width = block?.children[0]?.size?.width ?? 0;\n    const widthOfChildren = block.children.length * width + (block.children.length - 1) * padding;\n\n    log.debug('widthOfChildren 88', widthOfChildren, 'posX');\n\n    // let first = true;\n    let columnPos = 0;\n    log.debug('abc91 block?.size?.x', block.id, block?.size?.x);\n    let startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n    let rowPos = 0;\n    for (const child of block.children) {\n      const parent = block;\n\n      if (!child.size) {\n        continue;\n      }\n      const { width, height } = child.size;\n      const { px, py } = calculateBlockPosition(columns, columnPos);\n      if (py != rowPos) {\n        rowPos = py;\n        startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n        log.debug('New row in layout for block', block.id, ' and child ', child.id, rowPos);\n      }\n      log.debug(\n        `abc89 layout blocks (child) id: ${child.id} Pos: ${columnPos} (px, py) ${px},${py} (${parent?.size?.x},${parent?.size?.y}) parent: ${parent.id} width: ${width}${padding}`\n      );\n      if (parent.size) {\n        const halfWidth = width / 2;\n        child.size.x = startingPosX + padding + halfWidth;\n\n        // cspell:ignore pyid\n        log.debug(\n          `abc91 layout blocks (calc) px, pyid:${\n            child.id\n          } startingPos=X${startingPosX} new startingPosX${\n            child.size.x\n          } ${halfWidth} padding=${padding} width=${width} halfWidth=${halfWidth} => x:${\n            child.size.x\n          } y:${child.size.y} ${child.widthInColumns} (width * (child?.w || 1)) / 2 ${\n            (width * (child?.widthInColumns ?? 1)) / 2\n          }`\n        );\n\n        startingPosX = child.size.x + halfWidth;\n\n        child.size.y =\n          parent.size.y - parent.size.height / 2 + py * (height + padding) + height / 2 + padding;\n\n        log.debug(\n          `abc88 layout blocks (calc) px, pyid:${\n            child.id\n          }startingPosX${startingPosX}${padding}${halfWidth}=>x:${child.size.x}y:${child.size.y}${\n            child.widthInColumns\n          }(width * (child?.w || 1)) / 2${(width * (child?.widthInColumns ?? 1)) / 2}`\n        );\n      }\n      if (child.children) {\n        layoutBlocks(child, db);\n      }\n      columnPos += child?.widthInColumns ?? 1;\n      log.debug('abc88 columnsPos', child, columnPos);\n    }\n  }\n  log.debug(\n    `layout blocks (<==layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n}\n\nfunction findBounds(\n  block: Block,\n  { minX, minY, maxX, maxY } = { minX: 0, minY: 0, maxX: 0, maxY: 0 }\n) {\n  if (block.size && block.id !== 'root') {\n    const { x, y, width, height } = block.size;\n    if (x - width / 2 < minX) {\n      minX = x - width / 2;\n    }\n    if (y - height / 2 < minY) {\n      minY = y - height / 2;\n    }\n    if (x + width / 2 > maxX) {\n      maxX = x + width / 2;\n    }\n    if (y + height / 2 > maxY) {\n      maxY = y + height / 2;\n    }\n  }\n  if (block.children) {\n    for (const child of block.children) {\n      ({ minX, minY, maxX, maxY } = findBounds(child, { minX, minY, maxX, maxY }));\n    }\n  }\n  return { minX, minY, maxX, maxY };\n}\n\nexport function layout(db: BlockDB) {\n  const root = db.getBlock('root');\n  if (!root) {\n    return;\n  }\n\n  setBlockSizes(root, db, 0, 0);\n  layoutBlocks(root, db);\n  // Position blocks relative to parents\n  // positionBlock(root, root, db);\n  log.debug('getBlocks', JSON.stringify(root, null, 2));\n\n  const { minX, minY, maxX, maxY } = findBounds(root);\n\n  const height = maxY - minY;\n  const width = maxX - minX;\n  return { x: minX, y: minY, width, height };\n}\n", "import * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport { getConfig } from '../../config.js';\nimport { insertEdge, insertEdgeLabel, positionEdgeLabel } from '../../dagre-wrapper/edges.js';\nimport { insertNode, positionNode } from '../../dagre-wrapper/nodes.js';\nimport { getStylesFromArray } from '../../utils.js';\nimport type { BlockDB } from './blockDB.js';\nimport type { Block } from './blockTypes.js';\n\nfunction getNodeFromBlock(block: Block, db: BlockDB, positioned = false) {\n  const vertex = block;\n\n  let classStr = 'default';\n  if ((vertex?.classes?.length || 0) > 0) {\n    classStr = (vertex?.classes ?? []).join(' ');\n  }\n  classStr = classStr + ' flowchart-label';\n\n  // We create a SVG label, either by delegating to addHtmlLabel or manually\n  let radius = 0;\n  let shape = '';\n  let padding;\n  // Set the shape based parameters\n  switch (vertex.type) {\n    case 'round':\n      radius = 5;\n      shape = 'rect';\n      break;\n    case 'composite':\n      radius = 0;\n      shape = 'composite';\n      padding = 0;\n      break;\n    case 'square':\n      shape = 'rect';\n      break;\n    case 'diamond':\n      shape = 'question';\n      break;\n    case 'hexagon':\n      shape = 'hexagon';\n      break;\n    case 'block_arrow':\n      shape = 'block_arrow';\n      break;\n    case 'odd':\n      shape = 'rect_left_inv_arrow';\n      break;\n    case 'lean_right':\n      shape = 'lean_right';\n      break;\n    case 'lean_left':\n      shape = 'lean_left';\n      break;\n    case 'trapezoid':\n      shape = 'trapezoid';\n      break;\n    case 'inv_trapezoid':\n      shape = 'inv_trapezoid';\n      break;\n    case 'rect_left_inv_arrow':\n      shape = 'rect_left_inv_arrow';\n      break;\n    case 'circle':\n      shape = 'circle';\n      break;\n    case 'ellipse':\n      shape = 'ellipse';\n      break;\n    case 'stadium':\n      shape = 'stadium';\n      break;\n    case 'subroutine':\n      shape = 'subroutine';\n      break;\n    case 'cylinder':\n      shape = 'cylinder';\n      break;\n    case 'group':\n      shape = 'rect';\n      break;\n    case 'doublecircle':\n      shape = 'doublecircle';\n      break;\n    default:\n      shape = 'rect';\n  }\n\n  const styles = getStylesFromArray(vertex?.styles ?? []);\n\n  // Use vertex id as text in the box if no text is provided by the graph definition\n  const vertexText = vertex.label;\n\n  const bounds = vertex.size ?? { width: 0, height: 0, x: 0, y: 0 };\n  // Add the node\n  const node = {\n    labelStyle: styles.labelStyle,\n    shape: shape,\n    labelText: vertexText,\n    rx: radius,\n    ry: radius,\n    class: classStr,\n    style: styles.style,\n    id: vertex.id,\n    directions: vertex.directions,\n    width: bounds.width,\n    height: bounds.height,\n    x: bounds.x,\n    y: bounds.y,\n    positioned,\n    intersect: undefined,\n    type: vertex.type,\n    padding: padding ?? getConfig()?.block?.padding ?? 0,\n  };\n  return node;\n}\nasync function calculateBlockSize(\n  elem: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  block: any,\n  db: any\n) {\n  const node = getNodeFromBlock(block, db, false);\n  if (node.type === 'group') {\n    return;\n  }\n\n  // Add the element to the DOM to size it\n  const config = getConfig();\n  const nodeEl = await insertNode(elem, node, { config });\n  const boundingBox = nodeEl.node().getBBox();\n  const obj = db.getBlock(node.id);\n  obj.size = { width: boundingBox.width, height: boundingBox.height, x: 0, y: 0, node: nodeEl };\n  db.setBlock(obj);\n  nodeEl.remove();\n}\ntype ActionFun = typeof calculateBlockSize;\n\nexport async function insertBlockPositioned(elem: any, block: Block, db: any) {\n  const node = getNodeFromBlock(block, db, true);\n  // Add the element to the DOM to size it\n  const obj = db.getBlock(node.id);\n  if (obj.type !== 'space') {\n    const config = getConfig();\n    await insertNode(elem, node, { config });\n    block.intersect = node?.intersect;\n    positionNode(node);\n  }\n}\n\nexport async function performOperations(\n  elem: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  blocks: Block[],\n  db: BlockDB,\n  operation: ActionFun\n) {\n  for (const block of blocks) {\n    await operation(elem, block, db);\n    if (block.children) {\n      await performOperations(elem, block.children, db, operation);\n    }\n  }\n}\n\nexport async function calculateBlockSizes(elem: any, blocks: Block[], db: BlockDB) {\n  await performOperations(elem, blocks, db, calculateBlockSize);\n}\n\nexport async function insertBlocks(\n  elem: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  blocks: Block[],\n  db: BlockDB\n) {\n  await performOperations(elem, blocks, db, insertBlockPositioned);\n}\n\nexport async function insertEdges(\n  elem: any,\n  edges: Block[],\n  blocks: Block[],\n  db: BlockDB,\n  id: string\n) {\n  const g = new graphlib.Graph({\n    multigraph: true,\n    compound: true,\n  });\n  g.setGraph({\n    rankdir: 'TB',\n    nodesep: 10,\n    ranksep: 10,\n    marginx: 8,\n    marginy: 8,\n  });\n\n  for (const block of blocks) {\n    if (block.size) {\n      g.setNode(block.id, {\n        width: block.size.width,\n        height: block.size.height,\n        intersect: block.intersect,\n      });\n    }\n  }\n\n  for (const edge of edges) {\n    // elem, e, edge, clusterDb, diagramType, graph;\n    if (edge.start && edge.end) {\n      const startBlock = db.getBlock(edge.start);\n      const endBlock = db.getBlock(edge.end);\n\n      if (startBlock?.size && endBlock?.size) {\n        const start = startBlock.size;\n        const end = endBlock.size;\n        const points = [\n          { x: start.x, y: start.y },\n          { x: start.x + (end.x - start.x) / 2, y: start.y + (end.y - start.y) / 2 },\n          { x: end.x, y: end.y },\n        ];\n        // edge.points = points;\n        insertEdge(\n          elem,\n          { v: edge.start, w: edge.end, name: edge.id },\n          {\n            ...edge,\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: 'edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1',\n          },\n          undefined,\n          'block',\n          g,\n          id\n        );\n        if (edge.label) {\n          await insertEdgeLabel(elem, {\n            ...edge,\n            label: edge.label,\n            labelStyle: 'stroke: #333; stroke-width: 1.5px;fill:none;',\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: 'edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1',\n          });\n          positionEdgeLabel(\n            { ...edge, x: points[1].x, y: points[1].y },\n            {\n              originalPath: points,\n            }\n          );\n        }\n      }\n    }\n  }\n}\n", "import { select } from 'd3';\nimport { log } from '../logger.js';\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport { evaluate } from '../diagrams/common/common.js';\nimport { decodeEntities } from '../utils.js';\nimport { replaceIconSubstring } from '../rendering-util/createText.js';\n\n/**\n * @param dom\n * @param styleFn\n */\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\n/**\n * @param {any} node\n * @returns {SVGForeignObjectElement} Node\n */\nfunction addHtmlLabel(node) {\n  const fo = select(document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject'));\n  const div = fo.append('xhtml:div');\n\n  const label = node.label;\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  const span = div.append('span');\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr('class', labelClass);\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'inline-block');\n  // Fix for firefox\n  div.style('white-space', 'nowrap');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  return fo.node();\n}\n/**\n * @param _vertexText\n * @param style\n * @param isTitle\n * @param isNode\n * @deprecated svg-util/createText instead\n */\nconst createLabel = async (_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || '';\n  if (typeof vertexText === 'object') {\n    vertexText = vertexText[0];\n  }\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n    vertexText = vertexText.replace(/\\\\n|\\n/g, '<br />');\n    log.debug('vertexText' + vertexText);\n    const label = await replaceIconSubstring(decodeEntities(vertexText));\n    const node = {\n      isNode,\n      label,\n      labelStyle: style.replace('fill:', 'color:'),\n    };\n    let vertexNode = addHtmlLabel(node);\n    // vertexNode.parentNode.removeChild(vertexNode);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n    svgLabel.setAttribute('style', style.replace('color:', 'fill:'));\n    let rows = [];\n    if (typeof vertexText === 'string') {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n\n    for (const row of rows) {\n      const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');\n      tspan.setAttributeNS('http://www.w3.org/XML/1998/namespace', 'xml:space', 'preserve');\n      tspan.setAttribute('dy', '1em');\n      tspan.setAttribute('x', '0');\n      if (isTitle) {\n        tspan.setAttribute('class', 'title-row');\n      } else {\n        tspan.setAttribute('class', 'row');\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n};\n\nexport default createLabel;\n", "import { log } from '../logger.js';\nimport createLabel from './createLabel.js';\nimport { createText } from '../rendering-util/createText.js';\nimport { line, curveBasis, select } from 'd3';\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport utils from '../utils.js';\nimport { evaluate, getUrl } from '../diagrams/common/common.js';\nimport { getLineFunctionsWithOffset } from '../utils/lineWithOffset.js';\nimport { getSubGraphTitleMargins } from '../utils/subGraphTitleMargins.js';\nimport { addEdgeMarkers } from './edgeMarker.js';\n\nlet edgeLabels = {};\nlet terminalLabels = {};\n\nexport const clear = () => {\n  edgeLabels = {};\n  terminalLabels = {};\n};\n\nexport const insertEdgeLabel = async (elem, edge) => {\n  const config = getConfig();\n  const useHtmlLabels = evaluate(config.flowchart.htmlLabels);\n  // Create the actual text element\n  const labelElement =\n    edge.labelType === 'markdown'\n      ? createText(\n          elem,\n          edge.label,\n          {\n            style: edge.labelStyle,\n            useHtmlLabels,\n            addSvgBackground: true,\n          },\n          config\n        )\n      : await createLabel(edge.label, edge.labelStyle);\n\n  // Create outer g, edgeLabel, this will be positioned after graph layout\n  const edgeLabel = elem.insert('g').attr('class', 'edgeLabel');\n\n  // Create inner g, label, this will be positioned now for centering the text\n  const label = edgeLabel.insert('g').attr('class', 'label');\n  label.node().appendChild(labelElement);\n\n  // Center the label\n  let bbox = labelElement.getBBox();\n  if (useHtmlLabels) {\n    const div = labelElement.children[0];\n    const dv = select(labelElement);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n\n  // Make element accessible by id for positioning\n  edgeLabels[edge.id] = edgeLabel;\n\n  // Update the abstract data of the edge with the new information about its width and height\n  edge.width = bbox.width;\n  edge.height = bbox.height;\n\n  let fo;\n  if (edge.startLabelLeft) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(edge.startLabelLeft, edge.labelStyle);\n    const startEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startLeft = startEdgeLabelLeft;\n    setTerminalWidth(fo, edge.startLabelLeft);\n  }\n  if (edge.startLabelRight) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(edge.startLabelRight, edge.labelStyle);\n    const startEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelRight.insert('g').attr('class', 'inner');\n    fo = startEdgeLabelRight.node().appendChild(startLabelElement);\n    inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startRight = startEdgeLabelRight;\n    setTerminalWidth(fo, edge.startLabelRight);\n  }\n  if (edge.endLabelLeft) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelLeft, edge.labelStyle);\n    const endEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelLeft.node().appendChild(endLabelElement);\n\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endLeft = endEdgeLabelLeft;\n    setTerminalWidth(fo, edge.endLabelLeft);\n  }\n  if (edge.endLabelRight) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelRight, edge.labelStyle);\n    const endEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelRight.insert('g').attr('class', 'inner');\n\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelRight.node().appendChild(endLabelElement);\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endRight = endEdgeLabelRight;\n    setTerminalWidth(fo, edge.endLabelRight);\n  }\n  return labelElement;\n};\n\n/**\n * @param {any} fo\n * @param {any} value\n */\nfunction setTerminalWidth(fo, value) {\n  if (getConfig().flowchart.htmlLabels && fo) {\n    fo.style.width = value.length * 9 + 'px';\n    fo.style.height = '12px';\n  }\n}\n\nexport const positionEdgeLabel = (edge, paths) => {\n  log.debug('Moving label abc88 ', edge.id, edge.label, edgeLabels[edge.id], paths);\n  let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  const siteConfig = getConfig();\n  const { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  if (edge.label) {\n    const el = edgeLabels[edge.id];\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      //   // debugger;\n      const pos = utils.calcLabelPosition(path);\n      log.debug(\n        'Moving label ' + edge.label + ' from (',\n        x,\n        ',',\n        y,\n        ') to (',\n        pos.x,\n        ',',\n        pos.y,\n        ') abc88'\n      );\n      if (paths.updatedPath) {\n        x = pos.x;\n        y = pos.y;\n      }\n    }\n    el.attr('transform', `translate(${x}, ${y + subGraphTitleTotalMargin / 2})`);\n  }\n\n  //let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  if (edge.startLabelLeft) {\n    const el = terminalLabels[edge.id].startLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, 'start_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.startLabelRight) {\n    const el = terminalLabels[edge.id].startRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(\n        edge.arrowTypeStart ? 10 : 0,\n        'start_right',\n        path\n      );\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelLeft) {\n    const el = terminalLabels[edge.id].endLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelRight) {\n    const el = terminalLabels[edge.id].endRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      // debugger;\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_right', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n};\n\nconst outsideNode = (node, point) => {\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(point.x - x);\n  const dy = Math.abs(point.y - y);\n  const w = node.width / 2;\n  const h = node.height / 2;\n  if (dx >= w || dy >= h) {\n    return true;\n  }\n  return false;\n};\n\nexport const intersection = (node, outsidePoint, insidePoint) => {\n  log.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(outsidePoint)}\n  insidePoint : ${JSON.stringify(insidePoint)}\n  node        : x:${node.x} y:${node.y} w:${node.width} h:${node.height}`);\n  const x = node.x;\n  const y = node.y;\n\n  const dx = Math.abs(x - insidePoint.x);\n  // const dy = Math.abs(y - insidePoint.y);\n  const w = node.width / 2;\n  let r = insidePoint.x < outsidePoint.x ? w - dx : w + dx;\n  const h = node.height / 2;\n\n  const Q = Math.abs(outsidePoint.y - insidePoint.y);\n  const R = Math.abs(outsidePoint.x - insidePoint.x);\n\n  if (Math.abs(y - outsidePoint.y) * w > Math.abs(x - outsidePoint.x) * h) {\n    // Intersection is top or bottom of rect.\n    let q = insidePoint.y < outsidePoint.y ? outsidePoint.y - h - y : y - h - outsidePoint.y;\n    r = (R * q) / Q;\n    const res = {\n      x: insidePoint.x < outsidePoint.x ? insidePoint.x + r : insidePoint.x - R + r,\n      y: insidePoint.y < outsidePoint.y ? insidePoint.y + Q - q : insidePoint.y - Q + q,\n    };\n\n    if (r === 0) {\n      res.x = outsidePoint.x;\n      res.y = outsidePoint.y;\n    }\n    if (R === 0) {\n      res.x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      res.y = outsidePoint.y;\n    }\n\n    log.debug(`abc89 topp/bott calc, Q ${Q}, q ${q}, R ${R}, r ${r}`, res); // cspell: disable-line\n\n    return res;\n  } else {\n    // Intersection on sides of rect\n    if (insidePoint.x < outsidePoint.x) {\n      r = outsidePoint.x - w - x;\n    } else {\n      // r = outsidePoint.x - w - x;\n      r = x - w - outsidePoint.x;\n    }\n    let q = (Q * r) / R;\n    //  OK let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x + dx - w;\n    // OK let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : outsidePoint.x + r;\n    let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x - R + r;\n    // let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : outsidePoint.x + r;\n    let _y = insidePoint.y < outsidePoint.y ? insidePoint.y + q : insidePoint.y - q;\n    log.debug(`sides calc abc89, Q ${Q}, q ${q}, R ${R}, r ${r}`, { _x, _y });\n    if (r === 0) {\n      _x = outsidePoint.x;\n      _y = outsidePoint.y;\n    }\n    if (R === 0) {\n      _x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      _y = outsidePoint.y;\n    }\n\n    return { x: _x, y: _y };\n  }\n};\n/**\n * This function will page a path and node where the last point(s) in the path is inside the node\n * and return an update path ending by the border of the node.\n *\n * @param {Array} _points\n * @param {any} boundaryNode\n * @returns {Array} Points\n */\nconst cutPathAtIntersect = (_points, boundaryNode) => {\n  log.debug('abc88 cutPathAtIntersect', _points, boundaryNode);\n  let points = [];\n  let lastPointOutside = _points[0];\n  let isInside = false;\n  _points.forEach((point) => {\n    // check if point is inside the boundary rect\n    if (!outsideNode(boundaryNode, point) && !isInside) {\n      // First point inside the rect found\n      // Calc the intersection coord between the point and the last point outside the rect\n      const inter = intersection(boundaryNode, lastPointOutside, point);\n\n      // // Check case where the intersection is the same as the last point\n      let pointPresent = false;\n      points.forEach((p) => {\n        pointPresent = pointPresent || (p.x === inter.x && p.y === inter.y);\n      });\n      // // if (!pointPresent) {\n      if (!points.some((e) => e.x === inter.x && e.y === inter.y)) {\n        points.push(inter);\n      }\n\n      isInside = true;\n    } else {\n      // Outside\n      lastPointOutside = point;\n      // points.push(point);\n      if (!isInside) {\n        points.push(point);\n      }\n    }\n  });\n  return points;\n};\n\nexport const insertEdge = function (elem, e, edge, clusterDb, diagramType, graph, id) {\n  let points = edge.points;\n  log.debug('abc88 InsertEdge: edge=', edge, 'e=', e);\n  let pointsHasChanged = false;\n  const tail = graph.node(e.v);\n  var head = graph.node(e.w);\n\n  if (head?.intersect && tail?.intersect) {\n    points = points.slice(1, edge.points.length - 1);\n    points.unshift(tail.intersect(points[0]));\n    points.push(head.intersect(points[points.length - 1]));\n  }\n\n  if (edge.toCluster) {\n    log.debug('to cluster abc88', clusterDb[edge.toCluster]);\n    points = cutPathAtIntersect(edge.points, clusterDb[edge.toCluster].node);\n\n    pointsHasChanged = true;\n  }\n\n  if (edge.fromCluster) {\n    log.debug('from cluster abc88', clusterDb[edge.fromCluster]);\n    points = cutPathAtIntersect(points.reverse(), clusterDb[edge.fromCluster].node).reverse();\n\n    pointsHasChanged = true;\n  }\n\n  // The data for our line\n  const lineData = points.filter((p) => !Number.isNaN(p.y));\n\n  // This is the accessor function we talked about above\n  let curve = curveBasis;\n  // Currently only flowcharts get the curve from the settings, perhaps this should\n  // be expanded to a common setting? Restricting it for now in order not to cause side-effects that\n  // have not been thought through\n  if (edge.curve && (diagramType === 'graph' || diagramType === 'flowchart')) {\n    curve = edge.curve;\n  }\n\n  const { x, y } = getLineFunctionsWithOffset(edge);\n  const lineFunction = line().x(x).y(y).curve(curve);\n\n  // Construct stroke classes based on properties\n  let strokeClasses;\n  switch (edge.thickness) {\n    case 'normal':\n      strokeClasses = 'edge-thickness-normal';\n      break;\n    case 'thick':\n      strokeClasses = 'edge-thickness-thick';\n      break;\n    case 'invisible':\n      strokeClasses = 'edge-thickness-thick';\n      break;\n    default:\n      strokeClasses = '';\n  }\n  switch (edge.pattern) {\n    case 'solid':\n      strokeClasses += ' edge-pattern-solid';\n      break;\n    case 'dotted':\n      strokeClasses += ' edge-pattern-dotted';\n      break;\n    case 'dashed':\n      strokeClasses += ' edge-pattern-dashed';\n      break;\n  }\n\n  const svgPath = elem\n    .append('path')\n    .attr('d', lineFunction(lineData))\n    .attr('id', edge.id)\n    .attr('class', ' ' + strokeClasses + (edge.classes ? ' ' + edge.classes : ''))\n    .attr('style', edge.style);\n\n  // DEBUG code, adds a red circle at each edge coordinate\n  // edge.points.forEach((point) => {\n  //   elem\n  //     .append('circle')\n  //     .style('stroke', 'red')\n  //     .style('fill', 'red')\n  //     .attr('r', 1)\n  //     .attr('cx', point.x)\n  //     .attr('cy', point.y);\n  // });\n\n  let url = '';\n  // // TODO: Can we load this config only from the rendered graph type?\n  if (getConfig().flowchart.arrowMarkerAbsolute || getConfig().state.arrowMarkerAbsolute) {\n    url = getUrl(true);\n  }\n\n  addEdgeMarkers(svgPath, edge, url, id, diagramType);\n\n  let paths = {};\n  if (pointsHasChanged) {\n    paths.updatedPath = points;\n  }\n  paths.originalPath = edge.points;\n  return paths;\n};\n", "import type { SVG } from '../diagram-api/types.js';\nimport { log } from '../logger.js';\nimport type { EdgeData } from '../types.js';\n/**\n * Adds SVG markers to a path element based on the arrow types specified in the edge.\n *\n * @param svgPath - The SVG path element to add markers to.\n * @param edge - The edge data object containing the arrow types.\n * @param url - The URL of the SVG marker definitions.\n * @param id - The ID prefix for the SVG marker definitions.\n * @param diagramType - The type of diagram being rendered.\n */\nexport const addEdgeMarkers = (\n  svgPath: SVG,\n  edge: Pick<EdgeData, 'arrowTypeStart' | 'arrowTypeEnd'>,\n  url: string,\n  id: string,\n  diagramType: string\n) => {\n  if (edge.arrowTypeStart) {\n    addEdgeMarker(svgPath, 'start', edge.arrowTypeStart, url, id, diagramType);\n  }\n  if (edge.arrowTypeEnd) {\n    addEdgeMarker(svgPath, 'end', edge.arrowTypeEnd, url, id, diagramType);\n  }\n};\n\nconst arrowTypesMap = {\n  arrow_cross: 'cross',\n  arrow_point: 'point',\n  arrow_barb: 'barb',\n  arrow_circle: 'circle',\n  aggregation: 'aggregation',\n  extension: 'extension',\n  composition: 'composition',\n  dependency: 'dependency',\n  lollipop: 'lollipop',\n} as const;\n\nconst addEdgeMarker = (\n  svgPath: SVG,\n  position: 'start' | 'end',\n  arrowType: string,\n  url: string,\n  id: string,\n  diagramType: string\n) => {\n  const endMarkerType = arrowTypesMap[arrowType as keyof typeof arrowTypesMap];\n\n  if (!endMarkerType) {\n    log.warn(`Unknown arrow type: ${arrowType}`);\n    return; // unknown arrow type, ignore\n  }\n\n  const suffix = position === 'start' ? 'Start' : 'End';\n  svgPath.attr(`marker-${position}`, `url(${url}#${id}_${diagramType}-${endMarkerType}${suffix})`);\n};\n", "import { select } from 'd3';\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport { evaluate } from '../diagrams/common/common.js';\nimport { log } from '../logger.js';\nimport { getArrowPoints } from './blockArrowHelper.js';\nimport createLabel from './createLabel.js';\nimport intersect from './intersect/index.js';\nimport note from './shapes/note.js';\nimport { insertPolygonShape, labelHelper, updateNodeBounds } from './shapes/util.js';\n\nconst formatClass = (str) => {\n  if (str) {\n    return ' ' + str;\n  }\n  return '';\n};\nconst getClassesFromNode = (node, otherClasses) => {\n  return `${otherClasses ? otherClasses : 'node default'}${formatClass(node.classes)} ${formatClass(\n    node.class\n  )}`;\n};\n\nconst question = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n\n  const points = [\n    { x: s / 2, y: 0 },\n    { x: s, y: -s / 2 },\n    { x: s / 2, y: -s },\n    { x: 0, y: -s / 2 },\n  ];\n\n  log.info('Question main (Circle)');\n\n  const questionElem = insertPolygonShape(shapeSvg, s, s, points);\n  questionElem.attr('style', node.style);\n  updateNodeBounds(node, questionElem);\n\n  node.intersect = function (point) {\n    log.warn('Intersect called');\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst choice = (parent, node) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  const s = 28;\n  const points = [\n    { x: 0, y: s / 2 },\n    { x: s / 2, y: 0 },\n    { x: 0, y: -s / 2 },\n    { x: -s / 2, y: 0 },\n  ];\n\n  const choice = shapeSvg.insert('polygon', ':first-child').attr(\n    'points',\n    points\n      .map(function (d) {\n        return d.x + ',' + d.y;\n      })\n      .join(' ')\n  );\n  // center the circle around its coordinate\n  choice.attr('class', 'state-start').attr('r', 7).attr('width', 28).attr('height', 28);\n  node.width = 28;\n  node.height = 28;\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 14, point);\n  };\n\n  return shapeSvg;\n};\n\nconst hexagon = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n\n  const hex = insertPolygonShape(shapeSvg, w, h, points);\n  hex.attr('style', node.style);\n  updateNodeBounds(node, hex);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst block_arrow = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, undefined, true);\n\n  const f = 2;\n  const h = bbox.height + 2 * node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n\n  const points = getArrowPoints(node.directions, bbox, node);\n\n  const blockArrow = insertPolygonShape(shapeSvg, w, h, points);\n  blockArrow.attr('style', node.style);\n  updateNodeBounds(node, blockArrow);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst rect_left_inv_arrow = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -h / 2, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: -h / 2, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n\n  node.width = w + h;\n  node.height = h;\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst lean_right = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node), true);\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (-2 * h) / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + (2 * h) / 6, y: -h },\n    { x: h / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst lean_left = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (2 * h) / 6, y: 0 },\n    { x: w + h / 6, y: 0 },\n    { x: w - (2 * h) / 6, y: -h },\n    { x: -h / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst trapezoid = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (-2 * h) / 6, y: 0 },\n    { x: w + (2 * h) / 6, y: 0 },\n    { x: w - h / 6, y: -h },\n    { x: h / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst inv_trapezoid = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: h / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + (2 * h) / 6, y: -h },\n    { x: (-2 * h) / 6, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst rect_right_inv_arrow = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w + h / 2, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w + h / 2, y: -h },\n    { x: 0, y: -h },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst cylinder = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = bbox.height + ry + node.padding;\n\n  const shape =\n    'M 0,' +\n    ry +\n    ' a ' +\n    rx +\n    ',' +\n    ry +\n    ' 0,0,0 ' +\n    w +\n    ' 0 a ' +\n    rx +\n    ',' +\n    ry +\n    ' 0,0,0 ' +\n    -w +\n    ' 0 l 0,' +\n    h +\n    ' a ' +\n    rx +\n    ',' +\n    ry +\n    ' 0,0,0 ' +\n    w +\n    ' 0 l 0,' +\n    -h;\n\n  const el = shapeSvg\n    .attr('label-offset-y', ry)\n    .insert('path', ':first-child')\n    .attr('style', node.style)\n    .attr('d', shape)\n    .attr('transform', 'translate(' + -w / 2 + ',' + -(h / 2 + ry) + ')');\n\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const x = pos.x - node.x;\n\n    if (\n      rx != 0 &&\n      (Math.abs(x) < node.width / 2 ||\n        (Math.abs(x) == node.width / 2 && Math.abs(pos.y - node.y) > node.height / 2 - ry))\n    ) {\n      // ellipsis equation: x*x / a*a + y*y / b*b = 1\n      // solve for y to get adjusted value for pos.y\n      let y = ry * ry * (1 - (x * x) / (rx * rx));\n      if (y != 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - node.y > 0) {\n        y = -y;\n      }\n\n      pos.y += y;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n};\n\nconst rect = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    'node ' + node.classes + ' ' + node.class,\n    true\n  );\n\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // console.log('Rect node:', node, 'bbox:', bbox, 'halfPadding:', halfPadding, 'node.padding:', node.padding);\n  // const totalWidth = bbox.width + node.padding * 2;\n  // const totalHeight = bbox.height + node.padding * 2;\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect\n    .attr('class', 'basic label-container')\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', x)\n    .attr('y', y)\n    .attr('width', totalWidth)\n    .attr('height', totalHeight);\n\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete('borders');\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst composite = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    'node ' + node.classes,\n    true\n  );\n\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // const totalWidth = bbox.width + node.padding * 2;\n  // const totalHeight = bbox.height + node.padding * 2;\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect\n    .attr('class', 'basic cluster composite label-container')\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', x)\n    .attr('y', y)\n    .attr('width', totalWidth)\n    .attr('height', totalHeight);\n\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete('borders');\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst labelRect = async (parent, node) => {\n  const { shapeSvg } = await labelHelper(parent, node, 'label', true);\n\n  log.trace('Classes = ', node.class);\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // Hide the rect we are only after the label\n  const totalWidth = 0;\n  const totalHeight = 0;\n  rect.attr('width', totalWidth).attr('height', totalHeight);\n  shapeSvg.attr('class', 'label edgeLabel');\n\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete('borders');\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\n/**\n * @param rect\n * @param borders\n * @param totalWidth\n * @param totalHeight\n */\nfunction applyNodePropertyBorders(rect, borders, totalWidth, totalHeight) {\n  const strokeDashArray = [];\n  const addBorder = (length) => {\n    strokeDashArray.push(length, 0);\n  };\n  const skipBorder = (length) => {\n    strokeDashArray.push(0, length);\n  };\n  if (borders.includes('t')) {\n    log.debug('add top border');\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes('r')) {\n    log.debug('add right border');\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  if (borders.includes('b')) {\n    log.debug('add bottom border');\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes('l')) {\n    log.debug('add left border');\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  rect.attr('stroke-dasharray', strokeDashArray.join(' '));\n}\n\nconst rectWithTitle = async (parent, node) => {\n  // const { shapeSvg, bbox, halfPadding } = labelHelper(parent, node, 'node ' + node.classes);\n\n  let classes;\n  if (!node.classes) {\n    classes = 'node default';\n  } else {\n    classes = 'node ' + node.classes;\n  }\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the title label and insert it after the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n  // const innerRect = shapeSvg.insert('rect');\n  const innerLine = shapeSvg.insert('line');\n\n  const label = shapeSvg.insert('g').attr('class', 'label');\n\n  const text2 = node.labelText.flat ? node.labelText.flat() : node.labelText;\n  // const text2 = typeof text2prim === 'object' ? text2prim[0] : text2prim;\n\n  let title = '';\n  if (typeof text2 === 'object') {\n    title = text2[0];\n  } else {\n    title = text2;\n  }\n  log.info('Label text abc79', title, text2, typeof text2 === 'object');\n\n  const text = label.node().appendChild(await createLabel(title, node.labelStyle, true, true));\n  let bbox = { width: 0, height: 0 };\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  log.info('Text 2', text2);\n  const textRows = text2.slice(1, text2.length);\n  let titleBox = text.getBBox();\n  const descr = label\n    .node()\n    .appendChild(\n      await createLabel(\n        textRows.join ? textRows.join('<br/>') : textRows,\n        node.labelStyle,\n        true,\n        true\n      )\n    );\n\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = descr.children[0];\n    const dv = select(descr);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  // bbox = label.getBBox();\n  // log.info(descr);\n  const halfPadding = node.padding / 2;\n  select(descr).attr(\n    'transform',\n    'translate( ' +\n      // (titleBox.width - bbox.width) / 2 +\n      (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) +\n      ', ' +\n      (titleBox.height + halfPadding + 5) +\n      ')'\n  );\n  select(text).attr(\n    'transform',\n    'translate( ' +\n      // (titleBox.width - bbox.width) / 2 +\n      (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) +\n      ', ' +\n      0 +\n      ')'\n  );\n  // Get the size of the label\n\n  // Bounding box for title and text\n  bbox = label.node().getBBox();\n\n  // Center the label\n  label.attr(\n    'transform',\n    'translate(' + -bbox.width / 2 + ', ' + (-bbox.height / 2 - halfPadding + 3) + ')'\n  );\n\n  rect\n    .attr('class', 'outer title-state')\n    .attr('x', -bbox.width / 2 - halfPadding)\n    .attr('y', -bbox.height / 2 - halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  innerLine\n    .attr('class', 'divider')\n    .attr('x1', -bbox.width / 2 - halfPadding)\n    .attr('x2', bbox.width / 2 + halfPadding)\n    .attr('y1', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding)\n    .attr('y2', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst stadium = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n\n  // add the rect\n  const rect = shapeSvg\n    .insert('rect', ':first-child')\n    .attr('style', node.style)\n    .attr('rx', h / 2)\n    .attr('ry', h / 2)\n    .attr('x', -w / 2)\n    .attr('y', -h / 2)\n    .attr('width', w)\n    .attr('height', h);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst circle = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n  const circle = shapeSvg.insert('circle', ':first-child');\n\n  // center the circle around its coordinate\n  circle\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('r', bbox.width / 2 + halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  log.info('Circle main');\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    log.info('Circle intersect', node, bbox.width / 2 + halfPadding, point);\n    return intersect.circle(node, bbox.width / 2 + halfPadding, point);\n  };\n\n  return shapeSvg;\n};\n\nconst doublecircle = async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n  const gap = 5;\n  const circleGroup = shapeSvg.insert('g', ':first-child');\n  const outerCircle = circleGroup.insert('circle');\n  const innerCircle = circleGroup.insert('circle');\n\n  circleGroup.attr('class', node.class);\n\n  // center the circle around its coordinate\n  outerCircle\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('r', bbox.width / 2 + halfPadding + gap)\n    .attr('width', bbox.width + node.padding + gap * 2)\n    .attr('height', bbox.height + node.padding + gap * 2);\n\n  innerCircle\n    .attr('style', node.style)\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('r', bbox.width / 2 + halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  log.info('DoubleCircle main');\n\n  updateNodeBounds(node, outerCircle);\n\n  node.intersect = function (point) {\n    log.info('DoubleCircle intersect', node, bbox.width / 2 + halfPadding + gap, point);\n    return intersect.circle(node, bbox.width / 2 + halfPadding + gap, point);\n  };\n\n  return shapeSvg;\n};\n\nconst subroutine = async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, undefined),\n    true\n  );\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n    { x: 0, y: 0 },\n    { x: -8, y: 0 },\n    { x: w + 8, y: 0 },\n    { x: w + 8, y: -h },\n    { x: -8, y: -h },\n    { x: -8, y: 0 },\n  ];\n\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr('style', node.style);\n  updateNodeBounds(node, el);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n};\n\nconst start = (parent, node) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n  const circle = shapeSvg.insert('circle', ':first-child');\n\n  // center the circle around its coordinate\n  circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n};\n\nconst forkJoin = (parent, node, dir) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  let width = 70;\n  let height = 10;\n\n  if (dir === 'LR') {\n    width = 10;\n    height = 70;\n  }\n\n  const shape = shapeSvg\n    .append('rect')\n    .attr('x', (-1 * width) / 2)\n    .attr('y', (-1 * height) / 2)\n    .attr('width', width)\n    .attr('height', height)\n    .attr('class', 'fork-join');\n\n  updateNodeBounds(node, shape);\n  node.height = node.height + node.padding / 2;\n  node.width = node.width + node.padding / 2;\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst end = (parent, node) => {\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n  const innerCircle = shapeSvg.insert('circle', ':first-child');\n  const circle = shapeSvg.insert('circle', ':first-child');\n\n  circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n\n  innerCircle.attr('class', 'state-end').attr('r', 5).attr('width', 10).attr('height', 10);\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n};\n\nconst class_box = async (parent, node) => {\n  const halfPadding = node.padding / 2;\n  const rowPadding = 4;\n  const lineHeight = 8;\n\n  let classes;\n  if (!node.classes) {\n    classes = 'node default';\n  } else {\n    classes = 'node ' + node.classes;\n  }\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the title label and insert it after the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n  const topLine = shapeSvg.insert('line');\n  const bottomLine = shapeSvg.insert('line');\n  let maxWidth = 0;\n  let maxHeight = rowPadding;\n\n  const labelContainer = shapeSvg.insert('g').attr('class', 'label');\n  let verticalPos = 0;\n  const hasInterface = node.classData.annotations?.[0];\n\n  // 1. Create the labels\n  const interfaceLabelText = node.classData.annotations[0]\n    ? '«' + node.classData.annotations[0] + '»'\n    : '';\n  const interfaceLabel = labelContainer\n    .node()\n    .appendChild(await createLabel(interfaceLabelText, node.labelStyle, true, true));\n  let interfaceBBox = interfaceLabel.getBBox();\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = interfaceLabel.children[0];\n    const dv = select(interfaceLabel);\n    interfaceBBox = div.getBoundingClientRect();\n    dv.attr('width', interfaceBBox.width);\n    dv.attr('height', interfaceBBox.height);\n  }\n  if (node.classData.annotations[0]) {\n    maxHeight += interfaceBBox.height + rowPadding;\n    maxWidth += interfaceBBox.width;\n  }\n\n  let classTitleString = node.classData.label;\n\n  if (node.classData.type !== undefined && node.classData.type !== '') {\n    if (getConfig().flowchart.htmlLabels) {\n      classTitleString += '&lt;' + node.classData.type + '&gt;';\n    } else {\n      classTitleString += '<' + node.classData.type + '>';\n    }\n  }\n  const classTitleLabel = labelContainer\n    .node()\n    .appendChild(await createLabel(classTitleString, node.labelStyle, true, true));\n  select(classTitleLabel).attr('class', 'classTitle');\n  let classTitleBBox = classTitleLabel.getBBox();\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    const div = classTitleLabel.children[0];\n    const dv = select(classTitleLabel);\n    classTitleBBox = div.getBoundingClientRect();\n    dv.attr('width', classTitleBBox.width);\n    dv.attr('height', classTitleBBox.height);\n  }\n  maxHeight += classTitleBBox.height + rowPadding;\n  if (classTitleBBox.width > maxWidth) {\n    maxWidth = classTitleBBox.width;\n  }\n  const classAttributes = [];\n  node.classData.members.forEach(async (member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let parsedText = parsedInfo.displayText;\n    if (getConfig().flowchart.htmlLabels) {\n      parsedText = parsedText.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n    }\n    const lbl = labelContainer\n      .node()\n      .appendChild(\n        await createLabel(\n          parsedText,\n          parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n          true,\n          true\n        )\n      );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr('width', bbox.width);\n      dv.attr('height', bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n    classAttributes.push(lbl);\n  });\n\n  maxHeight += lineHeight;\n\n  const classMethods = [];\n  node.classData.methods.forEach(async (member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let displayText = parsedInfo.displayText;\n    if (getConfig().flowchart.htmlLabels) {\n      displayText = displayText.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n    }\n    const lbl = labelContainer\n      .node()\n      .appendChild(\n        await createLabel(\n          displayText,\n          parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n          true,\n          true\n        )\n      );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr('width', bbox.width);\n      dv.attr('height', bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n\n    classMethods.push(lbl);\n  });\n\n  maxHeight += lineHeight;\n\n  // 2. Position the labels\n\n  // position the interface label\n  if (hasInterface) {\n    let diffX = (maxWidth - interfaceBBox.width) / 2;\n    select(interfaceLabel).attr(\n      'transform',\n      'translate( ' + ((-1 * maxWidth) / 2 + diffX) + ', ' + (-1 * maxHeight) / 2 + ')'\n    );\n    verticalPos = interfaceBBox.height + rowPadding;\n  }\n  // Position the class title label\n  let diffX = (maxWidth - classTitleBBox.width) / 2;\n  select(classTitleLabel).attr(\n    'transform',\n    'translate( ' +\n      ((-1 * maxWidth) / 2 + diffX) +\n      ', ' +\n      ((-1 * maxHeight) / 2 + verticalPos) +\n      ')'\n  );\n  verticalPos += classTitleBBox.height + rowPadding;\n\n  topLine\n    .attr('class', 'divider')\n    .attr('x1', -maxWidth / 2 - halfPadding)\n    .attr('x2', maxWidth / 2 + halfPadding)\n    .attr('y1', -maxHeight / 2 - halfPadding + lineHeight + verticalPos)\n    .attr('y2', -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n\n  verticalPos += lineHeight;\n\n  classAttributes.forEach((lbl) => {\n    select(lbl).attr(\n      'transform',\n      'translate( ' +\n        -maxWidth / 2 +\n        ', ' +\n        ((-1 * maxHeight) / 2 + verticalPos + lineHeight / 2) +\n        ')'\n    );\n    //get the height of the bounding box of each member if exists\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n\n  verticalPos += lineHeight;\n  bottomLine\n    .attr('class', 'divider')\n    .attr('x1', -maxWidth / 2 - halfPadding)\n    .attr('x2', maxWidth / 2 + halfPadding)\n    .attr('y1', -maxHeight / 2 - halfPadding + lineHeight + verticalPos)\n    .attr('y2', -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n\n  verticalPos += lineHeight;\n\n  classMethods.forEach((lbl) => {\n    select(lbl).attr(\n      'transform',\n      'translate( ' + -maxWidth / 2 + ', ' + ((-1 * maxHeight) / 2 + verticalPos) + ')'\n    );\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n\n  rect\n    .attr('style', node.style)\n    .attr('class', 'outer title-state')\n    .attr('x', -maxWidth / 2 - halfPadding)\n    .attr('y', -(maxHeight / 2) - halfPadding)\n    .attr('width', maxWidth + node.padding)\n    .attr('height', maxHeight + node.padding);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nconst shapes = {\n  rhombus: question,\n  composite,\n  question,\n  rect,\n  labelRect,\n  rectWithTitle,\n  choice,\n  circle,\n  doublecircle,\n  stadium,\n  hexagon,\n  block_arrow,\n  rect_left_inv_arrow,\n  lean_right,\n  lean_left,\n  trapezoid,\n  inv_trapezoid,\n  rect_right_inv_arrow,\n  cylinder,\n  start,\n  end,\n  note,\n  subroutine,\n  fork: forkJoin,\n  join: forkJoin,\n  class_box,\n};\n\nlet nodeElems = {};\n\nexport const insertNode = async (elem, node, renderOptions) => {\n  let newEl;\n  let el;\n\n  // Add link when appropriate\n  if (node.link) {\n    let target;\n    if (getConfig().securityLevel === 'sandbox') {\n      target = '_top';\n    } else if (node.linkTarget) {\n      target = node.linkTarget || '_blank';\n    }\n    newEl = elem.insert('svg:a').attr('xlink:href', node.link).attr('target', target);\n    el = await shapes[node.shape](newEl, node, renderOptions);\n  } else {\n    el = await shapes[node.shape](elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr('title', node.tooltip);\n  }\n  if (node.class) {\n    el.attr('class', 'node default ' + node.class);\n  }\n\n  nodeElems[node.id] = newEl;\n\n  if (node.haveCallback) {\n    nodeElems[node.id].attr('class', nodeElems[node.id].attr('class') + ' clickable');\n  }\n  return newEl;\n};\nexport const setNodeElem = (elem, node) => {\n  nodeElems[node.id] = elem;\n};\nexport const clear = () => {\n  nodeElems = {};\n};\n\nexport const positionNode = (node) => {\n  const el = nodeElems[node.id];\n  log.trace(\n    'Transforming node',\n    node.diff,\n    node,\n    'translate(' + (node.x - node.width / 2 - 5) + ', ' + node.width / 2 + ')'\n  );\n  const padding = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\n      'transform',\n      'translate(' +\n        (node.x + diff - node.width / 2) +\n        ', ' +\n        (node.y - node.height / 2 - padding) +\n        ')'\n    );\n  } else {\n    el.attr('transform', 'translate(' + node.x + ', ' + node.y + ')');\n  }\n  return diff;\n};\n", "import type { Direction } from '../../src/diagrams/block/blockTypes.js';\n\nconst expandAndDeduplicateDirections = (directions: Direction[]) => {\n  const uniqueDirections = new Set();\n\n  for (const direction of directions) {\n    switch (direction) {\n      case 'x':\n        uniqueDirections.add('right');\n        uniqueDirections.add('left');\n        break;\n      case 'y':\n        uniqueDirections.add('up');\n        uniqueDirections.add('down');\n        break;\n      default:\n        uniqueDirections.add(direction);\n        break;\n    }\n  }\n\n  return uniqueDirections;\n};\nexport const getArrowPoints = (\n  duplicatedDirections: Direction[],\n  bbox: { width: number; height: number },\n  node: any\n) => {\n  // Expand and deduplicate the provided directions.\n  // for instance: x, right => right, left\n  const directions = expandAndDeduplicateDirections(duplicatedDirections);\n\n  // Factor to divide height for some calculations.\n  const f = 2;\n\n  // Calculated height of the bounding box, accounting for node padding.\n  const height = bbox.height + 2 * node.padding;\n  // Midpoint calculation based on height.\n  const midpoint = height / f;\n  // Calculated width of the bounding box, accounting for additional width and node padding.\n  const width = bbox.width + 2 * midpoint + node.padding;\n  // Padding to use, half of the node padding.\n  const padding = node.padding / 2;\n\n  if (\n    directions.has('right') &&\n    directions.has('left') &&\n    directions.has('up') &&\n    directions.has('down')\n  ) {\n    // SQUARE\n    return [\n      // Bottom\n      { x: 0, y: 0 },\n      { x: midpoint, y: 0 },\n      { x: width / 2, y: 2 * padding },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: 0 },\n\n      // Right\n      { x: width, y: -height / 3 },\n      { x: width + 2 * padding, y: -height / 2 },\n      { x: width, y: (-2 * height) / 3 },\n      { x: width, y: -height },\n\n      // Top\n      { x: width - midpoint, y: -height },\n      { x: width / 2, y: -height - 2 * padding },\n      { x: midpoint, y: -height },\n\n      // Left\n      { x: 0, y: -height },\n      { x: 0, y: (-2 * height) / 3 },\n      { x: -2 * padding, y: -height / 2 },\n      { x: 0, y: -height / 3 },\n    ];\n  }\n  if (directions.has('right') && directions.has('left') && directions.has('up')) {\n    // RECTANGLE_VERTICAL (Top Open)\n    return [\n      { x: midpoint, y: 0 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 },\n    ];\n  }\n  if (directions.has('right') && directions.has('left') && directions.has('down')) {\n    // RECTANGLE_VERTICAL (Bottom Open)\n    return [\n      { x: 0, y: 0 },\n      { x: midpoint, y: -height },\n      { x: width - midpoint, y: -height },\n      { x: width, y: 0 },\n    ];\n  }\n  if (directions.has('right') && directions.has('up') && directions.has('down')) {\n    // RECTANGLE_HORIZONTAL (Right Open)\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: width, y: -height + midpoint },\n      { x: 0, y: -height },\n    ];\n  }\n  if (directions.has('left') && directions.has('up') && directions.has('down')) {\n    // RECTANGLE_HORIZONTAL (Left Open)\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: 0, y: -height + midpoint },\n      { x: width, y: -height },\n    ];\n  }\n  if (directions.has('right') && directions.has('left')) {\n    // HORIZONTAL_LINE\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding },\n      { x: width - midpoint, y: -padding },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding },\n      { x: midpoint, y: -height + padding },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 },\n    ];\n  }\n  if (directions.has('up') && directions.has('down')) {\n    // VERTICAL_LINE\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding },\n      { x: midpoint, y: -padding },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding },\n      { x: 0, y: -height + padding },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding },\n      { x: width - midpoint, y: -padding },\n      { x: width, y: -padding },\n    ];\n  }\n  if (directions.has('right') && directions.has('up')) {\n    // ANGLE_RT\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: 0, y: -height },\n    ];\n  }\n  if (directions.has('right') && directions.has('down')) {\n    // ANGLE_RB\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: 0 },\n      { x: 0, y: -height },\n    ];\n  }\n  if (directions.has('left') && directions.has('up')) {\n    // ANGLE_LT\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: width, y: -height },\n    ];\n  }\n  if (directions.has('left') && directions.has('down')) {\n    // ANGLE_LB\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: 0 },\n      { x: width, y: -height },\n    ];\n  }\n  if (directions.has('right')) {\n    // ARROW_RIGHT\n    return [\n      { x: midpoint, y: -padding },\n      { x: midpoint, y: -padding },\n      { x: width - midpoint, y: -padding },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding },\n      // top left corner of arrow\n      { x: midpoint, y: -height + padding },\n      { x: midpoint, y: -height + padding },\n    ];\n  }\n  if (directions.has('left')) {\n    // ARROW_LEFT\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding },\n      // Two points, the right corners\n      { x: width - midpoint, y: -padding },\n      { x: width - midpoint, y: -height + padding },\n      { x: midpoint, y: -height + padding },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 },\n    ];\n  }\n  if (directions.has('up')) {\n    // ARROW_TOP\n    return [\n      // Bottom center\n      { x: midpoint, y: -padding },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding },\n      { x: 0, y: -height + padding },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding },\n      { x: width - midpoint, y: -padding },\n    ];\n  }\n  if (directions.has('down')) {\n    // ARROW_BOTTOM\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding },\n      { x: midpoint, y: -padding },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding },\n      { x: width - midpoint, y: -height + padding },\n      { x: width - midpoint, y: -padding },\n      { x: width, y: -padding },\n    ];\n  }\n\n  // POINT\n  return [{ x: 0, y: 0 }];\n};\n", "/**\n * @param node\n * @param point\n */\nfunction intersectNode(node, point) {\n  // console.info('Intersect Node');\n  return node.intersect(point);\n}\n\nexport default intersectNode;\n", "/**\n * @param node\n * @param rx\n * @param ry\n * @param point\n */\nfunction intersectEllipse(node, rx, ry, point) {\n  // Formulae from: https://mathworld.wolfram.com/Ellipse-LineIntersection.html\n\n  var cx = node.x;\n  var cy = node.y;\n\n  var px = cx - point.x;\n  var py = cy - point.y;\n\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n\n  var dx = Math.abs((rx * ry * px) / det);\n  if (point.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs((rx * ry * py) / det);\n  if (point.y < cy) {\n    dy = -dy;\n  }\n\n  return { x: cx + dx, y: cy + dy };\n}\n\nexport default intersectEllipse;\n", "import intersectEllipse from './intersect-ellipse.js';\n\n/**\n * @param node\n * @param rx\n * @param point\n */\nfunction intersectCircle(node, rx, point) {\n  return intersectEllipse(node, rx, rx, point);\n}\n\nexport default intersectCircle;\n", "/**\n * Returns the point at which two lines, p and q, intersect or returns undefined if they do not intersect.\n *\n * @param p1\n * @param p2\n * @param q1\n * @param q2\n */\nfunction intersectLine(p1, p2, q1, q2) {\n  // Algorithm from <PERSON><PERSON><PERSON>, (ed.) Graphics Gems, No 2, <PERSON>, 1994,\n  // p7 and p473.\n\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n\n  // Compute a1, b1, c1, where line joining points 1 and 2 is F(x,y) = a1 x +\n  // b1 y + c1 = 0.\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n\n  // Compute r3 and r4.\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n\n  // Check signs of r3 and r4. If both point 3 and point 4 lie on\n  // same side of line 1, the line segments do not intersect.\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Compute a2, b2, c2 where line joining points 3 and 4 is G(x,y) = a2 x + b2 y + c2 = 0\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n\n  // Compute r1 and r2\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n\n  // Check signs of r1 and r2. If both point 1 and point 2 lie\n  // on same side of second line segment, the line segments do\n  // not intersect.\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Line segments intersect: compute intersection point.\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return /*COLLINEAR*/;\n  }\n\n  offset = Math.abs(denom / 2);\n\n  // The denom/2 is to get rounding instead of truncating. It\n  // is added or subtracted to the numerator, depending upon the\n  // sign of the numerator.\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  return { x: x, y: y };\n}\n\n/**\n * @param r1\n * @param r2\n */\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n\nexport default intersectLine;\n", "/* eslint \"no-console\": off */\n\nimport intersectLine from './intersect-line.js';\n\nexport default intersectPolygon;\n\n/**\n * Returns the point ({x, y}) at which the point argument intersects with the node argument assuming\n * that it has the shape specified by polygon.\n *\n * @param node\n * @param polyPoints\n * @param point\n */\nfunction intersectPolygon(node, polyPoints, point) {\n  var x1 = node.x;\n  var y1 = node.y;\n\n  var intersections = [];\n\n  var minX = Number.POSITIVE_INFINITY;\n  var minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === 'function') {\n    polyPoints.forEach(function (entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n\n  var left = x1 - node.width / 2 - minX;\n  var top = y1 - node.height / 2 - minY;\n\n  for (var i = 0; i < polyPoints.length; i++) {\n    var p1 = polyPoints[i];\n    var p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    var intersect = intersectLine(\n      node,\n      point,\n      { x: left + p1.x, y: top + p1.y },\n      { x: left + p2.x, y: top + p2.y }\n    );\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n\n  if (!intersections.length) {\n    // console.log('NO INTERSECTION FOUND, RETURN NODE CENTER', node);\n    return node;\n  }\n\n  if (intersections.length > 1) {\n    // More intersections, find the one nearest to edge end point\n    intersections.sort(function (p, q) {\n      var pdx = p.x - point.x;\n      var pdy = p.y - point.y;\n      var distp = Math.sqrt(pdx * pdx + pdy * pdy);\n\n      var qdx = q.x - point.x;\n      var qdy = q.y - point.y;\n      var distq = Math.sqrt(qdx * qdx + qdy * qdy);\n\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n", "const intersectRect = (node, point) => {\n  var x = node.x;\n  var y = node.y;\n\n  // Rectangle intersection algorithm from:\n  // https://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n};\n\nexport default intersectRect;\n", "/*\n * Borrowed with love from dagre-d3. Many thanks to c<PERSON><PERSON><PERSON>!\n */\n\nimport node from './intersect-node.js';\nimport circle from './intersect-circle.js';\nimport ellipse from './intersect-ellipse.js';\nimport polygon from './intersect-polygon.js';\nimport rect from './intersect-rect.js';\n\nexport default {\n  node,\n  circle,\n  ellipse,\n  polygon,\n  rect,\n};\n", "import createLabel from '../createLabel.js';\nimport { createText } from '../../rendering-util/createText.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { select } from 'd3';\nimport { evaluate, sanitizeText } from '../../diagrams/common/common.js';\nimport { decodeEntities } from '../../utils.js';\n\nexport const labelHelper = async (parent, node, _classes, isNode) => {\n  const config = getConfig();\n  let classes;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(config.flowchart.htmlLabels);\n  if (!_classes) {\n    classes = 'node default';\n  } else {\n    classes = _classes;\n  }\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the label and insert it after the rect\n  const label = shapeSvg.insert('g').attr('class', 'label').attr('style', node.labelStyle);\n\n  // Replace labelText with default value if undefined\n  let labelText;\n  if (node.labelText === undefined) {\n    labelText = '';\n  } else {\n    labelText = typeof node.labelText === 'string' ? node.labelText : node.labelText[0];\n  }\n\n  const textNode = label.node();\n  let text;\n  if (node.labelType === 'markdown') {\n    // text = textNode;\n    text = createText(\n      label,\n      sanitizeText(decodeEntities(labelText), config),\n      {\n        useHtmlLabels,\n        width: node.width || config.flowchart.wrappingWidth,\n        classes: 'markdown-node-label',\n      },\n      config\n    );\n  } else {\n    text = textNode.appendChild(\n      await createLabel(\n        sanitizeText(decodeEntities(labelText), config),\n        node.labelStyle,\n        false,\n        isNode\n      )\n    );\n  }\n  // Get the size of the label\n  let bbox = text.getBBox();\n  const halfPadding = node.padding / 2;\n\n  if (evaluate(config.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n\n    // if there are images, need to wait for them to load before getting the bounding box\n    const images = div.getElementsByTagName('img');\n    if (images) {\n      const noImgText = labelText.replace(/<img[^>]*>/g, '').trim() === '';\n\n      await Promise.all(\n        [...images].map(\n          (img) =>\n            new Promise((res) => {\n              /**\n               *\n               */\n              function setupImage() {\n                img.style.display = 'flex';\n                img.style.flexDirection = 'column';\n\n                if (noImgText) {\n                  // default size if no text\n                  const bodyFontSize = config.fontSize\n                    ? config.fontSize\n                    : window.getComputedStyle(document.body).fontSize;\n                  const enlargingFactor = 5;\n                  const width = parseInt(bodyFontSize, 10) * enlargingFactor + 'px';\n                  img.style.minWidth = width;\n                  img.style.maxWidth = width;\n                } else {\n                  img.style.width = '100%';\n                }\n                res(img);\n              }\n              setTimeout(() => {\n                if (img.complete) {\n                  setupImage();\n                }\n              });\n              img.addEventListener('error', setupImage);\n              img.addEventListener('load', setupImage);\n            })\n        )\n      );\n    }\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center the label\n  if (useHtmlLabels) {\n    label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  } else {\n    label.attr('transform', 'translate(' + 0 + ', ' + -bbox.height / 2 + ')');\n  }\n  if (node.centerLabel) {\n    label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  }\n  label.insert('rect', ':first-child');\n\n  return { shapeSvg, bbox, halfPadding, label };\n};\n\nexport const updateNodeBounds = (node, element) => {\n  const bbox = element.node().getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n};\n\n/**\n * @param parent\n * @param w\n * @param h\n * @param points\n */\nexport function insertPolygonShape(parent, w, h, points) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('class', 'label-container')\n    .attr('transform', 'translate(' + -w / 2 + ',' + h / 2 + ')');\n}\n", "import { updateNodeBounds, labelHelper } from './util.js';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport intersect from '../intersect/index.js';\n\nconst note = async (parent, node) => {\n  const useHtmlLabels = node.useHtmlLabels || getConfig().flowchart.htmlLabels;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    'node ' + node.classes,\n    true\n  );\n\n  log.info('Classes = ', node.classes);\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  rect\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', -bbox.width / 2 - halfPadding)\n    .attr('y', -bbox.height / 2 - halfPadding)\n    .attr('width', bbox.width + node.padding)\n    .attr('height', bbox.height + node.padding);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n};\n\nexport default note;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: jison doesn't export types\nimport parser from './parser/block.jison';\nimport db from './blockDB.js';\nimport flowStyles from './styles.js';\nimport renderer from './blockRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles: flowStyles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE;AACjU,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,cAAa,GAAE,aAAY,GAAE,MAAK,GAAE,aAAY,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,GAAE,qBAAoB,IAAG,YAAW,IAAG,QAAO,IAAG,aAAY,IAAG,QAAO,IAAG,QAAO,IAAG,cAAa,IAAG,cAAa,IAAG,OAAM,IAAG,iBAAgB,IAAG,oBAAmB,IAAG,eAAc,IAAG,kBAAiB,IAAG,qBAAoB,IAAG,qBAAoB,IAAG,kBAAiB,IAAG,QAAO,IAAG,QAAO,IAAG,WAAU,IAAG,YAAW,IAAG,OAAM,IAAG,SAAQ,IAAG,WAAU,IAAG,mBAAkB,IAAG,WAAU,IAAG,OAAM,IAAG,eAAc,IAAG,aAAY,IAAG,qBAAoB,IAAG,mBAAkB,IAAG,YAAW,IAAG,eAAc,IAAG,sBAAqB,IAAG,WAAU,IAAG,SAAQ,IAAG,mBAAkB,IAAG,cAAa,IAAG,SAAQ,IAAG,oBAAmB,IAAG,yBAAwB,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACzvB,YAAY,EAAC,GAAE,SAAQ,GAAE,aAAY,GAAE,MAAK,GAAE,SAAQ,GAAE,OAAM,IAAG,qBAAoB,IAAG,QAAO,IAAG,cAAa,IAAG,cAAa,IAAG,OAAM,IAAG,eAAc,IAAG,QAAO,IAAG,WAAU,IAAG,YAAW,IAAG,OAAM,IAAG,SAAQ,IAAG,WAAU,IAAG,OAAM,IAAG,eAAc,IAAG,aAAY,IAAG,qBAAoB,IAAG,mBAAkB,IAAG,YAAW,IAAG,eAAc,IAAG,sBAAqB,IAAG,WAAU,IAAG,SAAQ,IAAG,mBAAkB,IAAG,cAAa,IAAG,SAAQ,IAAG,oBAAmB,IAAG,wBAAuB;AAAA,IACne,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACnR,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,uBAAuB;AAC5C;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,0BAA0B;AAC/C;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,wBAAwB;AAC7C;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,qBAAqB,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,aAAa,GAAG,KAAG,CAAC,CAAC;AAC9E;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,UAAU;AAC/B;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,WAAW;AAChC;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,WAAW;AAChC;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,YAAY;AACjC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,qBAAqB,GAAG,EAAE,CAAC;AAAG,iBAAO,GAAG,EAAE,EAAE,WAAW,WAAS,KAAK,IAAI,GAAG,EAAE,IAAE,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACtH;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,wBAAwB,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC;AAC1F;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,gBAAgB,GAAG,EAAE,GAAG,MAAM;AAAG,eAAK,IAAE,EAAC,aAAa,GAAG,EAAE,GAAG,OAAM,GAAE;AAC5F;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,sBAAsB,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,EAAC,aAAa,GAAG,EAAE,GAAG,OAAM,GAAG,KAAG,CAAC,EAAC;AACpH;AAAA,QACA,KAAK;AACJ,gBAAM,MAAI,SAAS,GAAG,EAAE,CAAC;AAAG,gBAAM,UAAU,GAAG,WAAW;AAAG,eAAK,IAAI,EAAE,IAAI,SAAS,MAAK,SAAS,OAAM,IAAI,OAAO,KAAK,UAAU,CAAC,EAAE;AACvI;AAAA,QACA,KAAK;AAED,aAAG,UAAU,EAAE,MAAM,oCAAoC,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,cAAa,GAAG,KAAG,CAAC,EAAE,WAAW;AACtH,gBAAM,WAAW,GAAG,kBAAkB,GAAG,KAAG,CAAC,EAAE,WAAW;AAC1D,eAAK,IAAI;AAAA,YACP,EAAC,IAAI,GAAG,KAAG,CAAC,EAAE,IAAI,OAAO,GAAG,KAAG,CAAC,EAAE,OAAO,MAAK,GAAG,KAAG,CAAC,EAAE,MAAM,YAAY,GAAG,KAAG,CAAC,EAAE,WAAU;AAAA,YAC5F,EAAC,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,MAAM,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,KAAG,CAAC,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,KAAG,CAAC,EAAE,OAAO,MAAM,QAAQ,YAAY,GAAG,EAAE,EAAE,YAAY,cAAc,UAAU,gBAAgB,aAAa;AAAA,YACjM,EAAC,IAAI,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,EAAE,EAAE,OAAO,MAAM,GAAG,aAAa,GAAG,EAAE,EAAE,OAAO,GAAG,YAAY,GAAG,EAAE,EAAE,WAAU;AAAA,UACzG;AAEN;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,0CAA0C,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,EAAC,IAAI,GAAG,KAAG,CAAC,EAAE,IAAI,OAAO,GAAG,KAAG,CAAC,EAAE,OAAO,MAAM,GAAG,aAAa,GAAG,KAAG,CAAC,EAAE,OAAO,GAAG,YAAY,GAAG,KAAG,CAAC,EAAE,YAAY,gBAAgB,SAAS,GAAG,EAAE,GAAE,EAAE,EAAC;AAClP;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,+BAA+B,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,EAAC,IAAI,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,EAAE,EAAE,OAAO,MAAM,GAAG,aAAa,GAAG,EAAE,EAAE,OAAO,GAAG,YAAY,GAAG,EAAE,EAAE,YAAY,gBAAe,EAAC;AAClM;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,UAAU,OAAM,OAAK,IAAI;AAAG,aAAG,UAAU,EAAE,MAAM,aAAa,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,EAAC,MAAM,kBAAkB,SAAS,GAAG,EAAE,MAAM,SAAO,KAAG,SAAS,GAAG,EAAE,CAAC,EAAE;AAC9K;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,+BAA+B,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAAG,gBAAM,MAAM,GAAG,WAAW;AAAG,eAAK,IAAI,EAAE,GAAG,GAAG,KAAG,CAAC,GAAG,MAAK,aAAa,UAAU,GAAG,KAAG,CAAC,EAAE;AACpK;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,2BAA2B,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAG,gBAAM,KAAK,GAAG,WAAW;AAAG,eAAK,IAAI,EAAE,IAAI,MAAK,aAAa,OAAM,IAAI,UAAU,GAAG,KAAG,CAAC,EAAE;AACxK;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,oCAAoC,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE;AACzF;AAAA,QACA,KAAK;AAED,aAAG,UAAU,EAAE,MAAM,oDAAoD,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AACzF,eAAK,IAAI,EAAE,IAAI,GAAG,KAAG,CAAC,GAAG,OAAO,GAAG,EAAE,EAAE,OAAO,SAAS,GAAG,EAAE,EAAE,SAAS,YAAY,GAAG,EAAE,EAAE,WAAW;AAEzG;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,mBAAmB,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AAClE;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,mBAAmB,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC;AAC7F;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,2BAA2B,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,EAAE,SAAS,GAAG,KAAG,CAAC,IAAI,GAAG,EAAE,GAAG,OAAO,GAAG,KAAG,CAAC,EAAE;AACrI;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,uCAAuC,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,QAAO,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,EAAE,SAAS,GAAG,KAAG,CAAC,IAAI,GAAG,EAAE,GAAG,OAAO,GAAG,KAAG,CAAC,GAAG,YAAY,GAAG,KAAG,CAAC,EAAC;AACvL;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AAER,eAAK,IAAI,EAAE,MAAM,YAAY,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,KAAK,GAAG,EAAE,EAAE,KAAK,EAAE;AAE3E;AAAA,QACA,KAAK;AAGG,eAAK,IAAE,EAAE,MAAM,cAAc,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,YAAY,GAAG,EAAE,EAAE,KAAK,EAAE;AAEpF;AAAA,QACA,KAAK;AAEG,eAAK,IAAE,EAAE,MAAM,eAAe,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,WAAW,GAAG,EAAE,EAAE,KAAK,EAAE;AAEpF;AAAA,MACA;AAAA,IACA,GA5Ge;AAAA,IA6Gf,OAAO,CAAC,EAAC,GAAE,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,GAAE,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,GAAE,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,GAAE,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC;AAAA,IAC5qC,gBAAgB,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC;AAAA,IACvD,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,CAAC;AAAA,MACV,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,eAAG,UAAU,EAAE,MAAM,mBAAmB;AAAG,mBAAO;AAC1D;AAAA,UACA,KAAK;AAAG,eAAG,UAAU,EAAE,MAAM,gBAAgB;AAAG,mBAAO;AACvD;AAAA,UACA,KAAK;AAAG,eAAG,UAAU,EAAE,MAAM,mBAAmB;AAAG,mBAAO;AAC1D;AAAA,UACA,KAAK;AAAG,eAAG,UAAU,EAAE,MAAM,KAAK,IAAI,MAAM;AAC5C;AAAA,UACA,KAAK;AAAE,eAAG,UAAU,EAAE,MAAM,KAAK,IAAI,MAAM;AAC3C;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,gBAAI,SAAO;AAAI,mBAAO;AAC9B;AAAA,UACA,KAAK;AAAG,gBAAI,SAAS,IAAI,OAAO,QAAQ,cAAa,EAAE;AAAG,eAAG,UAAU,EAAE,MAAM,iBAAiB,IAAI,MAAM;AAAG,mBAAO;AACpH;AAAA,UACA,KAAK;AAAG,iBAAK,UAAU,WAAW;AAClC;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,iBAAK,UAAU,QAAQ;AAC/B;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,qBAAqB,IAAI,MAAM;AAAE,iBAAK,SAAS;AAC7E;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,iBAAiB,IAAI,MAAM;AAAG,mBAAO;AACnE;AAAA,UACA,KAAK;AAAK,gBAAI,SAAS,IAAI,OAAO,QAAQ,WAAU,EAAE;AAAE,eAAG,UAAU,EAAE,MAAM,mBAAmB,IAAI,MAAM;AAAG,mBAAO;AACpH;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS;AAAK,eAAG,UAAU,EAAE,MAAM,iBAAiB,IAAI,MAAM;AAAG,mBAAO;AACrF;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,UAAU;AAAG,mBAAO;AAC5C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,YAAY;AAAG,mBAAO;AAC/D;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,YAAY;AAAG,mBAAO;AAC/D;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,OAAO;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,aAAa;AAAG,mBAAO;AAChE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,aAAa;AAAG,mBAAO;AAC/C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,kBAAkB;AAAG,mBAAO;AACrE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,WAAW;AAAE,mBAAO;AAC5C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,WAAW;AAAE,mBAAO;AAC5C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,qBAAqB;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,QAAQ;AAAI,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,QAAQ;AAAI,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,QAAQ;AAAG,mBAAO;AAChE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,UAAU;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACzE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,UAAU;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACzE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,UAAU;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACzE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACxE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,UAAU;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AAC1E;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACxE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACxE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACxE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACxE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,UAAU;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACzE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACxE;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,SAAS;AAAG,iBAAK,UAAU,MAAM;AAAE,mBAAO;AACxE;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,aAAa;AAAE,eAAG,UAAU,EAAE,MAAM,eAAe;AAAE,mBAAO;AACpF;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,gBAAgB,IAAI,MAAM;AAAE,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,YAAY,IAAI,MAAM;AAAE,mBAAO;AAC7D;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,WAAW;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,WAAW;AACnC;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,sBAAsB;AAAE,iBAAK,UAAU,QAAQ;AAC7E;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,0BAA0B;AAAE,iBAAK,UAAU,QAAQ;AACjF;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,oBAAoB,IAAI,MAAM;AAAG,mBAAO;AACtE;AAAA,UACA,KAAK;AAAG,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,iBAAK,SAAS;AAC1D;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,YAAY;AAAI,iBAAK,UAAU,WAAW;AACxE;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAAG,eAAG,UAAU,EAAE,MAAM,qBAAoB,IAAI,MAAM;AAAE,mBAAO;AACnH;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAAG,eAAG,UAAU,EAAE,MAAM,eAAc,IAAI,MAAM;AAAE,mBAAO;AAC7G;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAAG,eAAG,UAAU,EAAE,MAAM,YAAW,IAAI,MAAM;AAAG,mBAAO;AAC3G;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAAG,eAAG,UAAU,EAAE,MAAM,YAAW,IAAI,MAAM;AAAG,mBAAO;AAC3G;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAAG,eAAG,UAAU,EAAE,MAAM,aAAY,IAAI,MAAM;AAAG,mBAAO;AAC5G;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAAG,eAAG,UAAU,EAAE,MAAM,eAAc,IAAI,MAAM;AAAG,mBAAO;AAC9G;AAAA,UACA,KAAK;AAAI,gBAAI,SAAO;AAAK,eAAG,UAAU,EAAE,MAAM,wBAAuB,IAAI,MAAM;AAAE,iBAAK,SAAS;AAAE,iBAAK,SAAS;AAAE,mBAAO;AACxH;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,aAAa,MAAI,IAAI,SAAO,GAAG;AAAG,mBAAO;AACvE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,aAAa,IAAI,MAAM;AAAG,mBAAO;AAC/D;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,aAAa,IAAI,MAAM;AAAG,mBAAO;AAC/D;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,aAAa,IAAI,MAAM;AAAG,mBAAO;AAC/D;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,mBAAmB,IAAI,MAAM;AAAE,iBAAK,UAAU,QAAQ;AAAE,mBAAO;AAC7F;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,mBAAmB,IAAI,MAAM;AAAE,iBAAK,UAAU,QAAQ;AAAE,mBAAO;AAC7F;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,mBAAmB,IAAI,MAAM;AAAE,iBAAK,UAAU,QAAQ;AAAE,mBAAO;AAC7F;AAAA,UACA,KAAK;AAAK,iBAAK,UAAU,WAAW;AACpC;AAAA,UACA,KAAK;AAAK,eAAG,UAAU,EAAE,MAAM,sBAAsB;AAAE,iBAAK,UAAU,QAAQ;AAAG,mBAAO;AACxF;AAAA,UACA,KAAK;AAAK,iBAAK,SAAS;AAAG,eAAG,UAAU,EAAE,MAAM,aAAa,MAAI,IAAI,SAAO,GAAG;AAAG,mBAAO;AACzF;AAAA,UACA,KAAK;AAAK,iBAAK,SAAS;AAAG,eAAG,UAAU,EAAE,MAAM,aAAa,IAAI,MAAM;AAAG,mBAAO;AACjF;AAAA,UACA,KAAK;AAAK,iBAAK,SAAS;AAAG,eAAG,UAAU,EAAE,MAAM,aAAa,IAAI,MAAM;AAAG,mBAAO;AACjF;AAAA,UACA,KAAK;AAAK,eAAG,UAAU,EAAE,MAAM,cAAc,IAAI,MAAM;AAAG,gBAAI,SAAO,IAAI,OAAO,MAAM,CAAC;AAAE,mBAAO;AAChG;AAAA,QACA;AAAA,MACA,GAxNe;AAAA,MAyNf,OAAO,CAAC,qBAAoB,iBAAgB,iBAAgB,eAAc,cAAa,cAAa,kCAAiC,yBAAwB,wBAAuB,eAAc,eAAc,eAAc,YAAW,YAAW,cAAa,oBAAmB,gBAAe,kBAAiB,oBAAmB,sBAAqB,oBAAmB,mBAAkB,eAAc,eAAc,iBAAgB,2BAA0B,eAAc,iBAAgB,2BAA0B,eAAc,wBAAuB,wBAAuB,wBAAuB,wBAAuB,yBAAwB,aAAY,eAAc,iBAAgB,eAAc,eAAc,eAAc,aAAY,WAAU,YAAW,YAAW,aAAY,aAAY,WAAU,aAAY,aAAY,aAAY,aAAY,aAAY,YAAW,aAAY,YAAW,YAAW,aAAY,WAAU,eAAc,aAAY,aAAY,WAAU,UAAS,aAAY,WAAU,aAAY,aAAY,aAAY,eAAc,aAAY,aAAY,aAAY,WAAU,YAAW,kCAAiC,UAAS,eAAc,eAAc,eAAc,eAAc,YAAW,YAAW,cAAa,YAAW,iBAAgB,sBAAqB,qBAAoB,kBAAiB,kBAAiB,mBAAkB,qBAAoB,cAAa,8BAA6B,8BAA6B,iCAAgC,sBAAqB,uBAAsB,uBAAsB,wBAAuB,eAAc,YAAW,8BAA6B,8BAA6B,iCAAgC,WAAW;AAAA,MAC1qD,YAAY,EAAC,oBAAmB,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,eAAc,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,cAAa,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,YAAW,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,eAAc,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,KAAI,KAAI,KAAI,KAAI,GAAG,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,eAAc,EAAC,SAAQ,CAAC,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAG,GAAE,aAAY,KAAI,EAAC;AAAA,IAClhC;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,gBAAQ;;;ACj4BhB,OAAO,WAAW;AAUlB,IAAI,gBAAgB,oBAAI,IAAmB;AAC3C,IAAI,WAAoB,CAAC;AACzB,IAAI,YAAY,oBAAI,IAAoB;AAExC,IAAM,gBAAgB;AACtB,IAAM,eAAe;AACrB,IAAM,UAAU;AAChB,IAAM,iBAAiB;AACvB,IAAM,SAASE,WAAU;AAEzB,IAAI,UAAU,oBAAI,IAAsB;AAExC,IAAMC,gBAAe,wBAAC,QAAgB,eAAO,aAAa,KAAK,MAAM,GAAhD;AASd,IAAM,gBAAgB,gCAAU,IAAY,kBAAkB,IAAI;AAEvE,MAAI,aAAa,QAAQ,IAAI,EAAE;AAC/B,MAAI,CAAC,YAAY;AACf,iBAAa,EAAE,IAAQ,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE;AAClD,YAAQ,IAAI,IAAI,UAAU;AAAA,EAC5B;AACA,MAAI,oBAAoB,UAAa,oBAAoB,MAAM;AAC7D,oBAAgB,MAAM,cAAc,EAAE,QAAQ,CAAC,WAAW;AAExD,YAAM,cAAc,OAAO,QAAQ,YAAY,IAAI,EAAE,KAAK;AAG1D,UAAI,OAAO,aAAa,EAAE,KAAK,MAAM,GAAG;AACtC,cAAM,YAAY,YAAY,QAAQ,cAAc,OAAO;AAC3D,cAAM,YAAY,UAAU,QAAQ,eAAe,YAAY;AAC/D,mBAAW,WAAW,KAAK,SAAS;AAAA,MACtC;AACA,iBAAW,OAAO,KAAK,WAAW;AAAA,IACpC,CAAC;AAAA,EACH;AACF,GArB6B;AA8BtB,IAAM,gBAAgB,gCAAU,IAAY,SAAS,IAAI;AAC9D,QAAM,aAAa,cAAc,IAAI,EAAE;AACvC,MAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,eAAW,SAAS,OAAO,MAAM,cAAc;AAAA,EACjD;AACF,GAL6B;AAetB,IAAM,cAAc,gCAAU,SAAiB,cAAsB;AAC1E,UAAQ,MAAM,GAAG,EAAE,QAAQ,SAAU,IAAY;AAC/C,QAAI,aAAa,cAAc,IAAI,EAAE;AACrC,QAAI,eAAe,QAAW;AAC5B,YAAM,YAAY,GAAG,KAAK;AAC1B,mBAAa,EAAE,IAAI,WAAW,MAAM,MAAM,UAAU,CAAC,EAAE;AACvD,oBAAc,IAAI,WAAW,UAAU;AAAA,IACzC;AACA,QAAI,CAAC,WAAW,SAAS;AACvB,iBAAW,UAAU,CAAC;AAAA,IACxB;AACA,eAAW,QAAQ,KAAK,YAAY;AAAA,EACtC,CAAC;AACH,GAb2B;AAe3B,IAAM,wBAAwB,wBAAC,YAAqB,WAAwB;AAC1E,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,WAAW,CAAC;AAClB,aAAW,SAAS,WAAW;AAC7B,QAAI,MAAM,OAAO;AACf,YAAM,QAAQA,cAAa,MAAM,KAAK;AAAA,IACxC;AACA,QAAI,MAAM,SAAS,YAAY;AAC7B,oBAAc,MAAM,IAAI,MAAM,GAAG;AACjC;AAAA,IACF;AACA,QAAI,MAAM,SAAS,cAAc;AAC/B,kBAAY,MAAM,IAAI,OAAO,cAAc,EAAE;AAC7C;AAAA,IACF;AACA,QAAI,MAAM,SAAS,eAAe;AAChC,UAAI,OAAO,WAAW;AACpB,sBAAc,MAAM,IAAI,OAAO,SAAS;AAAA,MAC1C;AACA;AAAA,IACF;AACA,QAAI,MAAM,SAAS,kBAAkB;AACnC,aAAO,UAAU,MAAM,WAAW;AAAA,IACpC,WAAW,MAAM,SAAS,QAAQ;AAChC,YAAM,SAAS,UAAU,IAAI,MAAM,EAAE,KAAK,KAAK;AAC/C,gBAAU,IAAI,MAAM,IAAI,KAAK;AAC7B,YAAM,KAAK,QAAQ,MAAM,MAAM;AAC/B,eAAS,KAAK,KAAK;AAAA,IACrB,OAAO;AACL,UAAI,CAAC,MAAM,OAAO;AAChB,YAAI,MAAM,SAAS,aAAa;AAC9B,gBAAM,QAAQ;AAAA,QAEhB,OAAO;AACL,gBAAM,QAAQ,MAAM;AAAA,QACtB;AAAA,MACF;AACA,YAAM,gBAAgB,cAAc,IAAI,MAAM,EAAE;AAEhD,UAAI,kBAAkB,QAAW;AAC/B,sBAAc,IAAI,MAAM,IAAI,KAAK;AAAA,MACnC,OAAO;AAEL,YAAI,MAAM,SAAS,MAAM;AACvB,wBAAc,OAAO,MAAM;AAAA,QAC7B;AACA,YAAI,MAAM,UAAU,MAAM,IAAI;AAC5B,wBAAc,QAAQ,MAAM;AAAA,QAC9B;AAAA,MACF;AAEA,UAAI,MAAM,UAAU;AAClB,8BAAsB,MAAM,UAAU,KAAK;AAAA,MAC7C;AACA,UAAI,MAAM,SAAS,SAAS;AAE1B,cAAM,IAAI,MAAM,SAAS;AACzB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAM,WAAW,MAAM,KAAK;AAC5B,mBAAS,KAAK,SAAS,KAAK,MAAM;AAClC,wBAAc,IAAI,SAAS,IAAI,QAAQ;AACvC,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAAA,MACF,WAAW,kBAAkB,QAAW;AACtC,iBAAS,KAAK,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO,WAAW;AACpB,GArE8B;AAuE9B,IAAI,SAAkB,CAAC;AACvB,IAAI,YAAY,EAAE,IAAI,QAAQ,MAAM,aAAa,UAAU,CAAC,GAAG,SAAS,GAAG;AAE3E,IAAMC,SAAQ,6BAAY;AACxB,MAAI,MAAM,cAAc;AACxB,QAAY;AACZ,cAAY,EAAE,IAAI,QAAQ,MAAM,aAAa,UAAU,CAAC,GAAG,SAAS,GAAG;AACvE,kBAAgB,oBAAI,IAAI,CAAC,CAAC,QAAQ,SAAS,CAAC,CAAC;AAC7C,WAAS,CAAC;AACV,YAAU,oBAAI,IAAI;AAElB,aAAW,CAAC;AACZ,cAAY,oBAAI,IAAI;AACtB,GAVc;AAYP,SAAS,aAAa,SAAiB;AAC5C,MAAI,MAAM,gBAAgB,OAAO;AACjC,UAAQ,SAAS;AAAA,IACf,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,UAAI,MAAM,iBAAiB;AAC3B,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AArCgB;AAuCT,SAAS,iBAAiB,SAAyB;AACxD,MAAI,MAAM,gBAAgB,OAAO;AACjC,UAAQ,SAAS;AAAA,IACf,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AARgB;AAUT,SAAS,kBAAkB,SAAyB;AACzD,UAAQ,QAAQ,KAAK,GAAG;AAAA,IACtB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AATgB;AAWhB,IAAI,MAAM;AACH,IAAM,aAAa,6BAAM;AAC9B;AACA,SAAO,QAAQ,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,EAAE,IAAI,MAAM;AAClE,GAH0B;AAK1B,IAAM,eAAe,wBAAC,UAAyB;AAC7C,YAAU,WAAW;AACrB,wBAAsB,OAAO,SAAS;AACtC,WAAS,UAAU;AACrB,GAJqB;AAMrB,IAAM,aAAa,wBAAC,YAA4B;AAC9C,QAAM,QAAQ,cAAc,IAAI,OAAO;AACvC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,MAAI,MAAM,SAAS;AACjB,WAAO,MAAM;AAAA,EACf;AACA,MAAI,CAAC,MAAM,UAAU;AACnB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,SAAS;AACxB,GAZmB;AAkBnB,IAAM,gBAAgB,6BAAM;AAC1B,SAAO,CAAC,GAAG,cAAc,OAAO,CAAC;AACnC,GAFsB;AAOtB,IAAM,YAAY,6BAAM;AACtB,SAAO,UAAU,CAAC;AACpB,GAFkB;AAIlB,IAAM,WAAW,6BAAM;AACrB,SAAO;AACT,GAFiB;AAGjB,IAAM,WAAW,wBAAC,OAAe;AAC/B,SAAO,cAAc,IAAI,EAAE;AAC7B,GAFiB;AAIjB,IAAM,WAAW,wBAAC,UAAiB;AACjC,gBAAc,IAAI,MAAM,IAAI,KAAK;AACnC,GAFiB;AAIjB,IAAM,YAAY,6BAAM,KAAN;AAKX,IAAM,aAAa,kCAAY;AACpC,SAAO;AACT,GAF0B;AAI1B,IAAM,KAAK;AAAA,EACT,WAAW,6BAAgB,UAAU,EAAE,OAA5B;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAA;AAAA,EACA;AACF;AAGA,IAAO,kBAAQ;;;AC7Tf,YAAY,YAAY;AAoBxB,IAAM,OAAO,wBAAC,OAAe,YAAoB;AAE/C,QAAMC,WAAiB;AAEvB,QAAM,IAAIA,SAAQ,OAAO,GAAG;AAC5B,QAAM,IAAIA,SAAQ,OAAO,GAAG;AAC5B,QAAM,IAAIA,SAAQ,OAAO,GAAG;AAG5B,SAAc,YAAK,GAAG,GAAG,GAAG,OAAO;AACrC,GAVa;AAYb,IAAM,YAAY,wBAAC,YACjB;AAAA,mBACiB,QAAQ,UAAU;AAAA,aACxB,QAAQ,iBAAiB,QAAQ,SAAS;AAAA;AAAA;AAAA,YAG3C,QAAQ,UAAU;AAAA;AAAA;AAAA,aAGjB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMnB,QAAQ,iBAAiB,QAAQ,SAAS;AAAA,aACzC,QAAQ,iBAAiB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAQ3C,QAAQ,OAAO;AAAA,cACb,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAqBpB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA,cAIpB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,cAKjB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,wBAKP,QAAQ,mBAAmB;AAAA;AAAA;AAAA,0BAGzB,QAAQ,mBAAmB;AAAA,cACvC,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAOjB,KAAK,QAAQ,qBAAqB,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,eAK/C,KAAK,QAAQ,SAAS,GAAG,CAAC;AAAA,YAC7B,KAAK,QAAQ,YAAY,GAAG,CAAC;AAAA,cAC3B,KAAK,QAAQ,eAAe,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMlC,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,aAIjB,QAAQ,UAAU;AAAA;AAAA;AAAA,aAGlB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQZ,QAAQ,UAAU;AAAA;AAAA,kBAEnB,QAAQ,aAAa;AAAA,wBACf,QAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS3B,QAAQ,SAAS;AAAA;AAAA,IAEzB,cAAc,CAAC;AAAA,GAjHD;AAoHlB,IAAO,iBAAQ;;;ACpJf,SAAS,UAAU,gBAAgB;;;ACKnC,IAAM,gBAAgB,wBAAC,MAAM,aAAa,MAAM,OAAO;AACrD,cAAY,QAAQ,CAAC,eAAe;AAClC,YAAQ,UAAU,EAAE,MAAM,MAAM,EAAE;AAAA,EACpC,CAAC;AACH,GAJsB;AAMtB,IAAM,YAAY,wBAAC,MAAM,MAAM,OAAO;AACpC,MAAI,MAAM,uBAAuB,EAAE;AACnC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,oBAAoB;AAEjC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,eAAe,EAC5C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,oBAAoB;AACnC,GA3BkB;AA6BlB,IAAM,cAAc,wBAAC,MAAM,MAAM,OAAO;AACtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,mBAAmB,EAChD,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AAEvC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AACzC,GA1BoB;AA2BpB,IAAM,cAAc,wBAAC,MAAM,MAAM,OAAO;AACtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,mBAAmB,EAChD,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AAEvC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AACzC,GA1BoB;AA2BpB,IAAM,aAAa,wBAAC,MAAM,MAAM,OAAO;AACrC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,kBAAkB,EAC/C,KAAK,SAAS,uBAAuB,IAAI,EACzC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,yBAAyB;AAEtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,gBAAgB,EAC7C,KAAK,SAAS,uBAAuB,IAAI,EACzC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,2BAA2B;AAC1C,GA1BmB;AA2BnB,IAAM,WAAW,wBAAC,MAAM,MAAM,OAAO;AACnC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,gBAAgB,EAC7C,KAAK,SAAS,qBAAqB,IAAI,EACvC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,UAAU,OAAO,EACtB,KAAK,QAAQ,aAAa,EAC1B,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC;AAEd,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,cAAc,EAC3C,KAAK,SAAS,qBAAqB,IAAI,EACvC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,UAAU,OAAO,EACtB,KAAK,QAAQ,aAAa,EAC1B,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC;AAChB,GAlCiB;AAmCjB,IAAM,QAAQ,wBAAC,MAAM,MAAM,OAAO;AAChC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,WAAW,EACxC,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,uBAAuB,EACjC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AAClC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,aAAa,EAC1C,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,GAAG,EAChB,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,wBAAwB,EAClC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AACpC,GAjCc;AAkCd,IAAM,SAAS,wBAAC,MAAM,MAAM,OAAO;AACjC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,YAAY,EACzC,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,MAAM,GAAG,EACd,KAAK,MAAM,GAAG,EACd,KAAK,KAAK,GAAG,EACb,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AAElC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,cAAc,EAC3C,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,MAAM,GAAG,EACd,KAAK,MAAM,GAAG,EACd,KAAK,KAAK,GAAG,EACb,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AACpC,GAtCe;AAuCf,IAAM,QAAQ,wBAAC,MAAM,MAAM,OAAO;AAChC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,WAAW,EACxC,KAAK,SAAS,kBAAkB,IAAI,EACpC,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,GAAG,EAChB,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,KAAK,2BAA2B,EACrC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AAElC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,aAAa,EAC1C,KAAK,SAAS,kBAAkB,IAAI,EACpC,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,GAAG,EAChB,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,KAAK,2BAA2B,EACrC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AACpC,GApCc;AAqCd,IAAM,OAAO,wBAAC,MAAM,MAAM,OAAO;AAC/B,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,UAAU,EACvC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,eAAe,aAAa,EACjC,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,2BAA2B;AAC1C,GAba;AAgBb,IAAM,UAAU;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAO,kBAAQ;;;AChSf,IAAM,UAAUC,WAAU,GAAG,OAAO,WAAW;AAOxC,SAAS,uBAAuB,SAAiB,UAAiC;AAGvF,MAAI,YAAY,KAAK,CAAC,OAAO,UAAU,OAAO,GAAG;AAC/C,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACrD;AAGA,MAAI,WAAW,KAAK,CAAC,OAAO,UAAU,QAAQ,GAAG;AAC/C,UAAM,IAAI,MAAM,6CAA6C,QAAQ;AAAA,EACvE;AAEA,MAAI,UAAU,GAAG;AAEf,WAAO,EAAE,IAAI,UAAU,IAAI,EAAE;AAAA,EAC/B;AACA,MAAI,YAAY,GAAG;AAEjB,WAAO,EAAE,IAAI,GAAG,IAAI,SAAS;AAAA,EAC/B;AAEA,QAAM,KAAK,WAAW;AACtB,QAAM,KAAK,KAAK,MAAM,WAAW,OAAO;AAExC,SAAO,EAAE,IAAI,GAAG;AAClB;AAzBgB;AA2BhB,IAAM,kBAAkB,wBAAC,UAAiB;AACxC,MAAI,WAAW;AACf,MAAI,YAAY;AAGhB,aAAW,SAAS,MAAM,UAAU;AAClC,UAAM,EAAE,OAAO,QAAQ,GAAG,EAAE,IAAI,MAAM,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE;AAChF,QAAI;AAAA,MACF;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,IACR;AACA,QAAI,MAAM,SAAS,SAAS;AAC1B;AAAA,IACF;AACA,QAAI,QAAQ,UAAU;AACpB,iBAAW,SAAS,MAAM,kBAAkB;AAAA,IAC9C;AACA,QAAI,SAAS,WAAW;AACtB,kBAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO,EAAE,OAAO,UAAU,QAAQ,UAAU;AAC9C,GA/BwB;AAiCxB,SAAS,cAAc,OAAcC,KAAa,eAAe,GAAG,gBAAgB,GAAG;AACrF,MAAI;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,OAAO,MAAM;AAAA,IACb;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACF;AACA,MAAI,CAAC,OAAO,MAAM,OAAO;AACvB,UAAM,OAAO;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,MAAI,WAAW;AACf,MAAI,YAAY;AAEhB,MAAI,MAAM,UAAU,SAAS,GAAG;AAC9B,eAAW,SAAS,MAAM,UAAU;AAClC,oBAAc,OAAOA,GAAE;AAAA,IACzB;AAEA,UAAM,YAAY,gBAAgB,KAAK;AACvC,eAAW,UAAU;AACrB,gBAAY,UAAU;AACtB,QAAI,MAAM,mCAAmC,MAAM,IAAI,mBAAmB,UAAU,SAAS;AAG7F,eAAW,SAAS,MAAM,UAAU;AAClC,UAAI,MAAM,MAAM;AACd,YAAI;AAAA,UACF,qCAAqC,MAAM,EAAE,OAAO,MAAM,EAAE,IAAI,QAAQ,IAAI,SAAS,IAAI,KAAK,UAAU,MAAM,IAAI,CAAC;AAAA,QACrH;AACA,cAAM,KAAK,QACT,YAAY,MAAM,kBAAkB,KAAK,YAAY,MAAM,kBAAkB,KAAK;AACpF,cAAM,KAAK,SAAS;AACpB,cAAM,KAAK,IAAI;AACf,cAAM,KAAK,IAAI;AAEf,YAAI;AAAA,UACF,0BAA0B,MAAM,EAAE,mBAAmB,MAAM,EAAE,aAAa,QAAQ,cAAc,SAAS;AAAA,QAC3G;AAAA,MACF;AAAA,IACF;AACA,eAAW,SAAS,MAAM,UAAU;AAClC,oBAAc,OAAOA,KAAI,UAAU,SAAS;AAAA,IAC9C;AAEA,UAAM,UAAU,MAAM,WAAW;AACjC,QAAI,WAAW;AACf,eAAW,SAAS,MAAM,UAAU;AAClC,kBAAY,MAAM,kBAAkB;AAAA,IACtC;AAGA,QAAI,QAAQ,MAAM,SAAS;AAC3B,QAAI,UAAU,KAAK,UAAU,UAAU;AACrC,cAAQ;AAAA,IACV;AAEA,UAAM,QAAQ,KAAK,KAAK,WAAW,KAAK;AAExC,QAAI,QAAQ,SAAS,WAAW,WAAW;AAC3C,QAAI,SAAS,SAAS,YAAY,WAAW;AAE7C,QAAI,QAAQ,cAAc;AACxB,UAAI;AAAA,QACF,oCAAoC,MAAM,EAAE,iBAAiB,YAAY,kBAAkB,aAAa,UAAU,KAAK;AAAA,MACzH;AACA,cAAQ;AACR,eAAS;AACT,YAAM,cAAc,eAAe,QAAQ,UAAU,WAAW;AAChE,YAAM,eAAe,gBAAgB,QAAQ,UAAU,WAAW;AAElE,UAAI,MAAM,qBAAqB,MAAM,IAAI,cAAc,YAAY,YAAY,QAAQ;AACvF,UAAI,MAAM,qBAAqB,MAAM,IAAI,eAAe,aAAa,aAAa,SAAS;AAC3F,UAAI,MAAM,2BAA2B,OAAO,WAAW,OAAO;AAG9D,iBAAW,SAAS,MAAM,UAAU;AAClC,YAAI,MAAM,MAAM;AACd,gBAAM,KAAK,QAAQ;AACnB,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,IAAI;AACf,gBAAM,KAAK,IAAI;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAEA,QAAI;AAAA,MACF,uBAAuB,MAAM,EAAE,UAAU,KAAK,UAAU,KAAK,YAAY,OAAO,GAC9E,MAAM,SAAS,MACjB,UAAU,KAAK,IAAI,OAAO,MAAM,MAAM,SAAS,CAAC,CAAC;AAAA,IACnD;AACA,QAAI,SAAS,OAAO,MAAM,SAAS,IAAI;AACrC,cAAQ,OAAO,MAAM,SAAS;AAG9B,YAAM,MAAM,UAAU,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,OAAO,IAAI,MAAM,SAAS;AACpF,UAAI,MAAM,GAAG;AACX,cAAM,cAAc,QAAQ,MAAM,UAAU,WAAW;AACvD,YAAI,MAAM,gCAAgC,MAAM,IAAI,OAAO,MAAM,MAAM,OAAO,UAAU;AACxF,mBAAW,SAAS,MAAM,UAAU;AAClC,cAAI,MAAM,MAAM;AACd,kBAAM,KAAK,QAAQ;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,MAAI;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,OAAO,MAAM;AAAA,IACb,OAAO,MAAM;AAAA,IACb,OAAO,MAAM;AAAA,IACb,OAAO,MAAM;AAAA,EACf;AACF;AAjIS;AAmIT,SAAS,aAAa,OAAcA,KAAa;AAC/C,MAAI;AAAA,IACF,wCAAwC,MAAM,EAAE,OAAO,OAAO,MAAM,CAAC,OAAO,OAAO,MAAM,CAAC,WAAW,OAAO,MAAM,KAAK;AAAA,EACzH;AACA,QAAM,UAAU,MAAM,WAAW;AACjC,MAAI,MAAM,8BAA8B,MAAM,IAAI,MAAM,SAAS,KAAK;AACtE,MACE,MAAM;AAAA,EACN,MAAM,SAAS,SAAS,GACxB;AACA,UAAM,QAAQ,OAAO,SAAS,CAAC,GAAG,MAAM,SAAS;AACjD,UAAM,kBAAkB,MAAM,SAAS,SAAS,SAAS,MAAM,SAAS,SAAS,KAAK;AAEtF,QAAI,MAAM,sBAAsB,iBAAiB,MAAM;AAGvD,QAAI,YAAY;AAChB,QAAI,MAAM,wBAAwB,MAAM,IAAI,OAAO,MAAM,CAAC;AAC1D,QAAI,eAAe,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,CAAC,OAAO,MAAM,QAAQ,KAAK,KAAK,CAAC;AACvF,QAAI,SAAS;AACb,eAAW,SAAS,MAAM,UAAU;AAClC,YAAM,SAAS;AAEf,UAAI,CAAC,MAAM,MAAM;AACf;AAAA,MACF;AACA,YAAM,EAAE,OAAAC,QAAO,OAAO,IAAI,MAAM;AAChC,YAAM,EAAE,IAAI,GAAG,IAAI,uBAAuB,SAAS,SAAS;AAC5D,UAAI,MAAM,QAAQ;AAChB,iBAAS;AACT,uBAAe,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,CAAC,OAAO,MAAM,QAAQ,KAAK,KAAK,CAAC;AACnF,YAAI,MAAM,+BAA+B,MAAM,IAAI,eAAe,MAAM,IAAI,MAAM;AAAA,MACpF;AACA,UAAI;AAAA,QACF,mCAAmC,MAAM,EAAE,SAAS,SAAS,aAAa,EAAE,IAAI,EAAE,KAAK,QAAQ,MAAM,CAAC,IAAI,QAAQ,MAAM,CAAC,aAAa,OAAO,EAAE,WAAWA,MAAK,GAAG,OAAO;AAAA,MAC3K;AACA,UAAI,OAAO,MAAM;AACf,cAAM,YAAYA,SAAQ;AAC1B,cAAM,KAAK,IAAI,eAAe,UAAU;AAGxC,YAAI;AAAA,UACF,uCACE,MAAM,EACR,iBAAiB,YAAY,oBAC3B,MAAM,KAAK,CACb,IAAI,SAAS,YAAY,OAAO,UAAUA,MAAK,cAAc,SAAS,SACpE,MAAM,KAAK,CACb,MAAM,MAAM,KAAK,CAAC,IAAI,MAAM,cAAc,kCACvCA,UAAS,OAAO,kBAAkB,KAAM,CAC3C;AAAA,QACF;AAEA,uBAAe,MAAM,KAAK,IAAI;AAE9B,cAAM,KAAK,IACT,OAAO,KAAK,IAAI,OAAO,KAAK,SAAS,IAAI,MAAM,SAAS,WAAW,SAAS,IAAI;AAElF,YAAI;AAAA,UACF,uCACE,MAAM,EACR,eAAe,YAAY,GAAG,OAAO,GAAG,SAAS,OAAO,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,GACnF,MAAM,cACR,gCAAiCA,UAAS,OAAO,kBAAkB,KAAM,CAAC;AAAA,QAC5E;AAAA,MACF;AACA,UAAI,MAAM,UAAU;AAClB,qBAAa,OAAOD,GAAE;AAAA,MACxB;AACA,mBAAa,OAAO,kBAAkB;AACtC,UAAI,MAAM,oBAAoB,OAAO,SAAS;AAAA,IAChD;AAAA,EACF;AACA,MAAI;AAAA,IACF,mCAAmC,MAAM,EAAE,OAAO,OAAO,MAAM,CAAC,OAAO,OAAO,MAAM,CAAC,WAAW,OAAO,MAAM,KAAK;AAAA,EACpH;AACF;AA5ES;AA8ET,SAAS,WACP,OACA,EAAE,MAAM,MAAM,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,GAClE;AACA,MAAI,MAAM,QAAQ,MAAM,OAAO,QAAQ;AACrC,UAAM,EAAE,GAAG,GAAG,OAAO,OAAO,IAAI,MAAM;AACtC,QAAI,IAAI,QAAQ,IAAI,MAAM;AACxB,aAAO,IAAI,QAAQ;AAAA,IACrB;AACA,QAAI,IAAI,SAAS,IAAI,MAAM;AACzB,aAAO,IAAI,SAAS;AAAA,IACtB;AACA,QAAI,IAAI,QAAQ,IAAI,MAAM;AACxB,aAAO,IAAI,QAAQ;AAAA,IACrB;AACA,QAAI,IAAI,SAAS,IAAI,MAAM;AACzB,aAAO,IAAI,SAAS;AAAA,IACtB;AAAA,EACF;AACA,MAAI,MAAM,UAAU;AAClB,eAAW,SAAS,MAAM,UAAU;AAClC,OAAC,EAAE,MAAM,MAAM,MAAM,KAAK,IAAI,WAAW,OAAO,EAAE,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,IAC5E;AAAA,EACF;AACA,SAAO,EAAE,MAAM,MAAM,MAAM,KAAK;AAClC;AAzBS;AA2BF,SAAS,OAAOA,KAAa;AAClC,QAAM,OAAOA,IAAG,SAAS,MAAM;AAC/B,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AAEA,gBAAc,MAAMA,KAAI,GAAG,CAAC;AAC5B,eAAa,MAAMA,GAAE;AAGrB,MAAI,MAAM,aAAa,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAEpD,QAAM,EAAE,MAAM,MAAM,MAAM,KAAK,IAAI,WAAW,IAAI;AAElD,QAAM,SAAS,OAAO;AACtB,QAAM,QAAQ,OAAO;AACrB,SAAO,EAAE,GAAG,MAAM,GAAG,MAAM,OAAO,OAAO;AAC3C;AAjBgB;;;ACpThB,YAAY,cAAc;;;ACA1B,SAAS,cAAc;AAWvB,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AAJS;AAUT,SAAS,aAAa,MAAM;AAC1B,QAAM,KAAK,OAAO,SAAS,gBAAgB,8BAA8B,eAAe,CAAC;AACzF,QAAM,MAAM,GAAG,OAAO,WAAW;AAEjC,QAAM,QAAQ,KAAK;AACnB,QAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,QAAM,OAAO,IAAI,OAAO,MAAM;AAC9B,OAAK,KAAK,KAAK;AACf,aAAW,MAAM,KAAK,UAAU;AAChC,OAAK,KAAK,SAAS,UAAU;AAE7B,aAAW,KAAK,KAAK,UAAU;AAC/B,MAAI,MAAM,WAAW,cAAc;AAEnC,MAAI,MAAM,eAAe,QAAQ;AACjC,MAAI,KAAK,SAAS,8BAA8B;AAChD,SAAO,GAAG,KAAK;AACjB;AAjBS;AAyBT,IAAM,cAAc,8BAAO,aAAa,OAAO,SAAS,WAAW;AACjE,MAAI,aAAa,eAAe;AAChC,MAAI,OAAO,eAAe,UAAU;AAClC,iBAAa,WAAW,CAAC;AAAA,EAC3B;AACA,MAAI,SAASE,WAAU,EAAE,UAAU,UAAU,GAAG;AAE9C,iBAAa,WAAW,QAAQ,WAAW,QAAQ;AACnD,QAAI,MAAM,eAAe,UAAU;AACnC,UAAM,QAAQ,MAAM,qBAAqB,eAAe,UAAU,CAAC;AACnE,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA,YAAY,MAAM,QAAQ,SAAS,QAAQ;AAAA,IAC7C;AACA,QAAI,aAAa,aAAa,IAAI;AAElC,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW,SAAS,gBAAgB,8BAA8B,MAAM;AAC9E,aAAS,aAAa,SAAS,MAAM,QAAQ,UAAU,OAAO,CAAC;AAC/D,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,WAAW,MAAM,qBAAqB;AAAA,IAC/C,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC;AAAA,IACV;AAEA,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,SAAS,gBAAgB,8BAA8B,OAAO;AAC5E,YAAM,eAAe,wCAAwC,aAAa,UAAU;AACpF,YAAM,aAAa,MAAM,KAAK;AAC9B,YAAM,aAAa,KAAK,GAAG;AAC3B,UAAI,SAAS;AACX,cAAM,aAAa,SAAS,WAAW;AAAA,MACzC,OAAO;AACL,cAAM,aAAa,SAAS,KAAK;AAAA,MACnC;AACA,YAAM,cAAc,IAAI,KAAK;AAC7B,eAAS,YAAY,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF,GA7CoB;AA+CpB,IAAO,sBAAQ;;;AC1Ff,SAAS,MAAM,YAAY,UAAAC,eAAc;;;ACSlC,IAAM,iBAAiB,wBAC5B,SACA,MACA,KACA,IACA,gBACG;AACH,MAAI,KAAK,gBAAgB;AACvB,kBAAc,SAAS,SAAS,KAAK,gBAAgB,KAAK,IAAI,WAAW;AAAA,EAC3E;AACA,MAAI,KAAK,cAAc;AACrB,kBAAc,SAAS,OAAO,KAAK,cAAc,KAAK,IAAI,WAAW;AAAA,EACvE;AACF,GAb8B;AAe9B,IAAM,gBAAgB;AAAA,EACpB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AACZ;AAEA,IAAM,gBAAgB,wBACpB,SACA,UACA,WACA,KACA,IACA,gBACG;AACH,QAAM,gBAAgB,cAAc,SAAuC;AAE3E,MAAI,CAAC,eAAe;AAClB,QAAI,KAAK,uBAAuB,SAAS,EAAE;AAC3C;AAAA,EACF;AAEA,QAAM,SAAS,aAAa,UAAU,UAAU;AAChD,UAAQ,KAAK,UAAU,QAAQ,IAAI,OAAO,GAAG,IAAI,EAAE,IAAI,WAAW,IAAI,aAAa,GAAG,MAAM,GAAG;AACjG,GAjBsB;;;AD5BtB,IAAI,aAAa,CAAC;AAClB,IAAI,iBAAiB,CAAC;AAOf,IAAM,kBAAkB,8BAAO,MAAM,SAAS;AACnD,QAAMC,UAASC,WAAU;AACzB,QAAM,gBAAgB,SAASD,QAAO,UAAU,UAAU;AAE1D,QAAM,eACJ,KAAK,cAAc,aACf;AAAA,IACE;AAAA,IACA,KAAK;AAAA,IACL;AAAA,MACE,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,kBAAkB;AAAA,IACpB;AAAA,IACAA;AAAA,EACF,IACA,MAAM,oBAAY,KAAK,OAAO,KAAK,UAAU;AAGnD,QAAM,YAAY,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,WAAW;AAG5D,QAAM,QAAQ,UAAU,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACzD,QAAM,KAAK,EAAE,YAAY,YAAY;AAGrC,MAAI,OAAO,aAAa,QAAQ;AAChC,MAAI,eAAe;AACjB,UAAM,MAAM,aAAa,SAAS,CAAC;AACnC,UAAM,KAAKE,QAAO,YAAY;AAC9B,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAGtF,aAAW,KAAK,EAAE,IAAI;AAGtB,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AAEnB,MAAI;AACJ,MAAI,KAAK,gBAAgB;AAEvB,UAAM,oBAAoB,MAAM,oBAAY,KAAK,gBAAgB,KAAK,UAAU;AAChF,UAAM,qBAAqB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACzE,UAAM,QAAQ,mBAAmB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAClE,SAAK,MAAM,KAAK,EAAE,YAAY,iBAAiB;AAC/C,UAAM,QAAQ,kBAAkB,QAAQ;AACxC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AACxF,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAC;AAAA,IAC7B;AACA,mBAAe,KAAK,EAAE,EAAE,YAAY;AACpC,qBAAiB,IAAI,KAAK,cAAc;AAAA,EAC1C;AACA,MAAI,KAAK,iBAAiB;AAExB,UAAM,oBAAoB,MAAM,oBAAY,KAAK,iBAAiB,KAAK,UAAU;AACjF,UAAM,sBAAsB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAC1E,UAAM,QAAQ,oBAAoB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACnE,SAAK,oBAAoB,KAAK,EAAE,YAAY,iBAAiB;AAC7D,UAAM,KAAK,EAAE,YAAY,iBAAiB;AAC1C,UAAM,QAAQ,kBAAkB,QAAQ;AACxC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AAExF,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAC;AAAA,IAC7B;AACA,mBAAe,KAAK,EAAE,EAAE,aAAa;AACrC,qBAAiB,IAAI,KAAK,eAAe;AAAA,EAC3C;AACA,MAAI,KAAK,cAAc;AAErB,UAAM,kBAAkB,MAAM,oBAAY,KAAK,cAAc,KAAK,UAAU;AAC5E,UAAM,mBAAmB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACvE,UAAM,QAAQ,iBAAiB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAChE,SAAK,MAAM,KAAK,EAAE,YAAY,eAAe;AAC7C,UAAM,QAAQ,gBAAgB,QAAQ;AACtC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AAExF,qBAAiB,KAAK,EAAE,YAAY,eAAe;AAEnD,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAC;AAAA,IAC7B;AACA,mBAAe,KAAK,EAAE,EAAE,UAAU;AAClC,qBAAiB,IAAI,KAAK,YAAY;AAAA,EACxC;AACA,MAAI,KAAK,eAAe;AAEtB,UAAM,kBAAkB,MAAM,oBAAY,KAAK,eAAe,KAAK,UAAU;AAC7E,UAAM,oBAAoB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACxE,UAAM,QAAQ,kBAAkB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAEjE,SAAK,MAAM,KAAK,EAAE,YAAY,eAAe;AAC7C,UAAM,QAAQ,gBAAgB,QAAQ;AACtC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AAExF,sBAAkB,KAAK,EAAE,YAAY,eAAe;AACpD,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAC;AAAA,IAC7B;AACA,mBAAe,KAAK,EAAE,EAAE,WAAW;AACnC,qBAAiB,IAAI,KAAK,aAAa;AAAA,EACzC;AACA,SAAO;AACT,GA7G+B;AAmH/B,SAAS,iBAAiB,IAAI,OAAO;AACnC,MAAID,WAAU,EAAE,UAAU,cAAc,IAAI;AAC1C,OAAG,MAAM,QAAQ,MAAM,SAAS,IAAI;AACpC,OAAG,MAAM,SAAS;AAAA,EACpB;AACF;AALS;AAOF,IAAM,oBAAoB,wBAAC,MAAM,UAAU;AAChD,MAAI,MAAM,uBAAuB,KAAK,IAAI,KAAK,OAAO,WAAW,KAAK,EAAE,GAAG,KAAK;AAChF,MAAI,OAAO,MAAM,cAAc,MAAM,cAAc,MAAM;AACzD,QAAM,aAAaA,WAAU;AAC7B,QAAM,EAAE,yBAAyB,IAAI,wBAAwB,UAAU;AACvE,MAAI,KAAK,OAAO;AACd,UAAM,KAAK,WAAW,KAAK,EAAE;AAC7B,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AAER,YAAM,MAAM,cAAM,kBAAkB,IAAI;AACxC,UAAI;AAAA,QACF,kBAAkB,KAAK,QAAQ;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,QACJ;AAAA,QACA,IAAI;AAAA,QACJ;AAAA,MACF;AACA,UAAI,MAAM,aAAa;AACrB,YAAI,IAAI;AACR,YAAI,IAAI;AAAA,MACV;AAAA,IACF;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,IAAI,2BAA2B,CAAC,GAAG;AAAA,EAC7E;AAGA,MAAI,KAAK,gBAAgB;AACvB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AAER,YAAM,MAAM,cAAM,0BAA0B,KAAK,iBAAiB,KAAK,GAAG,cAAc,IAAI;AAC5F,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACA,MAAI,KAAK,iBAAiB;AACxB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AAER,YAAM,MAAM,cAAM;AAAA,QAChB,KAAK,iBAAiB,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AACA,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACA,MAAI,KAAK,cAAc;AACrB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AAER,YAAM,MAAM,cAAM,0BAA0B,KAAK,eAAe,KAAK,GAAG,YAAY,IAAI;AACxF,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACA,MAAI,KAAK,eAAe;AACtB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AAER,YAAM,MAAM,cAAM,0BAA0B,KAAK,eAAe,KAAK,GAAG,aAAa,IAAI;AACzF,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACF,GApFiC;AAsFjC,IAAM,cAAc,wBAAC,MAAME,WAAU;AACnC,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,KAAK,KAAK,IAAIA,OAAM,IAAI,CAAC;AAC/B,QAAM,KAAK,KAAK,IAAIA,OAAM,IAAI,CAAC;AAC/B,QAAM,IAAI,KAAK,QAAQ;AACvB,QAAM,IAAI,KAAK,SAAS;AACxB,MAAI,MAAM,KAAK,MAAM,GAAG;AACtB,WAAO;AAAA,EACT;AACA,SAAO;AACT,GAXoB;AAab,IAAM,eAAe,wBAAC,MAAM,cAAc,gBAAgB;AAC/D,MAAI,MAAM;AAAA,kBACM,KAAK,UAAU,YAAY,CAAC;AAAA,kBAC5B,KAAK,UAAU,WAAW,CAAC;AAAA,oBACzB,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,EAAE;AACvE,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AAEf,QAAM,KAAK,KAAK,IAAI,IAAI,YAAY,CAAC;AAErC,QAAM,IAAI,KAAK,QAAQ;AACvB,MAAI,IAAI,YAAY,IAAI,aAAa,IAAI,IAAI,KAAK,IAAI;AACtD,QAAM,IAAI,KAAK,SAAS;AAExB,QAAM,IAAI,KAAK,IAAI,aAAa,IAAI,YAAY,CAAC;AACjD,QAAM,IAAI,KAAK,IAAI,aAAa,IAAI,YAAY,CAAC;AAEjD,MAAI,KAAK,IAAI,IAAI,aAAa,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,aAAa,CAAC,IAAI,GAAG;AAEvE,QAAI,IAAI,YAAY,IAAI,aAAa,IAAI,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,aAAa;AACvF,QAAK,IAAI,IAAK;AACd,UAAM,MAAM;AAAA,MACV,GAAG,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,IAAI;AAAA,MAC5E,GAAG,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI;AAAA,IAClF;AAEA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;AACrB,UAAI,IAAI,aAAa;AAAA,IACvB;AACA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;AAAA,IACvB;AACA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;AAAA,IACvB;AAEA,QAAI,MAAM,2BAA2B,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG;AAErE,WAAO;AAAA,EACT,OAAO;AAEL,QAAI,YAAY,IAAI,aAAa,GAAG;AAClC,UAAI,aAAa,IAAI,IAAI;AAAA,IAC3B,OAAO;AAEL,UAAI,IAAI,IAAI,aAAa;AAAA,IAC3B;AACA,QAAI,IAAK,IAAI,IAAK;AAGlB,QAAI,KAAK,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI;AAEtF,QAAI,KAAK,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI;AAC9E,QAAI,MAAM,uBAAuB,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AACxE,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;AAClB,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;AAAA,IACpB;AAEA,WAAO,EAAE,GAAG,IAAI,GAAG,GAAG;AAAA,EACxB;AACF,GApE4B;AA6E5B,IAAM,qBAAqB,wBAAC,SAAS,iBAAiB;AACpD,MAAI,MAAM,4BAA4B,SAAS,YAAY;AAC3D,MAAI,SAAS,CAAC;AACd,MAAI,mBAAmB,QAAQ,CAAC;AAChC,MAAI,WAAW;AACf,UAAQ,QAAQ,CAACA,WAAU;AAEzB,QAAI,CAAC,YAAY,cAAcA,MAAK,KAAK,CAAC,UAAU;AAGlD,YAAM,QAAQ,aAAa,cAAc,kBAAkBA,MAAK;AAGhE,UAAI,eAAe;AACnB,aAAO,QAAQ,CAAC,MAAM;AACpB,uBAAe,gBAAiB,EAAE,MAAM,MAAM,KAAK,EAAE,MAAM,MAAM;AAAA,MACnE,CAAC;AAED,UAAI,CAAC,OAAO,KAAK,CAAC,MAAM,EAAE,MAAM,MAAM,KAAK,EAAE,MAAM,MAAM,CAAC,GAAG;AAC3D,eAAO,KAAK,KAAK;AAAA,MACnB;AAEA,iBAAW;AAAA,IACb,OAAO;AAEL,yBAAmBA;AAEnB,UAAI,CAAC,UAAU;AACb,eAAO,KAAKA,MAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT,GAjC2B;AAmCpB,IAAM,aAAa,gCAAU,MAAM,GAAG,MAAM,WAAW,aAAa,OAAO,IAAI;AACpF,MAAI,SAAS,KAAK;AAClB,MAAI,MAAM,2BAA2B,MAAM,MAAM,CAAC;AAClD,MAAI,mBAAmB;AACvB,QAAM,OAAO,MAAM,KAAK,EAAE,CAAC;AAC3B,MAAI,OAAO,MAAM,KAAK,EAAE,CAAC;AAEzB,MAAI,MAAM,aAAa,MAAM,WAAW;AACtC,aAAS,OAAO,MAAM,GAAG,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QAAQ,KAAK,UAAU,OAAO,CAAC,CAAC,CAAC;AACxC,WAAO,KAAK,KAAK,UAAU,OAAO,OAAO,SAAS,CAAC,CAAC,CAAC;AAAA,EACvD;AAEA,MAAI,KAAK,WAAW;AAClB,QAAI,MAAM,oBAAoB,UAAU,KAAK,SAAS,CAAC;AACvD,aAAS,mBAAmB,KAAK,QAAQ,UAAU,KAAK,SAAS,EAAE,IAAI;AAEvE,uBAAmB;AAAA,EACrB;AAEA,MAAI,KAAK,aAAa;AACpB,QAAI,MAAM,sBAAsB,UAAU,KAAK,WAAW,CAAC;AAC3D,aAAS,mBAAmB,OAAO,QAAQ,GAAG,UAAU,KAAK,WAAW,EAAE,IAAI,EAAE,QAAQ;AAExF,uBAAmB;AAAA,EACrB;AAGA,QAAM,WAAW,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC;AAGxD,MAAI,QAAQ;AAIZ,MAAI,KAAK,UAAU,gBAAgB,WAAW,gBAAgB,cAAc;AAC1E,YAAQ,KAAK;AAAA,EACf;AAEA,QAAM,EAAE,GAAG,EAAE,IAAI,2BAA2B,IAAI;AAChD,QAAM,eAAe,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,KAAK;AAGjD,MAAI;AACJ,UAAQ,KAAK,WAAW;AAAA,IACtB,KAAK;AACH,sBAAgB;AAChB;AAAA,IACF,KAAK;AACH,sBAAgB;AAChB;AAAA,IACF,KAAK;AACH,sBAAgB;AAChB;AAAA,IACF;AACE,sBAAgB;AAAA,EACpB;AACA,UAAQ,KAAK,SAAS;AAAA,IACpB,KAAK;AACH,uBAAiB;AACjB;AAAA,IACF,KAAK;AACH,uBAAiB;AACjB;AAAA,IACF,KAAK;AACH,uBAAiB;AACjB;AAAA,EACJ;AAEA,QAAM,UAAU,KACb,OAAO,MAAM,EACb,KAAK,KAAK,aAAa,QAAQ,CAAC,EAChC,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,SAAS,MAAM,iBAAiB,KAAK,UAAU,MAAM,KAAK,UAAU,GAAG,EAC5E,KAAK,SAAS,KAAK,KAAK;AAa3B,MAAI,MAAM;AAEV,MAAIF,WAAU,EAAE,UAAU,uBAAuBA,WAAU,EAAE,MAAM,qBAAqB;AACtF,UAAM,OAAO,IAAI;AAAA,EACnB;AAEA,iBAAe,SAAS,MAAM,KAAK,IAAI,WAAW;AAElD,MAAI,QAAQ,CAAC;AACb,MAAI,kBAAkB;AACpB,UAAM,cAAc;AAAA,EACtB;AACA,QAAM,eAAe,KAAK;AAC1B,SAAO;AACT,GArG0B;;;AEhW1B,SAAS,UAAAG,eAAc;;;ACEvB,IAAM,iCAAiC,wBAAC,eAA4B;AAClE,QAAM,mBAAmB,oBAAI,IAAI;AAEjC,aAAW,aAAa,YAAY;AAClC,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,yBAAiB,IAAI,OAAO;AAC5B,yBAAiB,IAAI,MAAM;AAC3B;AAAA,MACF,KAAK;AACH,yBAAiB,IAAI,IAAI;AACzB,yBAAiB,IAAI,MAAM;AAC3B;AAAA,MACF;AACE,yBAAiB,IAAI,SAAS;AAC9B;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT,GApBuC;AAqBhC,IAAM,iBAAiB,wBAC5B,sBACA,MACA,SACG;AAGH,QAAM,aAAa,+BAA+B,oBAAoB;AAGtE,QAAM,IAAI;AAGV,QAAM,SAAS,KAAK,SAAS,IAAI,KAAK;AAEtC,QAAM,WAAW,SAAS;AAE1B,QAAM,QAAQ,KAAK,QAAQ,IAAI,WAAW,KAAK;AAE/C,QAAMC,WAAU,KAAK,UAAU;AAE/B,MACE,WAAW,IAAI,OAAO,KACtB,WAAW,IAAI,MAAM,KACrB,WAAW,IAAI,IAAI,KACnB,WAAW,IAAI,MAAM,GACrB;AAEA,WAAO;AAAA;AAAA,MAEL,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACb,EAAE,GAAG,UAAU,GAAG,EAAE;AAAA,MACpB,EAAE,GAAG,QAAQ,GAAG,GAAG,IAAIA,SAAQ;AAAA,MAC/B,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAE;AAAA,MAC5B,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA;AAAA,MAGjB,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAE;AAAA,MAC3B,EAAE,GAAG,QAAQ,IAAIA,UAAS,GAAG,CAAC,SAAS,EAAE;AAAA,MACzC,EAAE,GAAG,OAAO,GAAI,KAAK,SAAU,EAAE;AAAA,MACjC,EAAE,GAAG,OAAO,GAAG,CAAC,OAAO;AAAA;AAAA,MAGvB,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAO;AAAA,MAClC,EAAE,GAAG,QAAQ,GAAG,GAAG,CAAC,SAAS,IAAIA,SAAQ;AAAA,MACzC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAO;AAAA;AAAA,MAG1B,EAAE,GAAG,GAAG,GAAG,CAAC,OAAO;AAAA,MACnB,EAAE,GAAG,GAAG,GAAI,KAAK,SAAU,EAAE;AAAA,MAC7B,EAAE,GAAG,KAAKA,UAAS,GAAG,CAAC,SAAS,EAAE;AAAA,MAClC,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IACzB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,IAAI,GAAG;AAE7E,WAAO;AAAA,MACL,EAAE,GAAG,UAAU,GAAG,EAAE;AAAA,MACpB,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAE;AAAA,MAC5B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAE;AAAA,MAC3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAO;AAAA,MAClC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAO;AAAA,MAC1B,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IACzB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;AAE/E,WAAO;AAAA,MACL,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACb,EAAE,GAAG,UAAU,GAAG,CAAC,OAAO;AAAA,MAC1B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAO;AAAA,MAClC,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA,IACnB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,MAAM,GAAG;AAE7E,WAAO;AAAA,MACL,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACb,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS;AAAA,MACzB,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,SAAS;AAAA,MAClC,EAAE,GAAG,GAAG,GAAG,CAAC,OAAO;AAAA,IACrB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,MAAM,GAAG;AAE5E,WAAO;AAAA,MACL,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA,MACjB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS;AAAA,MACrB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,SAAS;AAAA,MAC9B,EAAE,GAAG,OAAO,GAAG,CAAC,OAAO;AAAA,IACzB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,GAAG;AAErD,WAAO;AAAA,MACL,EAAE,GAAG,UAAU,GAAG,EAAE;AAAA,MACpB,EAAE,GAAG,UAAU,GAAG,CAACA,SAAQ;AAAA,MAC3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAACA,SAAQ;AAAA,MACnC,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAE;AAAA,MAC5B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAE;AAAA,MAC3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAO;AAAA,MAClC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MAC5C,EAAE,GAAG,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MACpC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAO;AAAA,MAC1B,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IACzB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,MAAM,GAAG;AAElD,WAAO;AAAA;AAAA,MAEL,EAAE,GAAG,QAAQ,GAAG,GAAG,EAAE;AAAA;AAAA,MAErB,EAAE,GAAG,GAAG,GAAG,CAACA,SAAQ;AAAA,MACpB,EAAE,GAAG,UAAU,GAAG,CAACA,SAAQ;AAAA;AAAA,MAE3B,EAAE,GAAG,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MACpC,EAAE,GAAG,GAAG,GAAG,CAAC,SAASA,SAAQ;AAAA;AAAA,MAE7B,EAAE,GAAG,QAAQ,GAAG,GAAG,CAAC,OAAO;AAAA,MAC3B,EAAE,GAAG,OAAO,GAAG,CAAC,SAASA,SAAQ;AAAA;AAAA,MAEjC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MAC5C,EAAE,GAAG,QAAQ,UAAU,GAAG,CAACA,SAAQ;AAAA,MACnC,EAAE,GAAG,OAAO,GAAG,CAACA,SAAQ;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,IAAI,GAAG;AAEnD,WAAO;AAAA,MACL,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACb,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS;AAAA,MACzB,EAAE,GAAG,GAAG,GAAG,CAAC,OAAO;AAAA,IACrB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,GAAG;AAErD,WAAO;AAAA,MACL,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACb,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA,MACjB,EAAE,GAAG,GAAG,GAAG,CAAC,OAAO;AAAA,IACrB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,IAAI,GAAG;AAElD,WAAO;AAAA,MACL,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA,MACjB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS;AAAA,MACrB,EAAE,GAAG,OAAO,GAAG,CAAC,OAAO;AAAA,IACzB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;AAEpD,WAAO;AAAA,MACL,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA,MACjB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACb,EAAE,GAAG,OAAO,GAAG,CAAC,OAAO;AAAA,IACzB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,OAAO,GAAG;AAE3B,WAAO;AAAA,MACL,EAAE,GAAG,UAAU,GAAG,CAACA,SAAQ;AAAA,MAC3B,EAAE,GAAG,UAAU,GAAG,CAACA,SAAQ;AAAA,MAC3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAACA,SAAQ;AAAA,MACnC,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAE;AAAA,MAC5B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAE;AAAA,MAC3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAO;AAAA,MAClC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA;AAAA,MAE5C,EAAE,GAAG,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MACpC,EAAE,GAAG,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,IACtC;AAAA,EACF;AACA,MAAI,WAAW,IAAI,MAAM,GAAG;AAE1B,WAAO;AAAA,MACL,EAAE,GAAG,UAAU,GAAG,EAAE;AAAA,MACpB,EAAE,GAAG,UAAU,GAAG,CAACA,SAAQ;AAAA;AAAA,MAE3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAACA,SAAQ;AAAA,MACnC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MAC5C,EAAE,GAAG,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MACpC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAO;AAAA,MAC1B,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IACzB;AAAA,EACF;AACA,MAAI,WAAW,IAAI,IAAI,GAAG;AAExB,WAAO;AAAA;AAAA,MAEL,EAAE,GAAG,UAAU,GAAG,CAACA,SAAQ;AAAA;AAAA,MAE3B,EAAE,GAAG,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MACpC,EAAE,GAAG,GAAG,GAAG,CAAC,SAASA,SAAQ;AAAA;AAAA,MAE7B,EAAE,GAAG,QAAQ,GAAG,GAAG,CAAC,OAAO;AAAA,MAC3B,EAAE,GAAG,OAAO,GAAG,CAAC,SAASA,SAAQ;AAAA;AAAA,MAEjC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MAC5C,EAAE,GAAG,QAAQ,UAAU,GAAG,CAACA,SAAQ;AAAA,IACrC;AAAA,EACF;AACA,MAAI,WAAW,IAAI,MAAM,GAAG;AAE1B,WAAO;AAAA;AAAA,MAEL,EAAE,GAAG,QAAQ,GAAG,GAAG,EAAE;AAAA;AAAA,MAErB,EAAE,GAAG,GAAG,GAAG,CAACA,SAAQ;AAAA,MACpB,EAAE,GAAG,UAAU,GAAG,CAACA,SAAQ;AAAA;AAAA,MAE3B,EAAE,GAAG,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MACpC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAASA,SAAQ;AAAA,MAC5C,EAAE,GAAG,QAAQ,UAAU,GAAG,CAACA,SAAQ;AAAA,MACnC,EAAE,GAAG,OAAO,GAAG,CAACA,SAAQ;AAAA,IAC1B;AAAA,EACF;AAGA,SAAO,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACxB,GA7N8B;;;ACnB9B,SAAS,cAAc,MAAMC,QAAO;AAElC,SAAO,KAAK,UAAUA,MAAK;AAC7B;AAHS;AAKT,IAAO,yBAAQ;;;ACHf,SAAS,iBAAiB,MAAM,IAAI,IAAIC,QAAO;AAG7C,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AAEd,MAAI,KAAK,KAAKA,OAAM;AACpB,MAAI,KAAK,KAAKA,OAAM;AAEpB,MAAI,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEzD,MAAI,KAAK,KAAK,IAAK,KAAK,KAAK,KAAM,GAAG;AACtC,MAAIA,OAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AACA,MAAI,KAAK,KAAK,IAAK,KAAK,KAAK,KAAM,GAAG;AACtC,MAAIA,OAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AAEA,SAAO,EAAE,GAAG,KAAK,IAAI,GAAG,KAAK,GAAG;AAClC;AArBS;AAuBT,IAAO,4BAAQ;;;ACtBf,SAAS,gBAAgB,MAAM,IAAIC,QAAO;AACxC,SAAO,0BAAiB,MAAM,IAAI,IAAIA,MAAK;AAC7C;AAFS;AAIT,IAAO,2BAAQ;;;ACHf,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI;AAIrC,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,OAAO,QAAQ;AACnB,MAAI,GAAG;AAIP,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAG7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAI7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AAGA,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAG7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAK7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AAGA,UAAQ,KAAK,KAAK,KAAK;AACvB,MAAI,UAAU,GAAG;AACf;AAAA,EACF;AAEA,WAAS,KAAK,IAAI,QAAQ,CAAC;AAK3B,QAAM,KAAK,KAAK,KAAK;AACrB,MAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AAExD,QAAM,KAAK,KAAK,KAAK;AACrB,MAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AAExD,SAAO,EAAE,GAAM,EAAK;AACtB;AA3DS;AAiET,SAAS,SAAS,IAAI,IAAI;AACxB,SAAO,KAAK,KAAK;AACnB;AAFS;AAIT,IAAO,yBAAQ;;;ACzEf,IAAO,4BAAQ;AAUf,SAAS,iBAAiB,MAAM,YAAYC,QAAO;AACjD,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AAEd,MAAI,gBAAgB,CAAC;AAErB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,WAAW,YAAY,YAAY;AAC5C,eAAW,QAAQ,SAAU,OAAO;AAClC,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH,OAAO;AACL,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAAA,EACpC;AAEA,MAAI,OAAO,KAAK,KAAK,QAAQ,IAAI;AACjC,MAAI,MAAM,KAAK,KAAK,SAAS,IAAI;AAEjC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,KAAK,WAAW,CAAC;AACrB,QAAI,KAAK,WAAW,IAAI,WAAW,SAAS,IAAI,IAAI,IAAI,CAAC;AACzD,QAAI,YAAY;AAAA,MACd;AAAA,MACAA;AAAA,MACA,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,MAChC,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,IAClC;AACA,QAAI,WAAW;AACb,oBAAc,KAAK,SAAS;AAAA,IAC9B;AAAA,EACF;AAEA,MAAI,CAAC,cAAc,QAAQ;AAEzB,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,SAAS,GAAG;AAE5B,kBAAc,KAAK,SAAU,GAAG,GAAG;AACjC,UAAI,MAAM,EAAE,IAAIA,OAAM;AACtB,UAAI,MAAM,EAAE,IAAIA,OAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAE3C,UAAI,MAAM,EAAE,IAAIA,OAAM;AACtB,UAAI,MAAM,EAAE,IAAIA,OAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAE3C,aAAO,QAAQ,QAAQ,KAAK,UAAU,QAAQ,IAAI;AAAA,IACpD,CAAC;AAAA,EACH;AACA,SAAO,cAAc,CAAC;AACxB;AAvDS;;;ACdT,IAAM,gBAAgB,wBAAC,MAAMC,WAAU;AACrC,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AAIb,MAAI,KAAKA,OAAM,IAAI;AACnB,MAAI,KAAKA,OAAM,IAAI;AACnB,MAAI,IAAI,KAAK,QAAQ;AACrB,MAAI,IAAI,KAAK,SAAS;AAEtB,MAAI,IAAI;AACR,MAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG;AAEvC,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;AAAA,IACP;AACA,SAAK,OAAO,IAAI,IAAK,IAAI,KAAM;AAC/B,SAAK;AAAA,EACP,OAAO;AAEL,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;AAAA,IACP;AACA,SAAK;AACL,SAAK,OAAO,IAAI,IAAK,IAAI,KAAM;AAAA,EACjC;AAEA,SAAO,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG;AAChC,GA7BsB;AA+BtB,IAAO,yBAAQ;;;ACrBf,IAAO,oBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACbA,SAAS,UAAAC,eAAc;AAIhB,IAAM,cAAc,8BAAO,QAAQ,MAAM,UAAU,WAAW;AACnE,QAAMC,UAASC,WAAU;AACzB,MAAIC;AACJ,QAAM,gBAAgB,KAAK,iBAAiB,SAASF,QAAO,UAAU,UAAU;AAChF,MAAI,CAAC,UAAU;AACb,IAAAE,WAAU;AAAA,EACZ,OAAO;AACL,IAAAA,WAAU;AAAA,EACZ;AAGA,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAASA,QAAO,EACrB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAGnC,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,KAAK,UAAU;AAGvF,MAAI;AACJ,MAAI,KAAK,cAAc,QAAW;AAChC,gBAAY;AAAA,EACd,OAAO;AACL,gBAAY,OAAO,KAAK,cAAc,WAAW,KAAK,YAAY,KAAK,UAAU,CAAC;AAAA,EACpF;AAEA,QAAM,WAAW,MAAM,KAAK;AAC5B,MAAI;AACJ,MAAI,KAAK,cAAc,YAAY;AAEjC,WAAO;AAAA,MACL;AAAA,MACA,aAAa,eAAe,SAAS,GAAGF,OAAM;AAAA,MAC9C;AAAA,QACE;AAAA,QACA,OAAO,KAAK,SAASA,QAAO,UAAU;AAAA,QACtC,SAAS;AAAA,MACX;AAAA,MACAA;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,SAAS;AAAA,MACd,MAAM;AAAA,QACJ,aAAa,eAAe,SAAS,GAAGA,OAAM;AAAA,QAC9C,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,QAAQ;AACxB,QAAM,cAAc,KAAK,UAAU;AAEnC,MAAI,SAASA,QAAO,UAAU,UAAU,GAAG;AACzC,UAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,UAAM,KAAKG,QAAO,IAAI;AAGtB,UAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,QAAI,QAAQ;AACV,YAAM,YAAY,UAAU,QAAQ,eAAe,EAAE,EAAE,KAAK,MAAM;AAElE,YAAM,QAAQ;AAAA,QACZ,CAAC,GAAG,MAAM,EAAE;AAAA,UACV,CAAC,QACC,IAAI,QAAQ,CAAC,QAAQ;AAInB,qBAAS,aAAa;AACpB,kBAAI,MAAM,UAAU;AACpB,kBAAI,MAAM,gBAAgB;AAE1B,kBAAI,WAAW;AAEb,sBAAM,eAAeH,QAAO,WACxBA,QAAO,WACP,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC3C,sBAAM,kBAAkB;AACxB,sBAAM,QAAQ,SAAS,cAAc,EAAE,IAAI,kBAAkB;AAC7D,oBAAI,MAAM,WAAW;AACrB,oBAAI,MAAM,WAAW;AAAA,cACvB,OAAO;AACL,oBAAI,MAAM,QAAQ;AAAA,cACpB;AACA,kBAAI,GAAG;AAAA,YACT;AAjBS;AAkBT,uBAAW,MAAM;AACf,kBAAI,IAAI,UAAU;AAChB,2BAAW;AAAA,cACb;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,SAAS,UAAU;AACxC,gBAAI,iBAAiB,QAAQ,UAAU;AAAA,UACzC,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAGA,MAAI,eAAe;AACjB,UAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EACxF,OAAO;AACL,UAAM,KAAK,aAAa,kBAA0B,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1E;AACA,MAAI,KAAK,aAAa;AACpB,UAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EACxF;AACA,QAAM,OAAO,QAAQ,cAAc;AAEnC,SAAO,EAAE,UAAU,MAAM,aAAa,MAAM;AAC9C,GAtH2B;AAwHpB,IAAM,mBAAmB,wBAAC,MAAM,YAAY;AACjD,QAAM,OAAO,QAAQ,KAAK,EAAE,QAAQ;AACpC,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACrB,GAJgC;AAYzB,SAAS,mBAAmB,QAAQ,GAAG,GAAG,QAAQ;AACvD,SAAO,OACJ,OAAO,WAAW,cAAc,EAChC;AAAA,IACC;AAAA,IACA,OACG,IAAI,SAAU,GAAG;AAChB,aAAO,EAAE,IAAI,MAAM,EAAE;AAAA,IACvB,CAAC,EACA,KAAK,GAAG;AAAA,EACb,EACC,KAAK,SAAS,iBAAiB,EAC/B,KAAK,aAAa,eAAe,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG;AAChE;AAbgB;;;ACtIhB,IAAM,OAAO,8BAAO,QAAQ,SAAS;AACnC,QAAM,gBAAgB,KAAK,iBAAiBI,WAAU,EAAE,UAAU;AAClE,MAAI,CAAC,eAAe;AAClB,SAAK,cAAc;AAAA,EACrB;AACA,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,UAAU,KAAK;AAAA,IACf;AAAA,EACF;AAEA,MAAI,KAAK,cAAc,KAAK,OAAO;AAEnC,QAAMC,QAAO,SAAS,OAAO,QAAQ,cAAc;AAEnD,EAAAA,MACG,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,WAAW,EACvC,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,WAAW,EACxC,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EACvC,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AAE5C,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAUC,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GA/Ba;AAiCb,IAAO,eAAQ;;;AV5Bf,IAAM,cAAc,wBAAC,QAAQ;AAC3B,MAAI,KAAK;AACP,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AACT,GALoB;AAMpB,IAAM,qBAAqB,wBAAC,MAAM,iBAAiB;AACjD,SAAO,GAAG,eAAe,eAAe,cAAc,GAAG,YAAY,KAAK,OAAO,CAAC,IAAI;AAAA,IACpF,KAAK;AAAA,EACP,CAAC;AACH,GAJ2B;AAM3B,IAAM,WAAW,8BAAO,QAAQ,SAAS;AACvC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,IAAI;AAEd,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,EACpB;AAEA,MAAI,KAAK,wBAAwB;AAEjC,QAAM,eAAe,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAC9D,eAAa,KAAK,SAAS,KAAK,KAAK;AACrC,mBAAiB,MAAM,YAAY;AAEnC,OAAK,YAAY,SAAUC,QAAO;AAChC,QAAI,KAAK,kBAAkB;AAC3B,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GA/BiB;AAiCjB,IAAM,SAAS,wBAAC,QAAQ,SAAS;AAC/B,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,cAAc,EAC5B,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,QAAM,IAAI;AACV,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,IAAI,EAAE;AAAA,IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE;AAAA,EACpB;AAEA,QAAMC,UAAS,SAAS,OAAO,WAAW,cAAc,EAAE;AAAA,IACxD;AAAA,IACA,OACG,IAAI,SAAU,GAAG;AAChB,aAAO,EAAE,IAAI,MAAM,EAAE;AAAA,IACvB,CAAC,EACA,KAAK,GAAG;AAAA,EACb;AAEA,EAAAA,QAAO,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AACpF,OAAK,QAAQ;AACb,OAAK,SAAS;AAEd,OAAK,YAAY,SAAUD,QAAO;AAChC,WAAO,kBAAU,OAAO,MAAM,IAAIA,MAAK;AAAA,EACzC;AAEA,SAAO;AACT,GAhCe;AAkCf,IAAM,UAAU,8BAAO,QAAQ,SAAS;AACtC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI;AACV,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AACpC,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,EACpB;AAEA,QAAM,MAAM,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACrD,MAAI,KAAK,SAAS,KAAK,KAAK;AAC5B,mBAAiB,MAAM,GAAG;AAE1B,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GA9BgB;AAgChB,IAAM,cAAc,8BAAO,QAAQ,SAAS;AAC1C,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,QAAW,IAAI;AAE1E,QAAM,IAAI;AACV,QAAM,IAAI,KAAK,SAAS,IAAI,KAAK;AACjC,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AAEpC,QAAM,SAAS,eAAe,KAAK,YAAY,MAAM,IAAI;AAEzD,QAAM,aAAa,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAC5D,aAAW,KAAK,SAAS,KAAK,KAAK;AACnC,mBAAiB,MAAM,UAAU;AAEjC,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GAnBoB;AAqBpB,IAAM,sBAAsB,8BAAO,QAAQ,SAAS;AAClD,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IACnB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,EACpB;AAEA,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAE3B,OAAK,QAAQ,IAAI;AACjB,OAAK,SAAS;AAEd,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GA7B4B;AA+B5B,IAAM,aAAa,8BAAO,QAAQ,SAAS;AACzC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,mBAAmB,IAAI,GAAG,IAAI;AAEzF,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAI,KAAK,IAAK,GAAG,GAAG,EAAE;AAAA,IACxB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAE;AAAA,IACrB,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,CAAC,EAAE;AAAA,IAC5B,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,EACpB;AAEA,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AAEzB,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GArBmB;AAuBnB,IAAM,YAAY,8BAAO,QAAQ,SAAS;AACxC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAI,IAAI,IAAK,GAAG,GAAG,EAAE;AAAA,IACvB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAE;AAAA,IACrB,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,CAAC,EAAE;AAAA,IAC5B,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,EACrB;AAEA,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AAEzB,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GA1BkB;AA4BlB,IAAM,YAAY,8BAAO,QAAQ,SAAS;AACxC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAI,KAAK,IAAK,GAAG,GAAG,EAAE;AAAA,IACxB,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,EAAE;AAAA,IAC3B,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IACtB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,EACpB;AAEA,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AAEzB,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GA1BkB;AA4BlB,IAAM,gBAAgB,8BAAO,QAAQ,SAAS;AAC5C,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAE;AAAA,IACrB,EAAE,GAAG,IAAK,IAAI,IAAK,GAAG,GAAG,CAAC,EAAE;AAAA,IAC5B,EAAE,GAAI,KAAK,IAAK,GAAG,GAAG,CAAC,EAAE;AAAA,EAC3B;AAEA,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AAEzB,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GA1BsB;AA4BtB,IAAM,uBAAuB,8BAAO,QAAQ,SAAS;AACnD,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAE;AAAA,IACrB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IACtB,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,EAChB;AAEA,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AAEzB,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GA3B6B;AA6B7B,IAAM,WAAW,8BAAO,QAAQ,SAAS;AACvC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,MAAM,MAAM,IAAI;AAC3B,QAAM,IAAI,KAAK,SAAS,KAAK,KAAK;AAElC,QAAM,QACJ,SACA,KACA,QACA,KACA,MACA,KACA,YACA,IACA,UACA,KACA,MACA,KACA,YACA,CAAC,IACD,YACA,IACA,QACA,KACA,MACA,KACA,YACA,IACA,YACA,CAAC;AAEH,QAAM,KAAK,SACR,KAAK,kBAAkB,EAAE,EACzB,OAAO,QAAQ,cAAc,EAC7B,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,KAAK,KAAK,EACf,KAAK,aAAa,eAAe,CAAC,IAAI,IAAI,MAAM,EAAE,IAAI,IAAI,MAAM,GAAG;AAEtE,mBAAiB,MAAM,EAAE;AAEzB,OAAK,YAAY,SAAUA,QAAO;AAChC,UAAM,MAAM,kBAAU,KAAK,MAAMA,MAAK;AACtC,UAAM,IAAI,IAAI,IAAI,KAAK;AAEvB,QACE,MAAM,MACL,KAAK,IAAI,CAAC,IAAI,KAAK,QAAQ,KACzB,KAAK,IAAI,CAAC,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KACjF;AAGA,UAAI,IAAI,KAAK,MAAM,IAAK,IAAI,KAAM,KAAK;AACvC,UAAI,KAAK,GAAG;AACV,YAAI,KAAK,KAAK,CAAC;AAAA,MACjB;AACA,UAAI,KAAK;AACT,UAAIA,OAAM,IAAI,KAAK,IAAI,GAAG;AACxB,YAAI,CAAC;AAAA,MACP;AAEA,UAAI,KAAK;AAAA,IACX;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT,GA3EiB;AA6EjB,IAAM,OAAO,8BAAO,QAAQ,SAAS;AACnC,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,UAAU,KAAK,UAAU,MAAM,KAAK;AAAA,IACpC;AAAA,EACF;AAGA,QAAME,QAAO,SAAS,OAAO,QAAQ,cAAc;AAKnD,QAAM,aAAa,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,KAAK;AACpE,QAAM,cAAc,KAAK,aAAa,KAAK,SAAS,KAAK,SAAS,KAAK;AACvE,QAAM,IAAI,KAAK,aAAa,CAAC,aAAa,IAAI,CAAC,KAAK,QAAQ,IAAI;AAChE,QAAM,IAAI,KAAK,aAAa,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,IAAI;AAClE,EAAAA,MACG,KAAK,SAAS,uBAAuB,EACrC,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,UAAU,EACxB,KAAK,UAAU,WAAW;AAE7B,MAAI,KAAK,OAAO;AACd,UAAM,WAAW,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC;AAChD,QAAI,KAAK,MAAM,SAAS;AACtB,+BAAyBA,OAAM,KAAK,MAAM,SAAS,YAAY,WAAW;AAC1E,eAAS,OAAO,SAAS;AAAA,IAC3B;AACA,aAAS,QAAQ,CAAC,YAAY;AAC5B,UAAI,KAAK,yBAAyB,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAEA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAUF,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GA9Ca;AAgDb,IAAM,YAAY,8BAAO,QAAQ,SAAS;AACxC,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,UAAU,KAAK;AAAA,IACf;AAAA,EACF;AAGA,QAAME,QAAO,SAAS,OAAO,QAAQ,cAAc;AAInD,QAAM,aAAa,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,KAAK;AACpE,QAAM,cAAc,KAAK,aAAa,KAAK,SAAS,KAAK,SAAS,KAAK;AACvE,QAAM,IAAI,KAAK,aAAa,CAAC,aAAa,IAAI,CAAC,KAAK,QAAQ,IAAI;AAChE,QAAM,IAAI,KAAK,aAAa,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,IAAI;AAClE,EAAAA,MACG,KAAK,SAAS,yCAAyC,EACvD,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,UAAU,EACxB,KAAK,UAAU,WAAW;AAE7B,MAAI,KAAK,OAAO;AACd,UAAM,WAAW,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC;AAChD,QAAI,KAAK,MAAM,SAAS;AACtB,+BAAyBA,OAAM,KAAK,MAAM,SAAS,YAAY,WAAW;AAC1E,eAAS,OAAO,SAAS;AAAA,IAC3B;AACA,aAAS,QAAQ,CAAC,YAAY;AAC5B,UAAI,KAAK,yBAAyB,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAEA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAUF,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GA7CkB;AA+ClB,IAAM,YAAY,8BAAO,QAAQ,SAAS;AACxC,QAAM,EAAE,SAAS,IAAI,MAAM,YAAY,QAAQ,MAAM,SAAS,IAAI;AAElE,MAAI,MAAM,cAAc,KAAK,KAAK;AAElC,QAAME,QAAO,SAAS,OAAO,QAAQ,cAAc;AAGnD,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,EAAAA,MAAK,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AACzD,WAAS,KAAK,SAAS,iBAAiB;AAExC,MAAI,KAAK,OAAO;AACd,UAAM,WAAW,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC;AAChD,QAAI,KAAK,MAAM,SAAS;AACtB,+BAAyBA,OAAM,KAAK,MAAM,SAAS,YAAY,WAAW;AAC1E,eAAS,OAAO,SAAS;AAAA,IAC3B;AACA,aAAS,QAAQ,CAAC,YAAY;AAC5B,UAAI,KAAK,yBAAyB,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAEA,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAUF,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GA/BkB;AAuClB,SAAS,yBAAyBE,OAAM,SAAS,YAAY,aAAa;AACxE,QAAM,kBAAkB,CAAC;AACzB,QAAM,YAAY,wBAAC,WAAW;AAC5B,oBAAgB,KAAK,QAAQ,CAAC;AAAA,EAChC,GAFkB;AAGlB,QAAM,aAAa,wBAAC,WAAW;AAC7B,oBAAgB,KAAK,GAAG,MAAM;AAAA,EAChC,GAFmB;AAGnB,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,gBAAgB;AAC1B,cAAU,UAAU;AAAA,EACtB,OAAO;AACL,eAAW,UAAU;AAAA,EACvB;AACA,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,kBAAkB;AAC5B,cAAU,WAAW;AAAA,EACvB,OAAO;AACL,eAAW,WAAW;AAAA,EACxB;AACA,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,mBAAmB;AAC7B,cAAU,UAAU;AAAA,EACtB,OAAO;AACL,eAAW,UAAU;AAAA,EACvB;AACA,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,iBAAiB;AAC3B,cAAU,WAAW;AAAA,EACvB,OAAO;AACL,eAAW,WAAW;AAAA,EACxB;AACA,EAAAA,MAAK,KAAK,oBAAoB,gBAAgB,KAAK,GAAG,CAAC;AACzD;AAjCS;AAmCT,IAAM,gBAAgB,8BAAO,QAAQ,SAAS;AAG5C,MAAIC;AACJ,MAAI,CAAC,KAAK,SAAS;AACjB,IAAAA,WAAU;AAAA,EACZ,OAAO;AACL,IAAAA,WAAU,UAAU,KAAK;AAAA,EAC3B;AAEA,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAASA,QAAO,EACrB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAGnC,QAAMD,QAAO,SAAS,OAAO,QAAQ,cAAc;AAEnD,QAAM,YAAY,SAAS,OAAO,MAAM;AAExC,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAExD,QAAM,QAAQ,KAAK,UAAU,OAAO,KAAK,UAAU,KAAK,IAAI,KAAK;AAGjE,MAAI,QAAQ;AACZ,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,MAAM,CAAC;AAAA,EACjB,OAAO;AACL,YAAQ;AAAA,EACV;AACA,MAAI,KAAK,oBAAoB,OAAO,OAAO,OAAO,UAAU,QAAQ;AAEpE,QAAM,OAAO,MAAM,KAAK,EAAE,YAAY,MAAM,oBAAY,OAAO,KAAK,YAAY,MAAM,IAAI,CAAC;AAC3F,MAAI,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AACjC,MAAI,SAASE,WAAU,EAAE,UAAU,UAAU,GAAG;AAC9C,UAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,UAAM,KAAKC,QAAO,IAAI;AACtB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,MAAI,KAAK,UAAU,KAAK;AACxB,QAAM,WAAW,MAAM,MAAM,GAAG,MAAM,MAAM;AAC5C,MAAI,WAAW,KAAK,QAAQ;AAC5B,QAAM,QAAQ,MACX,KAAK,EACL;AAAA,IACC,MAAM;AAAA,MACJ,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI;AAAA,MACzC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEF,MAAI,SAASD,WAAU,EAAE,UAAU,UAAU,GAAG;AAC9C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAKC,QAAO,KAAK;AACvB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AAGA,QAAM,cAAc,KAAK,UAAU;AACnC,EAAAA,QAAO,KAAK,EAAE;AAAA,IACZ;AAAA,IACA;AAAA,KAEG,KAAK,QAAQ,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,SAAS,KACnE,QACC,SAAS,SAAS,cAAc,KACjC;AAAA,EACJ;AACA,EAAAA,QAAO,IAAI,EAAE;AAAA,IACX;AAAA,IACA;AAAA,KAEG,KAAK,QAAQ,SAAS,QAAQ,IAAI,EAAE,SAAS,QAAQ,KAAK,SAAS,KACpE;AAAA,EAGJ;AAIA,SAAO,MAAM,KAAK,EAAE,QAAQ;AAG5B,QAAM;AAAA,IACJ;AAAA,IACA,eAAe,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,cAAc,KAAK;AAAA,EACjF;AAEA,EAAAH,MACG,KAAK,SAAS,mBAAmB,EACjC,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,WAAW,EACvC,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,WAAW,EACxC,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EACvC,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AAE5C,YACG,KAAK,SAAS,SAAS,EACvB,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,WAAW,EACxC,KAAK,MAAM,KAAK,QAAQ,IAAI,WAAW,EACvC,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW,EACzE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW;AAE5E,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAUF,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GApHsB;AAsHtB,IAAM,UAAU,8BAAO,QAAQ,SAAS;AACtC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AAGpC,QAAME,QAAO,SACV,OAAO,QAAQ,cAAc,EAC7B,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,MAAM,IAAI,CAAC,EAChB,KAAK,MAAM,IAAI,CAAC,EAChB,KAAK,KAAK,CAAC,IAAI,CAAC,EAChB,KAAK,KAAK,CAAC,IAAI,CAAC,EAChB,KAAK,SAAS,CAAC,EACf,KAAK,UAAU,CAAC;AAEnB,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAUF,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GA7BgB;AA+BhB,IAAMM,UAAS,8BAAO,QAAQ,SAAS;AACrC,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AACA,QAAMA,UAAS,SAAS,OAAO,UAAU,cAAc;AAGvD,EAAAA,QACG,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,KAAK,QAAQ,IAAI,WAAW,EACtC,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EACvC,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AAE5C,MAAI,KAAK,aAAa;AAEtB,mBAAiB,MAAMA,OAAM;AAE7B,OAAK,YAAY,SAAUN,QAAO;AAChC,QAAI,KAAK,oBAAoB,MAAM,KAAK,QAAQ,IAAI,aAAaA,MAAK;AACtE,WAAO,kBAAU,OAAO,MAAM,KAAK,QAAQ,IAAI,aAAaA,MAAK;AAAA,EACnE;AAEA,SAAO;AACT,GA5Be;AA8Bf,IAAM,eAAe,8BAAO,QAAQ,SAAS;AAC3C,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AACA,QAAM,MAAM;AACZ,QAAM,cAAc,SAAS,OAAO,KAAK,cAAc;AACvD,QAAM,cAAc,YAAY,OAAO,QAAQ;AAC/C,QAAM,cAAc,YAAY,OAAO,QAAQ;AAE/C,cAAY,KAAK,SAAS,KAAK,KAAK;AAGpC,cACG,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,KAAK,QAAQ,IAAI,cAAc,GAAG,EAC5C,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,MAAM,CAAC,EACjD,KAAK,UAAU,KAAK,SAAS,KAAK,UAAU,MAAM,CAAC;AAEtD,cACG,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,KAAK,KAAK,QAAQ,IAAI,WAAW,EACtC,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EACvC,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AAE5C,MAAI,KAAK,mBAAmB;AAE5B,mBAAiB,MAAM,WAAW;AAElC,OAAK,YAAY,SAAUA,QAAO;AAChC,QAAI,KAAK,0BAA0B,MAAM,KAAK,QAAQ,IAAI,cAAc,KAAKA,MAAK;AAClF,WAAO,kBAAU,OAAO,MAAM,KAAK,QAAQ,IAAI,cAAc,KAAKA,MAAK;AAAA,EACzE;AAEA,SAAO;AACT,GAzCqB;AA2CrB,IAAM,aAAa,8BAAO,QAAQ,SAAS;AACzC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,mBAAmB,MAAM,MAAS;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE;AAAA,IACf,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,EAChB;AAEA,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AAEzB,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,QAAQ,MAAM,QAAQA,MAAK;AAAA,EAC9C;AAEA,SAAO;AACT,GAhCmB;AAkCnB,IAAM,QAAQ,wBAAC,QAAQ,SAAS;AAC9B,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,cAAc,EAC5B,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACnC,QAAMM,UAAS,SAAS,OAAO,UAAU,cAAc;AAGvD,EAAAA,QAAO,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAEpF,mBAAiB,MAAMA,OAAM;AAE7B,OAAK,YAAY,SAAUN,QAAO;AAChC,WAAO,kBAAU,OAAO,MAAM,GAAGA,MAAK;AAAA,EACxC;AAEA,SAAO;AACT,GAjBc;AAmBd,IAAM,WAAW,wBAAC,QAAQ,MAAM,QAAQ;AACtC,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,cAAc,EAC5B,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAEnC,MAAI,QAAQ;AACZ,MAAI,SAAS;AAEb,MAAI,QAAQ,MAAM;AAChB,YAAQ;AACR,aAAS;AAAA,EACX;AAEA,QAAM,QAAQ,SACX,OAAO,MAAM,EACb,KAAK,KAAM,KAAK,QAAS,CAAC,EAC1B,KAAK,KAAM,KAAK,SAAU,CAAC,EAC3B,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,SAAS,WAAW;AAE5B,mBAAiB,MAAM,KAAK;AAC5B,OAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AAC3C,OAAK,QAAQ,KAAK,QAAQ,KAAK,UAAU;AACzC,OAAK,YAAY,SAAUA,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GA9BiB;AAgCjB,IAAM,MAAM,wBAAC,QAAQ,SAAS;AAC5B,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAAS,cAAc,EAC5B,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACnC,QAAM,cAAc,SAAS,OAAO,UAAU,cAAc;AAC5D,QAAMM,UAAS,SAAS,OAAO,UAAU,cAAc;AAEvD,EAAAA,QAAO,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAEpF,cAAY,KAAK,SAAS,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAEvF,mBAAiB,MAAMA,OAAM;AAE7B,OAAK,YAAY,SAAUN,QAAO;AAChC,WAAO,kBAAU,OAAO,MAAM,GAAGA,MAAK;AAAA,EACxC;AAEA,SAAO;AACT,GAnBY;AAqBZ,IAAM,YAAY,8BAAO,QAAQ,SAAS;AACxC,QAAM,cAAc,KAAK,UAAU;AACnC,QAAM,aAAa;AACnB,QAAM,aAAa;AAEnB,MAAIG;AACJ,MAAI,CAAC,KAAK,SAAS;AACjB,IAAAA,WAAU;AAAA,EACZ,OAAO;AACL,IAAAA,WAAU,UAAU,KAAK;AAAA,EAC3B;AAEA,QAAM,WAAW,OACd,OAAO,GAAG,EACV,KAAK,SAASA,QAAO,EACrB,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAGnC,QAAMD,QAAO,SAAS,OAAO,QAAQ,cAAc;AACnD,QAAM,UAAU,SAAS,OAAO,MAAM;AACtC,QAAM,aAAa,SAAS,OAAO,MAAM;AACzC,MAAI,WAAW;AACf,MAAI,YAAY;AAEhB,QAAM,iBAAiB,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACjE,MAAI,cAAc;AAClB,QAAM,eAAe,KAAK,UAAU,cAAc,CAAC;AAGnD,QAAM,qBAAqB,KAAK,UAAU,YAAY,CAAC,IACnD,SAAM,KAAK,UAAU,YAAY,CAAC,IAAI,SACtC;AACJ,QAAM,iBAAiB,eACpB,KAAK,EACL,YAAY,MAAM,oBAAY,oBAAoB,KAAK,YAAY,MAAM,IAAI,CAAC;AACjF,MAAI,gBAAgB,eAAe,QAAQ;AAC3C,MAAI,SAASE,WAAU,EAAE,UAAU,UAAU,GAAG;AAC9C,UAAM,MAAM,eAAe,SAAS,CAAC;AACrC,UAAM,KAAKC,QAAO,cAAc;AAChC,oBAAgB,IAAI,sBAAsB;AAC1C,OAAG,KAAK,SAAS,cAAc,KAAK;AACpC,OAAG,KAAK,UAAU,cAAc,MAAM;AAAA,EACxC;AACA,MAAI,KAAK,UAAU,YAAY,CAAC,GAAG;AACjC,iBAAa,cAAc,SAAS;AACpC,gBAAY,cAAc;AAAA,EAC5B;AAEA,MAAI,mBAAmB,KAAK,UAAU;AAEtC,MAAI,KAAK,UAAU,SAAS,UAAa,KAAK,UAAU,SAAS,IAAI;AACnE,QAAID,WAAU,EAAE,UAAU,YAAY;AACpC,0BAAoB,SAAS,KAAK,UAAU,OAAO;AAAA,IACrD,OAAO;AACL,0BAAoB,MAAM,KAAK,UAAU,OAAO;AAAA,IAClD;AAAA,EACF;AACA,QAAM,kBAAkB,eACrB,KAAK,EACL,YAAY,MAAM,oBAAY,kBAAkB,KAAK,YAAY,MAAM,IAAI,CAAC;AAC/E,EAAAC,QAAO,eAAe,EAAE,KAAK,SAAS,YAAY;AAClD,MAAI,iBAAiB,gBAAgB,QAAQ;AAC7C,MAAI,SAASD,WAAU,EAAE,UAAU,UAAU,GAAG;AAC9C,UAAM,MAAM,gBAAgB,SAAS,CAAC;AACtC,UAAM,KAAKC,QAAO,eAAe;AACjC,qBAAiB,IAAI,sBAAsB;AAC3C,OAAG,KAAK,SAAS,eAAe,KAAK;AACrC,OAAG,KAAK,UAAU,eAAe,MAAM;AAAA,EACzC;AACA,eAAa,eAAe,SAAS;AACrC,MAAI,eAAe,QAAQ,UAAU;AACnC,eAAW,eAAe;AAAA,EAC5B;AACA,QAAM,kBAAkB,CAAC;AACzB,OAAK,UAAU,QAAQ,QAAQ,OAAO,WAAW;AAC/C,UAAM,aAAa,OAAO,kBAAkB;AAC5C,QAAI,aAAa,WAAW;AAC5B,QAAID,WAAU,EAAE,UAAU,YAAY;AACpC,mBAAa,WAAW,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;AAAA,IACpE;AACA,UAAM,MAAM,eACT,KAAK,EACL;AAAA,MACC,MAAM;AAAA,QACJ;AAAA,QACA,WAAW,WAAW,WAAW,WAAW,KAAK;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACF,QAAI,OAAO,IAAI,QAAQ;AACvB,QAAI,SAASA,WAAU,EAAE,UAAU,UAAU,GAAG;AAC9C,YAAM,MAAM,IAAI,SAAS,CAAC;AAC1B,YAAM,KAAKC,QAAO,GAAG;AACrB,aAAO,IAAI,sBAAsB;AACjC,SAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,SAAG,KAAK,UAAU,KAAK,MAAM;AAAA,IAC/B;AACA,QAAI,KAAK,QAAQ,UAAU;AACzB,iBAAW,KAAK;AAAA,IAClB;AACA,iBAAa,KAAK,SAAS;AAC3B,oBAAgB,KAAK,GAAG;AAAA,EAC1B,CAAC;AAED,eAAa;AAEb,QAAM,eAAe,CAAC;AACtB,OAAK,UAAU,QAAQ,QAAQ,OAAO,WAAW;AAC/C,UAAM,aAAa,OAAO,kBAAkB;AAC5C,QAAI,cAAc,WAAW;AAC7B,QAAID,WAAU,EAAE,UAAU,YAAY;AACpC,oBAAc,YAAY,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;AAAA,IACtE;AACA,UAAM,MAAM,eACT,KAAK,EACL;AAAA,MACC,MAAM;AAAA,QACJ;AAAA,QACA,WAAW,WAAW,WAAW,WAAW,KAAK;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACF,QAAI,OAAO,IAAI,QAAQ;AACvB,QAAI,SAASA,WAAU,EAAE,UAAU,UAAU,GAAG;AAC9C,YAAM,MAAM,IAAI,SAAS,CAAC;AAC1B,YAAM,KAAKC,QAAO,GAAG;AACrB,aAAO,IAAI,sBAAsB;AACjC,SAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,SAAG,KAAK,UAAU,KAAK,MAAM;AAAA,IAC/B;AACA,QAAI,KAAK,QAAQ,UAAU;AACzB,iBAAW,KAAK;AAAA,IAClB;AACA,iBAAa,KAAK,SAAS;AAE3B,iBAAa,KAAK,GAAG;AAAA,EACvB,CAAC;AAED,eAAa;AAKb,MAAI,cAAc;AAChB,QAAIE,UAAS,WAAW,cAAc,SAAS;AAC/C,IAAAF,QAAO,cAAc,EAAE;AAAA,MACrB;AAAA,MACA,iBAAkB,KAAK,WAAY,IAAIE,UAAS,OAAQ,KAAK,YAAa,IAAI;AAAA,IAChF;AACA,kBAAc,cAAc,SAAS;AAAA,EACvC;AAEA,MAAI,SAAS,WAAW,eAAe,SAAS;AAChD,EAAAF,QAAO,eAAe,EAAE;AAAA,IACtB;AAAA,IACA,iBACI,KAAK,WAAY,IAAI,SACvB,QACE,KAAK,YAAa,IAAI,eACxB;AAAA,EACJ;AACA,iBAAe,eAAe,SAAS;AAEvC,UACG,KAAK,SAAS,SAAS,EACvB,KAAK,MAAM,CAAC,WAAW,IAAI,WAAW,EACtC,KAAK,MAAM,WAAW,IAAI,WAAW,EACrC,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW,EAClE,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW;AAErE,iBAAe;AAEf,kBAAgB,QAAQ,CAAC,QAAQ;AAC/B,IAAAA,QAAO,GAAG,EAAE;AAAA,MACV;AAAA,MACA,gBACE,CAAC,WAAW,IACZ,QACE,KAAK,YAAa,IAAI,cAAc,aAAa,KACnD;AAAA,IACJ;AAEA,UAAM,aAAa,KAAK,QAAQ;AAChC,oBAAgB,YAAY,UAAU,KAAK;AAAA,EAC7C,CAAC;AAED,iBAAe;AACf,aACG,KAAK,SAAS,SAAS,EACvB,KAAK,MAAM,CAAC,WAAW,IAAI,WAAW,EACtC,KAAK,MAAM,WAAW,IAAI,WAAW,EACrC,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW,EAClE,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW;AAErE,iBAAe;AAEf,eAAa,QAAQ,CAAC,QAAQ;AAC5B,IAAAA,QAAO,GAAG,EAAE;AAAA,MACV;AAAA,MACA,gBAAgB,CAAC,WAAW,IAAI,QAAS,KAAK,YAAa,IAAI,eAAe;AAAA,IAChF;AACA,UAAM,aAAa,KAAK,QAAQ;AAChC,oBAAgB,YAAY,UAAU,KAAK;AAAA,EAC7C,CAAC;AAED,EAAAH,MACG,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,SAAS,mBAAmB,EACjC,KAAK,KAAK,CAAC,WAAW,IAAI,WAAW,EACrC,KAAK,KAAK,EAAE,YAAY,KAAK,WAAW,EACxC,KAAK,SAAS,WAAW,KAAK,OAAO,EACrC,KAAK,UAAU,YAAY,KAAK,OAAO;AAE1C,mBAAiB,MAAMA,KAAI;AAE3B,OAAK,YAAY,SAAUF,QAAO;AAChC,WAAO,kBAAU,KAAK,MAAMA,MAAK;AAAA,EACnC;AAEA,SAAO;AACT,GA9NkB;AAgOlB,IAAM,SAAS;AAAA,EACb,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAM;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AAAA,EACN;AACF;AAEA,IAAI,YAAY,CAAC;AAEV,IAAM,aAAa,8BAAO,MAAM,MAAM,kBAAkB;AAC7D,MAAI;AACJ,MAAI;AAGJ,MAAI,KAAK,MAAM;AACb,QAAI;AACJ,QAAIF,WAAU,EAAE,kBAAkB,WAAW;AAC3C,eAAS;AAAA,IACX,WAAW,KAAK,YAAY;AAC1B,eAAS,KAAK,cAAc;AAAA,IAC9B;AACA,YAAQ,KAAK,OAAO,OAAO,EAAE,KAAK,cAAc,KAAK,IAAI,EAAE,KAAK,UAAU,MAAM;AAChF,SAAK,MAAM,OAAO,KAAK,KAAK,EAAE,OAAO,MAAM,aAAa;AAAA,EAC1D,OAAO;AACL,SAAK,MAAM,OAAO,KAAK,KAAK,EAAE,MAAM,MAAM,aAAa;AACvD,YAAQ;AAAA,EACV;AACA,MAAI,KAAK,SAAS;AAChB,OAAG,KAAK,SAAS,KAAK,OAAO;AAAA,EAC/B;AACA,MAAI,KAAK,OAAO;AACd,OAAG,KAAK,SAAS,kBAAkB,KAAK,KAAK;AAAA,EAC/C;AAEA,YAAU,KAAK,EAAE,IAAI;AAErB,MAAI,KAAK,cAAc;AACrB,cAAU,KAAK,EAAE,EAAE,KAAK,SAAS,UAAU,KAAK,EAAE,EAAE,KAAK,OAAO,IAAI,YAAY;AAAA,EAClF;AACA,SAAO;AACT,GA/B0B;AAuCnB,IAAM,eAAe,wBAAC,SAAS;AACpC,QAAM,KAAK,UAAU,KAAK,EAAE;AAC5B,MAAI;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA,gBAAgB,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI;AAAA,EACzE;AACA,QAAMI,WAAU;AAChB,QAAM,OAAO,KAAK,QAAQ;AAC1B,MAAI,KAAK,aAAa;AACpB,OAAG;AAAA,MACD;AAAA,MACA,gBACG,KAAK,IAAI,OAAO,KAAK,QAAQ,KAC9B,QACC,KAAK,IAAI,KAAK,SAAS,IAAIA,YAC5B;AAAA,IACJ;AAAA,EACF,OAAO;AACL,OAAG,KAAK,aAAa,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI,GAAG;AAAA,EAClE;AACA,SAAO;AACT,GAvB4B;;;AJjpC5B,SAAS,iBAAiB,OAAcC,KAAa,aAAa,OAAO;AACvE,QAAM,SAAS;AAEf,MAAI,WAAW;AACf,OAAK,QAAQ,SAAS,UAAU,KAAK,GAAG;AACtC,gBAAY,QAAQ,WAAW,CAAC,GAAG,KAAK,GAAG;AAAA,EAC7C;AACA,aAAW,WAAW;AAGtB,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAIC;AAEJ,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK;AACH,eAAS;AACT,cAAQ;AACR;AAAA,IACF,KAAK;AACH,eAAS;AACT,cAAQ;AACR,MAAAA,WAAU;AACV;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF;AACE,cAAQ;AAAA,EACZ;AAEA,QAAM,SAAS,mBAAmB,QAAQ,UAAU,CAAC,CAAC;AAGtD,QAAM,aAAa,OAAO;AAE1B,QAAM,SAAS,OAAO,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE;AAEhE,QAAM,OAAO;AAAA,IACX,YAAY,OAAO;AAAA,IACnB;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO,OAAO;AAAA,IACd,IAAI,OAAO;AAAA,IACX,YAAY,OAAO;AAAA,IACnB,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,IACf,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,IACV;AAAA,IACA,WAAW;AAAA,IACX,MAAM,OAAO;AAAA,IACb,SAASA,YAAW,UAAU,GAAG,OAAO,WAAW;AAAA,EACrD;AACA,SAAO;AACT;AA1GS;AA2GT,eAAe,mBACb,MACA,OACAD,KACA;AACA,QAAM,OAAO,iBAAiB,OAAOA,KAAI,KAAK;AAC9C,MAAI,KAAK,SAAS,SAAS;AACzB;AAAA,EACF;AAGA,QAAME,UAAS,UAAU;AACzB,QAAM,SAAS,MAAM,WAAW,MAAM,MAAM,EAAE,QAAAA,QAAO,CAAC;AACtD,QAAM,cAAc,OAAO,KAAK,EAAE,QAAQ;AAC1C,QAAM,MAAMF,IAAG,SAAS,KAAK,EAAE;AAC/B,MAAI,OAAO,EAAE,OAAO,YAAY,OAAO,QAAQ,YAAY,QAAQ,GAAG,GAAG,GAAG,GAAG,MAAM,OAAO;AAC5F,EAAAA,IAAG,SAAS,GAAG;AACf,SAAO,OAAO;AAChB;AAlBe;AAqBf,eAAsB,sBAAsB,MAAW,OAAcA,KAAS;AAC5E,QAAM,OAAO,iBAAiB,OAAOA,KAAI,IAAI;AAE7C,QAAM,MAAMA,IAAG,SAAS,KAAK,EAAE;AAC/B,MAAI,IAAI,SAAS,SAAS;AACxB,UAAME,UAAS,UAAU;AACzB,UAAM,WAAW,MAAM,MAAM,EAAE,QAAAA,QAAO,CAAC;AACvC,UAAM,YAAY,MAAM;AACxB,iBAAa,IAAI;AAAA,EACnB;AACF;AAVsB;AAYtB,eAAsB,kBACpB,MACAC,SACAH,KACA,WACA;AACA,aAAW,SAASG,SAAQ;AAC1B,UAAM,UAAU,MAAM,OAAOH,GAAE;AAC/B,QAAI,MAAM,UAAU;AAClB,YAAM,kBAAkB,MAAM,MAAM,UAAUA,KAAI,SAAS;AAAA,IAC7D;AAAA,EACF;AACF;AAZsB;AActB,eAAsB,oBAAoB,MAAWG,SAAiBH,KAAa;AACjF,QAAM,kBAAkB,MAAMG,SAAQH,KAAI,kBAAkB;AAC9D;AAFsB;AAItB,eAAsB,aACpB,MACAG,SACAH,KACA;AACA,QAAM,kBAAkB,MAAMG,SAAQH,KAAI,qBAAqB;AACjE;AANsB;AAQtB,eAAsB,YACpB,MACA,OACAG,SACAH,KACA,IACA;AACA,QAAM,IAAI,IAAa,eAAM;AAAA,IAC3B,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ,CAAC;AACD,IAAE,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,EACX,CAAC;AAED,aAAW,SAASG,SAAQ;AAC1B,QAAI,MAAM,MAAM;AACd,QAAE,QAAQ,MAAM,IAAI;AAAA,QAClB,OAAO,MAAM,KAAK;AAAA,QAClB,QAAQ,MAAM,KAAK;AAAA,QACnB,WAAW,MAAM;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,aAAW,QAAQ,OAAO;AAExB,QAAI,KAAK,SAAS,KAAK,KAAK;AAC1B,YAAM,aAAaH,IAAG,SAAS,KAAK,KAAK;AACzC,YAAM,WAAWA,IAAG,SAAS,KAAK,GAAG;AAErC,UAAI,YAAY,QAAQ,UAAU,MAAM;AACtC,cAAMI,SAAQ,WAAW;AACzB,cAAMC,OAAM,SAAS;AACrB,cAAM,SAAS;AAAA,UACb,EAAE,GAAGD,OAAM,GAAG,GAAGA,OAAM,EAAE;AAAA,UACzB,EAAE,GAAGA,OAAM,KAAKC,KAAI,IAAID,OAAM,KAAK,GAAG,GAAGA,OAAM,KAAKC,KAAI,IAAID,OAAM,KAAK,EAAE;AAAA,UACzE,EAAE,GAAGC,KAAI,GAAG,GAAGA,KAAI,EAAE;AAAA,QACvB;AAEA;AAAA,UACE;AAAA,UACA,EAAE,GAAG,KAAK,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG;AAAA,UAC5C;AAAA,YACE,GAAG;AAAA,YACH,cAAc,KAAK;AAAA,YACnB,gBAAgB,KAAK;AAAA,YACrB;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,KAAK,OAAO;AACd,gBAAM,gBAAgB,MAAM;AAAA,YAC1B,GAAG;AAAA,YACH,OAAO,KAAK;AAAA,YACZ,YAAY;AAAA,YACZ,cAAc,KAAK;AAAA,YACnB,gBAAgB,KAAK;AAAA,YACrB;AAAA,YACA,SAAS;AAAA,UACX,CAAC;AACD;AAAA,YACE,EAAE,GAAG,MAAM,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE;AAAA,YAC1C;AAAA,cACE,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA/EsB;;;AHpKf,IAAMC,cAAa,gCAAU,MAAW,SAAc;AAC3D,SAAO,QAAQ,GAAG,WAAW;AAC/B,GAF0B;AAInB,IAAM,OAAO,sCAClB,MACA,IACA,UACA,SACe;AACf,QAAM,EAAE,eAAe,OAAO,KAAK,IAAc,UAAU;AAC3D,QAAMC,MAAK,QAAQ;AACnB,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,SAAS,OAAO,EAAE;AAAA,EACrC;AACA,QAAM,OACJ,kBAAkB,YACd,SAAmC,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACjF,SAAmC,MAAM;AAE/C,QAAM,MACJ,kBAAkB,YACd,KAAK,OAAsB,QAAQ,EAAE,IAAI,IACzC,SAAiC,QAAQ,EAAE,IAAI;AAGrD,QAAMC,WAAU,CAAC,SAAS,UAAU,OAAO;AAG3C,kBAAc,KAAKA,UAAS,QAAQ,MAAM,EAAE;AAE5C,QAAM,KAAKD,IAAG,UAAU;AACxB,QAAM,QAAQA,IAAG,cAAc;AAC/B,QAAM,QAAQA,IAAG,SAAS;AAE1B,QAAM,QAAQ,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACnD,QAAM,oBAAoB,OAAO,IAAIA,GAAE;AACvC,QAAM,SAAS,OAAOA,GAAE;AACxB,QAAM,aAAa,OAAO,IAAIA,GAAE;AAChC,QAAM,YAAY,OAAO,OAAO,OAAOA,KAAI,EAAE;AAI7C,MAAI,QAAQ;AACV,UAAM,UAAU;AAChB,UAAM,cAAc,KAAK,IAAI,GAAG,KAAK,MAAM,SAAS,QAAQ,QAAQ,QAAQ,OAAO,CAAC;AACpF,UAAM,SAAS,QAAQ,SAAS,cAAc;AAC9C,UAAM,QAAQ,QAAQ,QAAQ;AAC9B,UAAM,EAAE,YAAY,IAAI;AACxB,qBAAiB,KAAK,QAAQ,OAAO,CAAC,CAAC,WAAW;AAClD,QAAI,MAAM,eAAe,QAAQ,OAAO;AACxC,QAAI;AAAA,MACF;AAAA,MACA,GAAG,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,QAAQ,EAAE,IAAI,QAAQ,SAAS,EAAE;AAAA,IAChF;AAAA,EACF;AACF,GArDoB;AAuDpB,IAAO,wBAAQ;AAAA,EACb;AAAA,EACA,YAAAD;AACF;;;AkBjEO,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AACV;", "names": ["o", "parser", "lexer", "getConfig", "sanitizeText", "clear", "channel", "getConfig", "db", "width", "getConfig", "select", "config", "getConfig", "select", "point", "select", "padding", "point", "point", "point", "point", "point", "select", "config", "getConfig", "classes", "select", "getConfig", "rect", "point", "point", "choice", "rect", "classes", "getConfig", "select", "circle", "diffX", "padding", "db", "padding", "config", "blocks", "start", "end", "getClasses", "db", "markers"]}