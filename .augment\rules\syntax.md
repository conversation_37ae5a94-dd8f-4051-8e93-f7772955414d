---
type: "always_apply"
---

Syntax Guide
Slidev's slides are written as Markdown files, which are called Slidev Markdowns. A presentation has a Slidev Markdown as its entry, which is ./slides.md by default, but you can change it by passing the file path as an argument to the CLI commands.

In a Slidev Markdown, not only the basic Markdown features can be used as usual, <PERSON><PERSON><PERSON> also provides additional features to enhance your slides. This section covers the syntax introduced by Slidev. Please make sure you know the basic Markdown syntax before reading this guide.

Slide Separators
Use --- padded with a new line to separate your slides.


# Title

Hello, **Slidev**!

---

# Slide 2

Use code blocks for highlighting:

```ts
console.log('Hello, World!')
```

---

# Slide 3

Use UnoCSS classes and Vue components to style and enrich your slides:

<div class="p-3">
  <Tweet id="..." />
</div>
Frontmatter & Headmatter
At the beginning of each slide, you can add an optional frontmatter to configure the slide. The first frontmatter block is called headmatter and can configure the whole slide deck. The rest are frontmatters for individual slides. Texts in the headmatter or the frontmatter should be an object in YAML format. For example:


---
theme: seriph
title: Welcome to Slidev
---

# Slide 1

The frontmatter of this slide is also the headmatter

---
layout: center
background: /background-1.png
class: text-white
---

# Slide 2

A page with the layout `center` and a background image

---

# Slide 3

A page without frontmatter

---
src: ./pages/4.md  # This slide only contains a frontmatter
---

---

# Slide 5
Configurations you can set are described in the Slides deck configurations and Per slide configurations sections.

To make the headmatter more readable, you can install the VSCode extension:


Block Frontmatter
The usual way to define frontmatters of slides is concise, but may lack of highlighting and formatter support. To solve this, you can use a YAML block at the very beginning of the slide content as the frontmatter of the slide:


---
theme: default
---

# Slide 1

---

```yaml
layout: quote
```

# Slide 2

---

# Slide 3
About headmatter

Headmatter in Slidev is exactly the usual called "frontmatter" of the a Markdown file, which is supported by most of the Markdown editors and formatters. So you can't use a YAML block as the headmatter of the whole slide deck.



Notes
You can also create presenter notes for each slide. They will show up in 📖 User Interface📖 User Interface for you to reference during presentations.

The comment blocks at the end of each slide are treated as the note of the slide:


---
layout: cover
---

# Slide 1

This is the cover page.

<!-- This is a **note** -->

---

# Slide 2

<!-- This is NOT a note because it is not at the end of the slide -->

The second page

<!--
This is _another_ note
-->
Basic Markdown and HTML are also supported in notes and will be rendered.

Click Markers
For some slides you may have longer notes that could be hard to find your place. Slidev supports click markers that allow highlighting and auto-scrolling to the section of notes from your corresponding content. Put [click] markers at the beginning of any line in your notes for the timing you need to go to another click. You may skip n clicks by using [click:{n+1}]. For example:


<!--
Content before the first click

[click] This will be highlighted after the first click

Also highlighted after the first click

- [click] This list element will be highlighted after the second click

[click:3] Last click (skip two clicks)
-->
Slidev divides the content between the click markers and highlights it in presenter notes, synchronized with your slide progress.



Code Blocks
One big reason that led to the creation of Slidev was the need to perfectly display code in slides. Consequently, you can use Markdown-flavored code blocks to highlight your code.


```ts
console.log('Hello, World!')
```
Slidev has Shiki built in as the syntax highlighter. Refer to Configure Shiki for more details.

More about code blocks:

Line Numbers
You can enable line numbering for all code blocks across the slides by setting lineNumbers: true in the headmatter, or enable each code block individually by setting lines: true.

You can also set the starting line for each code block and highlight the lines accordingly via {startLine: number}, which defaults to 1.


```ts {6,7}{lines:true,startLine:5}
function add(
  a: Ref<number> | number,
  b: Ref<number> | number
) {
  return computed(() => unref(a) + unref(b))
}
```
Note that you can use {*} as a placeholder of ✨ Line Highlighting✨ Line Highlighting:


```ts {*}{lines:true,startLine:5}
// ...
```

Max Height
If the code doesn't fit into one slide, you use the maxHeight to set a fixed height and enable scrolling:


```ts {2|3|7|12}{maxHeight:'100px'}
function add(
  a: Ref<number> | number,
  b: Ref<number> | number
) {
  return computed(() => unref(a) + unref(b))
}
/// ...as many lines as you want
const c = add(1, 2)
```
Note that you can use {*} as a placeholder of ✨ Line Highlighting✨ Line Highlighting:


```ts {*}{maxHeight:'100px'}
// ...
```

Line Highlighting
To highlight specific lines, simply add line numbers within brackets {}. Line numbers start counting from 1 by default.


```ts {2,3}
function add(
  a: Ref<number> | number,
  b: Ref<number> | number
) {
  return computed(() => unref(a) + unref(b))
}
```
Dynamic Line Highlighting
To change what's highlighted with multiple clicks, you can use | to separate each stage:


```ts {2-3|5|all}
function add(
  a: Ref<number> | number,
  b: Ref<number> | number
) {
  return computed(() => unref(a) + unref(b))
}
```
This will first highlight a: Ref<number> | number and b: Ref<number> | number, and then return computed(() => unref(a) + unref(b)) after one click, and lastly, the whole block.

You can set the line number to hide to hide the code block or none to not highlight any line:


```ts {hide|none}
function add(
  a: Ref<number> | number,
  b: Ref<number> | number
) {
  return computed(() => unref(a) + unref(b))
}
```
TIP

Learn more in the click animations guide.


Monaco Editor

Whenever you want to do some modification in the presentation, simply add {monaco} after the language id — it turns the block into a fully-featured Monaco editor!


```ts {monaco}
console.log('HelloWorld')
```
Learn more about Configuring Monaco.

Diff Editor
Monaco can also generate a diff between two code blocks. Use {monaco-diff} to turn the block into a Monaco diff editor and use ~~~ to separate the original and modified code!


```ts {monaco-diff}
console.log('Original text')
~~~
console.log('Modified text')
```

Configure Monaco
Create ./setup/monaco.ts with the following content:

./setup/monaco.ts

import { defineMonacoSetup } from '@slidev/types'

export default defineMonacoSetup(async (monaco) => {
  // use `monaco` to configure
})
Learn more about configuring Monaco.

TypeScript Types
When using TypeScript with Monaco, types for dependencies will be installed to the client-side automatically.


```ts {monaco}
import { ref } from 'vue'
import { useMouse } from '@vueuse/core'

const counter = ref(0)
```
In the example above, make sure vue and @vueuse/core are installed locally as dependencies / devDependencies, Slidev will handle the rest to get the types working for the editor automatically. When deployed as SPA, those types will also be bundled for static hosting.

Additional Types
Slidev will scan all the Monaco code blocks in your slides and import the types for those used libraries for you. In case it missed some, you can explicitly specify extra packages to import the types for:


---
monacoTypesAdditionalPackages:
  - lodash-es
  - foo
---
Auto Type Acquisition
You can optionally switch to load types from CDN by setting the following headmatter:


---
monacoTypesSource: ata
---
This feature is powered by @typescript/ata and runs completely on the client-side.

Configure Themes
Since v0.48.0, Monaco will reuse the Shiki theme you configured in Shiki's setup file, powered by @shikijs/monaco. You don't need to worry about it anymore and it will have a consistent style with the rest of your code blocks.

Configure the Editor
Available since v0.43.0

If you would like to customize the Monaco editor you may pass an editorOptions object that matches the Monaco IEditorOptions definition.


```ts {monaco} { editorOptions: { wordWrap:'on'} }
console.log('HelloWorld')
```
Alternatively if you would like these options to be applied to every Monaco instance, you can return them in the defineMonacoSetup function

./setup/monaco.ts

import { defineMonacoSetup } from '@slidev/types'

export default defineMonacoSetup(() => {
  return {
    editorOptions: {
      wordWrap: 'on'
    }
  }
})
Disabling
Since v0.48.0, the Monaco editor is enabled by default and only be bundled when you use it. If you want to disable it, you can set monaco to false in the frontmatter of your slide:


---
monaco: false # can also be `dev` or `build` to conditionally enable it
---
Configure Code Runners
To configure how the Monaco Runner runs the code, or to add support for custom languages, please reference Configure Code Runners.


Configure Code Runners
Define code runners for custom languages in your Monaco Editor.

By default, JavaScript, TypeScript runners are supported built-in. They run in the browser without a sandbox environment. If you want more advanced integrations, you can provide your own code runner that sends the code to a remote server, runs in a Web Worker, or anything, up to you.

Create ./setup/code-runners.ts with the following content:

setup/code-runners.ts

import { defineCodeRunnersSetup } from '@slidev/types'

export default defineCodeRunnersSetup(() => {
  return {
    async python(code, ctx) {
      // Somehow execute the code and return the result
      const result = await executePythonCodeRemotely(code)
      return {
        text: result
      }
    },
    html(code, ctx) {
      return {
        html: sanitizeHtml(code)
      }
    },
    // or other languages, key is the language id
  }
})
Runner Context
The second argument ctx is the runner context, which contains the following properties:


export interface CodeRunnerContext {
  /**
   * Options passed to runner via the `runnerOptions` prop.
   */
  options: Record<string, unknown>
  /**
   * Highlight code with shiki.
   */
  highlight: (code: string, lang: string, options?: Partial<CodeToHastOptions>) => string
  /**
   * Use (other) code runner to run code.
   */
  run: (code: string, lang: string) => Promise<CodeRunnerOutputs>
}
Runner Output
The runner can either return a text or HTML output, or an element to be mounted. Refer to https://github.com/slidevjs/slidev/blob/main/packages/types/src/code-runner.ts for more details.

Additional Runner Dependencies
By default, Slidev will scan the Markdown source and automatically import the necessary dependencies for the code runners. If you want to manually import dependencies, you can use the monacoRunAdditionalDeps option in the slide frontmatter:


monacoRunAdditionalDeps:
  - ./path/to/dependency
  - lodash-es

  Monaco Runner
Slidev also provides the Monaco Runner Editor, which allows you to run the code directly in the editor and see the result. Use {monaco-run} to turn the block into a Monaco Runner Editor.


```ts {monaco-run}
function distance(x: number, y: number) {
  return Math.sqrt(x ** 2 + y ** 2)
}
console.log(distance(3, 4))
```
It provides the editor with a "Run" button, and shows the result of the code execution right below the code block. You may also modify the code and the result will be re-evaluated on the fly.

By default it will automatically run the code when the slide is loaded; if you want to instead explicitly trigger the run, you can set {autorun:false}.


```ts {monaco-run} {autorun:false}
console.log('Click the play button to run me')
```
If you want to only show the output in certain clicks, you can use the showOutputAt prop. The value is the same as v-click.


```ts {monaco-run} {showOutputAt:'+1'}
console.log('Shown after 1 click')
```
Currently, Slidev supports running JavaScript and TypeScript code out-of-box. Refer to Custom Code Runners for custom language support.

You can also use the Import Code Snippets syntax combined with the {monaco-write} directive, to link your Monaco Editor with a file on your filesystem. This will allow you to edit the code directly in the editor and save the changes back to the file.


<<< ./some-file.ts {monaco-write}
When using this, be sure to back up your files beforehand, as the changes will be saved directly to the file.

hiki Magic Move
Shiki Magic Move enables you to have a granular transition between code changes, similar to Keynote's Magic Move. You can check the playground to see how it works.


In Slidev, we bind the magic-move to the clicks system. The syntax is to wrap multiple code blocks representing each step with ````md magic-move (mind it's 4 backticks), this will be transformed into one code block, that morphs to each step as you click.


````md magic-move
```js
console.log(`Step ${1}`)
```
```js
console.log(`Step ${1 + 1}`)
```
```ts
console.log(`Step ${3}` as string)
```
````
It's also possible to mix Magic Move with ✨ Line Highlighting✨ Line Highlighting and ✨ Line Numbers✨ Line Numbers, for example:


````md magic-move {at:4, lines: true} // [!code hl]
```js {*|1|2-5} // [!code hl]
let count = 1
function add() {
  count++
}
```

Non-code blocks in between as ignored, you can put some comments.

```js {*}{lines: false} // [!code hl]
let count = 1
const add = () => count += 1
```
````

TwoSlash Integration
TwoSlash is a powerful tool for rendering TypeScript code blocks with type information on hover or inlined. It's quite useful for preparing slides for JavaScript/TypeScript-related topics.

To use it, you can add twoslash to the code block's language identifier:


```ts twoslash
import { ref } from 'vue'

const count = ref(0)
//            ^?
```
It will be rendered as:


import { ref } from 'vue'

const count = ref(0)
Suggest changes to this page
Pager
Next page
All Features
ref<number>(value: number): Ref<number, number> (+1 overload)
Takes an inner value and returns a reactive and mutable ref object, which has a single property .value that points to the inner value.

@paramvalue - The object to wrap in the ref.
@seehttps://vuejs.org/api/reactivity-core.html#ref

Import Code Snippets
You can import code snippets from existing files via the following syntax:


<<< @/snippets/snippet.js
TIP

The value of @ corresponds to your package's root directory. It's recommended to put snippets in @/snippets in order to be compatible with the Monaco editor. Alternatively, you can also import from relative paths.

You can also use a VS Code region to only include the corresponding part of the code file:


<<< @/snippets/snippet.js#region-name
To explicitly specify the language of the imported code, you can add a language identifier after:


<<< @/snippets/snippet.js ts
Any code block features like line highlighting and Monaco editor are also supported:


<<< @/snippets/snippet.js {2,3|5}{lines:true}
<<< @/snippets/snippet.js ts {monaco}{height:200px}
Note that you can use {*} as a placeholder of ✨ Line Highlighting✨ Line Highlighting:


<<< @/snippets/snippet.js {*}{lines:true}

Code Groups
NOTE

This feature requires MDC Syntax. Enable mdc: true to use it.

You can group multiple code blocks like this:


::code-group

```sh [npm]
npm i @slidev/cli
```

```sh [yarn]
yarn add @slidev/cli
```

```sh [pnpm]
pnpm add @slidev/cli
```

::
Title Icon Matching
code groups, code block and Shiki Magic Move also supports the automatically icon matching by the title name.

code-groups-demo

INFO

By default, we provide some built-in icons, you can use them by install @iconify-json/vscode-icons.

Custom Icons
You can use any name from the iconify collection by using the ~icon~ syntax, for example:


```js [npm ~i-uil:github~]
console.log('Hello, GitHub!')
```
To make it work, you need to:

Install the icon's collection.

npm

yarn

pnpm

bun

npm add @iconify-json/uil
Add the icon to the uno.config.ts file.
uno.config.ts

import { defineConfig } from 'unocss'

export default defineConfig({
  safelist: [
    'i-uil:github',
  ],
})

LaTeX
Slidev comes with LaTeX support out-of-box, powered by KaTeX.

Inline
Surround your LaTeX with a single $ on each side for inline rendering.


$\sqrt{3x-1}+(1+x)^2$
Block
Use two ($$) for block rendering. This mode uses bigger symbols and centers the result.


$$
\begin{aligned}
\nabla \cdot \vec{E} &= \frac{\rho}{\varepsilon_0} \\
\nabla \cdot \vec{B} &= 0 \\
\nabla \times \vec{E} &= -\frac{\partial\vec{B}}{\partial t} \\
\nabla \times \vec{B} &= \mu_0\vec{J} + \mu_0\varepsilon_0\frac{\partial\vec{E}}{\partial t}
\end{aligned}
$$
Line Highlighting
To highlight specific lines, simply add line numbers within bracket {}. Line numbers start counting from 1 by default.


$$ {1|3|all}
\begin{aligned}
\nabla \cdot \vec{E} &= \frac{\rho}{\varepsilon_0} \\
\nabla \cdot \vec{B} &= 0 \\
\nabla \times \vec{E} &= -\frac{\partial\vec{B}}{\partial t} \\
\nabla \times \vec{B} &= \mu_0\vec{J} + \mu_0\varepsilon_0\frac{\partial\vec{E}}{\partial t}
\end{aligned}
$$
The at and finally options of code blocks are also available for LaTeX blocks.

Chemical equations
To enable the rendering of chemical equations, the mhchem KaTeX extension needs to be loaded.

Create vite.config.ts with the following content:


import 'katex/contrib/mhchem'

export default {}
Now chemical equations can be rendered properly.


$$
\displaystyle{\ce{B(OH)3 + H2O <--> B(OH)4^- + H+}}
$$
Learn more: Syntax

Mermaid Diagrams
You can also create diagrams/graphs from textual descriptions in your Markdown, powered by Mermaid.

Code blocks marked as mermaid will be converted to diagrams, for example:


```mermaid
sequenceDiagram
  Alice->John: Hello John, how are you?
  Note over Alice,John: A typical interaction
```
You can further pass an options object to it to specify the scaling and theming. The syntax of the object is a JavaScript object literal, you will need to add quotes (') for strings and use comma (,) between keys.


```mermaid {theme: 'neutral', scale: 0.8}
graph TD
B[Text] --> C{Decision}
C -->|One| D[Result 1]
C -->|Two| E[Result 2]
```
Visit the Mermaid Website for more information.

Slide Scope Styles
You can use the <style> tag in your Markdown to define styles for only the current slide.


# This is Red

<style>
h1 {
  color: red;
}
</style>

---

# Other slides are not affected
The <style> tag in Markdown is always scoped. As a result, a selector with a child combinator (.a > .b) is unusable as such; see the previous link. To have global styles, check out the customization section.

Powered by UnoCSS, you can directly use nested css and directives:


# Slidev

> Hello **world**

<style>
blockquote {
  strong {
    --uno: 'text-teal-500 dark:text-teal-400';
  }
}
</style>

Importing Slides
You can split your slides.md into multiple files for better reusability and organization. To do this, you can use the src frontmatter option to specify the path to the external markdown file. For example:


./slides.md

./pages/toc.md

# Title

This is a normal page

---
src: ./pages/toc.md // [!code highlight]
---

<!-- this page will be loaded from './pages/toc.md' -->

Contents here are ignored

---

# Page 4

Another normal page

---
src: ./pages/toc.md   # Reuse the same file // [!code highlight]
---
Importing Specific Slides
To reuse some of the slides inside another Markdown file, you can use the hash part of the import path:


---
src: ./another-presentation.md#2,5-7
---
This will import the slides 2, 5, 6, and 7 from ./another-presentation.md.

Frontmatter Merging
✨ Frontmatter Merging
✨ Frontmatter Merging
Merge frontmatter from multiple markdown files.
