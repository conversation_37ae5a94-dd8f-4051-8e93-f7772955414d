---
# You can also start simply with 'default'
theme: seriph
# some information about your   slides (markdown enabled)
title: 'Reading 1: Static Checking'
info: |
  ## Software Construction - Lecture 1
  An introduction to static checking, types, and the principles of good software design.

  Adapted from the curriculum of MIT 6.102.
# apply unocss classes to the current slide
class: text-center
# slide transition: https://sli.dev/guide/animations.html#slide-transitions
transition: slide-left
# enable MDC Syntax: https://sli.dev/features/mdc
mdc: true
---

# Reading 1: Static Checking

An introduction to safe, understandable, and maintainable code.

<div class="abs-br m-6 op-80">
Adapted from MIT 6.102
</div>

<!--
This is the cover slide.
-->

---
transition: fade-out
---

# Today's Objectives

Today’s class has two main topics:

1.  **Static Typing**: What it is and why it's a powerful tool for building reliable software.

2.  **The Big Three Properties of Good Software**: The fundamental goals we aim for in software engineering.

---

# A Simple Example: Calculating Total Price

As a running example, we'll write a function that calculates the total price of a number of items.

**The Task:**
Given the price of a single item and the quantity being purchased, calculate the total cost.

**Example:**
- If an item costs **$10** and the quantity is **5**, the total price is **$50**.
- If an item costs **$2.50** and the quantity is **4**, the total price is **$10**.

This simple example will help us see how different programming languages handle types and errors.

---
layout: two-cols
---

# Computing the Total Price

Here’s some code for a function that calculates the total price. We'll compare the Python and TypeScript versions.

::right::


<br>
<div class="text-sm">
<b>Key Syntax Differences:</b>
<ul>
<li>TypeScript uses <b>curly braces `{}`</b> for blocks.</li>
<li>TypeScript statements end with a <b>semicolon `;`</b>.</li>
<li>We use <b>`const`</b> to declare a variable that cannot be reassigned.</li>
</ul>
</div>

::left::

```python
# Python

def calculate_total_price(price, quantity):
  return price * quantity

# --- Calling the function ---
total = calculate_total_price(10, 5)
print(total) # Output: 50
```

-----

# What Are Types?

A **type** is a set of values, along with a set of operations that can be performed on those values.

TypeScript has several built-in types, including:

  - `number`: Represents both integers and floating-point numbers (e.g., `10`, `3.14`).
  - `boolean`: Represents `true` or `false`.
  - `string`: Represents a sequence of characters (e.g., `"Hello, world!"`).

**Operations** are functions that act on these values. They can have different syntaxes:

  - **Operator:** `price * quantity` (the `*` operation)
  - **Function:** `Math.round(3.14)` (the `round` operation)
  - **Method:** `"hello".toUpperCase()` (the `toUpperCase` operation)

-----

# Static Typing

TypeScript is a **statically-typed** language.

  - Types are checked at **compile time** (before the program runs).
  - The compiler can deduce the types of expressions. If `price` and `quantity` are declared as `number`, the compiler knows `price * quantity` will also be a `number`.
  - This allows your editor (like VS Code) to find bugs *while you are typing*.

<br>

In contrast, Python and JavaScript are **dynamically-typed**.

  - Types are checked at **runtime** (while the program is running).
  - This means you might not find a type-related bug until the code actually executes.

-----

# Catching Bugs Early with Static Checking

Static typing is a form of **static checking**, which means finding bugs automatically before the program runs. This is a powerful way to make your code safer.

Consider this broken line of code:

```ts {monaco}
// What happens if we pass a string instead of a number?
const wrongTotal = calculateTotalPrice("ten", 5);
```

  - In a **dynamically-typed** language like Python or plain JavaScript, this error wouldn't be found until this line runs, potentially crashing the program or producing a nonsensical result (`NaN`).

  - In **TypeScript**, the compiler sees this mistake immediately and gives you a **static error**:

    > `Argument of type 'string' is not assignable to parameter of type 'number'.`

This prevents a whole class of bugs from ever making it into your running program.

-----

# Static, Dynamic, and No Checking

It’s useful to think about three kinds of automatic checking a language can provide:

  - **Static Checking:** The bug is found automatically **before** the program runs.

      - *Example*: `calculateTotalPrice("ten", 5)` is caught by the TypeScript compiler.

  - **Dynamic Checking:** The bug is found automatically **during** execution.

      - *Example*: A check inside our function like `if (quantity < 0) { throw new Error("Quantity cannot be negative"); }`. This error can only happen at runtime.

  - **No Checking:** The language doesn’t help you find the error. You get a wrong answer or unexpected behavior.

      - *Example*: In plain JavaScript, `10 / "hello"` doesn't throw an error. It produces the special value `NaN` (Not a Number), which can silently corrupt later calculations.

<br>
<div class="p-4 rounded bg-amber-500 bg-opacity-10">
Catching a bug statically is better than catching it dynamically, and catching it dynamically is better than not catching it at all.
</div>

-----

# Documenting Assumptions

Writing down our assumptions is critical for communicating with other programmers (and our future selves).

  - **Types document assumptions.**

      - `price: number` documents the assumption that `price` will always be a number. TypeScript checks this for us.

  - **`const` documents an assumption.**

      - `const taxRate = 0.16;` documents the assumption that `taxRate` will never be reassigned. TypeScript checks this too.

  - **Specifications (comments) document assumptions.**

      - A comment like `// Assumes quantity is a non-negative integer` documents an assumption that the type system can't check on its own.

Without clear documentation, other developers have to guess, which leads to bugs.

-----

# Hacking vs. Engineering

We've explored how to write code that is more robust than a simple script. This reflects a shift in mindset from hacking to engineering.

<div grid="\~ cols-2 gap-4" class="mt-4">
<div>

### Hacking

Marked by unbridled optimism:

  - Writing lots of code before testing.
  - Keeping details in your head.
  - Assuming bugs won’t happen.

</div>
<div>

### Engineering

Marked by professional pessimism:

  - Writing a little, testing as you go.
  - Documenting assumptions.
  - Defending code against mistakes.

</div>
</div>

-----

## layout: center class: text-center

# The Goals of This Course: The Big Three

Our primary goal is to learn how to produce software that is:

1.  **Safe From Bugs (SFB)**
2.  **Easy To Understand (ETU)**
3.  **Ready For Change (RFC)**

-----

# Why We Use TypeScript in This Course

We use TypeScript for several key reasons that align with our course goals:

  - **Safety:** TypeScript’s static checking catches many common bugs before the code even runs, making it a great language for learning safe engineering practices.

  - **Ubiquity:** TypeScript compiles to JavaScript, the language of the web. It runs everywhere: in browsers, on servers, and in desktop apps.

  - **Modern Tooling:** The JavaScript/TypeScript ecosystem has excellent, free tools for development, including editors (VS Code), testing frameworks, and style checkers.

  - **Learning a New Language:** Good programmers are multilingual. Learning a new language and its paradigms makes you a more flexible and effective developer.

-----

## layout: center

# Summary

The main idea we introduced today is **static checking**.

Here’s how this idea relates to our goals:

  - **Safe From Bugs:** Static checking catches type errors and other bugs before they can cause problems at runtime.

  - **Easy To Understand:** Explicit type declarations (`price: number`) act as clear, enforceable documentation right in the code.

  - **Ready For Change:** When you change a piece of code, the static checker helps you find all the other places in the program that are affected and need to be updated.

<!-- end list -->

```
```